# 通用VOC报表系统安全设计方案

**文档版本**: v1.0.0  
**创建日期**: 2024年1月  
**最后更新**: 2024年1月  
**文档状态**: 设计阶段

---

## 📋 文档概述

本文档详细描述了通用VOC报表系统的安全架构设计，包括多层安全防护体系、认证授权机制、数据保护策略、网络安全措施以及安全监控和应急响应机制。

### 安全设计原则
- **纵深防御**：多层次安全防护，确保单点失效不影响整体安全
- **最小权限**：用户和系统组件只获得必要的最小权限
- **零信任架构**：不信任网络内外的任何实体，验证一切访问
- **数据保护优先**：优先保护敏感数据的机密性、完整性和可用性
- **持续监控**：实时监控安全态势，快速响应安全事件

---

## 🛡️ 安全架构总览

### 1.1 多层安全防护架构

```mermaid
graph TB
    subgraph "网络安全层 Network Security"
        A1[防火墙]
        A2[WAF应用防火墙]
        A3[DDoS防护]
        A4[VPN接入]
    end
    
    subgraph "接入安全层 Access Security"
        B1[API网关]
        B2[负载均衡器]
        B3[SSL/TLS终端]
        B4[CDN安全]
    end
    
    subgraph "应用安全层 Application Security"
        C1[身份认证]
        C2[权限控制]
        C3[会话管理]
        C4[输入验证]
    end
    
    subgraph "数据安全层 Data Security"
        D1[数据加密]
        D2[数据脱敏]
        D3[备份加密]
        D4[密钥管理]
    end
    
    subgraph "基础设施安全层 Infrastructure Security"
        E1[容器安全]
        E2[主机安全]
        E3[网络隔离]
        E4[安全监控]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C4
    
    C1 --> D1
    C2 --> D2
    C3 --> D3
    C4 --> D4
    
    D1 --> E1
    D2 --> E2
    D3 --> E3
    D4 --> E4
```

### 1.2 安全域划分

**网络安全域**：
```json
{
  "securityZones": {
    "dmz": {
      "description": "非军事化区域",
      "components": ["负载均衡器", "Web代理", "API网关"],
      "accessControl": "严格控制，仅允许必要端口",
      "monitoring": "全流量监控和日志记录"
    },
    "webTier": {
      "description": "Web应用层",
      "components": ["前端应用服务器", "静态资源服务"],
      "accessControl": "仅允许来自DMZ的访问",
      "monitoring": "应用层安全监控"
    },
    "appTier": {
      "description": "应用服务层",
      "components": ["业务逻辑服务", "API服务", "认证服务"],
      "accessControl": "服务间认证和授权",
      "monitoring": "业务安全监控"
    },
    "dataTier": {
      "description": "数据服务层",
      "components": ["数据库服务", "缓存服务", "存储服务"],
      "accessControl": "数据库访问控制和审计",
      "monitoring": "数据访问监控"
    },
    "management": {
      "description": "管理服务层",
      "components": ["监控服务", "日志服务", "配置管理"],
      "accessControl": "管理员专用访问",
      "monitoring": "管理操作审计"
    }
  }
}
```

---

## 🔐 身份认证与授权

### 2.1 多因子认证架构

**认证流程设计**：
```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant AuthGateway
    participant AuthService
    participant MFA
    participant TokenService
    
    User->>Frontend: 登录请求
    Frontend->>AuthGateway: 提交凭据
    AuthGateway->>AuthService: 验证用户名密码
    AuthService-->>AuthGateway: 第一因子验证成功
    AuthGateway->>MFA: 触发第二因子
    MFA-->>User: 发送验证码/推送通知
    User->>Frontend: 输入验证码
    Frontend->>AuthGateway: 提交验证码
    AuthGateway->>MFA: 验证第二因子
    MFA-->>AuthGateway: 第二因子验证成功
    AuthGateway->>TokenService: 生成访问令牌
    TokenService-->>AuthGateway: 返回JWT Token
    AuthGateway-->>Frontend: 返回认证结果
    Frontend-->>User: 登录成功
```

**认证配置**：
```json
{
  "authenticationConfig": {
    "primaryFactors": {
      "usernamePassword": {
        "enabled": true,
        "passwordPolicy": {
          "minLength": 12,
          "requireUppercase": true,
          "requireLowercase": true,
          "requireNumbers": true,
          "requireSpecialChars": true,
          "maxAge": 90,
          "historyCount": 12
        },
        "lockoutPolicy": {
          "maxAttempts": 5,
          "lockoutDuration": 1800,
          "progressiveLockout": true
        }
      },
      "ssoIntegration": {
        "enabled": true,
        "providers": ["LDAP", "SAML", "OAuth2"],
        "fallbackToLocal": true
      }
    },
    "secondaryFactors": {
      "sms": {
        "enabled": true,
        "provider": "阿里云短信",
        "codeLength": 6,
        "validityPeriod": 300
      },
      "email": {
        "enabled": true,
        "codeLength": 8,
        "validityPeriod": 600
      },
      "totp": {
        "enabled": true,
        "apps": ["Google Authenticator", "Microsoft Authenticator"],
        "windowSize": 2
      },
      "hardware": {
        "enabled": false,
        "supportedDevices": ["YubiKey", "FIDO2"]
      }
    }
  }
}
```

**认证服务实现**：
```java
@Service
public class MultiFactorAuthenticationService {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Autowired
    private MfaProviderManager mfaProviderManager;
    
    @Autowired
    private JwtTokenProvider tokenProvider;
    
    public AuthenticationResult authenticate(AuthenticationRequest request) {
        try {
            // 1. 第一因子验证（用户名密码）
            User user = validatePrimaryCredentials(request.getUsername(), request.getPassword());
            
            // 2. 检查账户状态
            validateAccountStatus(user);
            
            // 3. 第二因子验证
            if (requiresSecondFactor(user)) {
                return initiateMfaChallenge(user, request);
            }
            
            // 4. 生成访问令牌
            String accessToken = tokenProvider.generateAccessToken(user);
            String refreshToken = tokenProvider.generateRefreshToken(user);
            
            // 5. 记录认证日志
            auditService.logSuccessfulAuthentication(user, request);
            
            return AuthenticationResult.success(user, accessToken, refreshToken);
            
        } catch (AuthenticationException e) {
            auditService.logFailedAuthentication(request.getUsername(), e);
            throw e;
        }
    }
    
    private User validatePrimaryCredentials(String username, String password) {
        User user = userService.findByUsername(username);
        if (user == null) {
            throw new BadCredentialsException("用户名或密码错误");
        }
        
        if (!passwordEncoder.matches(password, user.getPassword())) {
            // 记录失败尝试
            securityEventService.recordFailedLoginAttempt(username);
            throw new BadCredentialsException("用户名或密码错误");
        }
        
        return user;
    }
    
    private AuthenticationResult initiateMfaChallenge(User user, AuthenticationRequest request) {
        // 获取用户的MFA偏好
        MfaPreference preference = user.getMfaPreference();
        MfaProvider provider = mfaProviderManager.getProvider(preference.getType());
        
        // 发送MFA挑战
        String challengeId = provider.sendChallenge(user, preference.getTarget());
        
        // 创建临时认证状态
        String tempToken = tokenProvider.generateTempToken(user.getId(), challengeId);
        
        return AuthenticationResult.mfaRequired(challengeId, tempToken);
    }
    
    public AuthenticationResult completeMfaChallenge(MfaChallengeRequest request) {
        try {
            // 1. 验证临时令牌
            Claims claims = tokenProvider.validateTempToken(request.getTempToken());
            String userId = claims.get("userId", String.class);
            String challengeId = claims.get("challengeId", String.class);
            
            // 2. 验证MFA代码
            User user = userService.findById(userId);
            MfaProvider provider = mfaProviderManager.getProvider(user.getMfaPreference().getType());
            
            if (!provider.verifyChallenge(challengeId, request.getCode())) {
                throw new BadCredentialsException("验证码错误");
            }
            
            // 3. 生成最终访问令牌
            String accessToken = tokenProvider.generateAccessToken(user);
            String refreshToken = tokenProvider.generateRefreshToken(user);
            
            return AuthenticationResult.success(user, accessToken, refreshToken);
            
        } catch (Exception e) {
            auditService.logFailedMfaAttempt(request);
            throw new AuthenticationException("MFA验证失败", e);
        }
    }
}
```

### 2.2 基于角色的访问控制（RBAC）

**RBAC模型设计**：
```json
{
  "rbacModel": {
    "roles": {
      "system_admin": {
        "description": "系统管理员",
        "level": 1,
        "permissions": ["*"],
        "constraints": {
          "ipWhitelist": ["***********/24", "10.0.0.0/8"],
          "timeRestriction": "09:00-18:00",
          "mfaRequired": true
        }
      },
      "tenant_admin": {
        "description": "租户管理员",
        "level": 2,
        "permissions": [
          "tenant:*",
          "user:read,write,delete",
          "config:read,write",
          "report:read,write,export"
        ],
        "constraints": {
          "scopeRestriction": "tenant",
          "mfaRequired": true
        }
      },
      "data_analyst": {
        "description": "数据分析师",
        "level": 3,
        "permissions": [
          "data:read",
          "analysis:read,write",
          "report:read,write,export",
          "config:read"
        ],
        "constraints": {
          "dataAccess": "authorized_industries_only"
        }
      },
      "report_viewer": {
        "description": "报表查看者",
        "level": 4,
        "permissions": [
          "report:read,export"
        ],
        "constraints": {
          "reportAccess": "assigned_reports_only"
        }
      }
    },
    "permissions": {
      "data": ["read", "write", "delete", "export"],
      "analysis": ["read", "write", "execute"],
      "report": ["read", "write", "delete", "export", "share"],
      "config": ["read", "write", "delete"],
      "user": ["read", "write", "delete", "invite"],
      "audit": ["read", "export"],
      "system": ["read", "write", "restart", "backup"]
    }
  }
}
```

**权限控制实现**：
```java
@Component
public class RoleBasedAccessControl {
    
    @Autowired
    private RoleService roleService;
    
    @Autowired
    private PermissionService permissionService;
    
    public boolean hasPermission(UserDetails user, String resource, String action) {
        return hasPermission(user, resource, action, null);
    }
    
    public boolean hasPermission(UserDetails user, String resource, String action, Object targetObject) {
        try {
            // 1. 获取用户角色
            Set<Role> userRoles = roleService.getUserRoles(user.getUsername());
            
            // 2. 检查基本权限
            if (!hasBasicPermission(userRoles, resource, action)) {
                return false;
            }
            
            // 3. 检查约束条件
            if (!checkConstraints(user, userRoles, resource, targetObject)) {
                return false;
            }
            
            // 4. 检查数据范围权限
            if (!checkDataScope(user, userRoles, resource, targetObject)) {
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("权限检查失败: user={}, resource={}, action={}", 
                user.getUsername(), resource, action, e);
            return false;
        }
    }
    
    private boolean hasBasicPermission(Set<Role> roles, String resource, String action) {
        for (Role role : roles) {
            Set<Permission> permissions = role.getPermissions();
            for (Permission permission : permissions) {
                if (permission.matches(resource, action)) {
                    return true;
                }
            }
        }
        return false;
    }
    
    private boolean checkConstraints(UserDetails user, Set<Role> roles, String resource, Object targetObject) {
        for (Role role : roles) {
            RoleConstraints constraints = role.getConstraints();
            
            // IP白名单检查
            if (constraints.hasIpWhitelist()) {
                String userIp = getCurrentUserIp();
                if (!constraints.isIpAllowed(userIp)) {
                    log.warn("IP访问被拒绝: user={}, ip={}", user.getUsername(), userIp);
                    return false;
                }
            }
            
            // 时间限制检查
            if (constraints.hasTimeRestriction()) {
                LocalTime currentTime = LocalTime.now();
                if (!constraints.isTimeAllowed(currentTime)) {
                    log.warn("时间访问被拒绝: user={}, time={}", user.getUsername(), currentTime);
                    return false;
                }
            }
            
            // MFA要求检查
            if (constraints.isMfaRequired()) {
                if (!user.isMfaAuthenticated()) {
                    log.warn("需要MFA认证: user={}", user.getUsername());
                    return false;
                }
            }
        }
        
        return true;
    }
    
    private boolean checkDataScope(UserDetails user, Set<Role> roles, String resource, Object targetObject) {
        if (targetObject == null) {
            return true;
        }
        
        // 租户级别的数据隔离
        if (targetObject instanceof TenantAwareEntity) {
            TenantAwareEntity entity = (TenantAwareEntity) targetObject;
            if (!entity.getTenantId().equals(user.getTenantId())) {
                log.warn("跨租户访问被拒绝: user={}, userTenant={}, targetTenant={}", 
                    user.getUsername(), user.getTenantId(), entity.getTenantId());
                return false;
            }
        }
        
        // 行业级别的数据隔离
        if (targetObject instanceof IndustryAwareEntity) {
            IndustryAwareEntity entity = (IndustryAwareEntity) targetObject;
            Set<String> userIndustries = getUserAuthorizedIndustries(user);
            if (!userIndustries.contains(entity.getIndustryCode())) {
                log.warn("行业数据访问被拒绝: user={}, industry={}", 
                    user.getUsername(), entity.getIndustryCode());
                return false;
            }
        }
        
        return true;
    }
}
```

### 2.3 JWT令牌管理

**令牌结构设计**：
```json
{
  "jwtStructure": {
    "header": {
      "alg": "RS256",
      "typ": "JWT",
      "kid": "key-2024-01"
    },
    "payload": {
      "iss": "voc-system",
      "sub": "user_12345",
      "aud": ["voc-api", "voc-web"],
      "exp": **********,
      "iat": **********,
      "jti": "token_uuid",
      "tenantId": "tenant_001",
      "roles": ["data_analyst", "report_viewer"],
      "permissions": ["data:read", "report:read,write"],
      "mfaAuthenticated": true,
      "sessionId": "session_abc123"
    }
  }
}
```

**令牌管理服务**：
```java
@Service
public class JwtTokenProvider {
    
    @Value("${jwt.secret}")
    private String jwtSecret;
    
    @Value("${jwt.expiration}")
    private int jwtExpiration;
    
    @Value("${jwt.refresh-expiration}")
    private int refreshExpiration;
    
    @Autowired
    private RSAKeyProvider keyProvider;
    
    @Autowired
    private TokenBlacklistService blacklistService;
    
    public String generateAccessToken(User user) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + jwtExpiration * 1000);
        
        Map<String, Object> claims = new HashMap<>();
        claims.put("tenantId", user.getTenantId());
        claims.put("roles", user.getRoles().stream()
            .map(Role::getName)
            .collect(Collectors.toList()));
        claims.put("permissions", getUserPermissions(user));
        claims.put("mfaAuthenticated", user.isMfaAuthenticated());
        claims.put("sessionId", user.getCurrentSessionId());
        
        return Jwts.builder()
            .setClaims(claims)
            .setSubject(user.getId())
            .setIssuedAt(now)
            .setExpiration(expiryDate)
            .setIssuer("voc-system")
            .setId(UUID.randomUUID().toString())
            .signWith(keyProvider.getPrivateKey(), SignatureAlgorithm.RS256)
            .compact();
    }
    
    public String generateRefreshToken(User user) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + refreshExpiration * 1000);
        
        return Jwts.builder()
            .setSubject(user.getId())
            .setIssuedAt(now)
            .setExpiration(expiryDate)
            .setIssuer("voc-system")
            .setId(UUID.randomUUID().toString())
            .claim("type", "refresh")
            .signWith(keyProvider.getPrivateKey(), SignatureAlgorithm.RS256)
            .compact();
    }
    
    public boolean validateToken(String token) {
        try {
            // 检查黑名单
            if (blacklistService.isBlacklisted(token)) {
                return false;
            }
            
            // 验证签名和有效期
            Jwts.parserBuilder()
                .setSigningKey(keyProvider.getPublicKey())
                .build()
                .parseClaimsJws(token);
            
            return true;
        } catch (JwtException | IllegalArgumentException e) {
            log.warn("JWT令牌验证失败: {}", e.getMessage());
            return false;
        }
    }
    
    public void revokeToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            Date expiration = claims.getExpiration();
            
            // 添加到黑名单直到过期
            blacklistService.addToBlacklist(token, expiration);
            
            // 记录令牌撤销日志
            auditService.logTokenRevocation(token, claims.getSubject());
            
        } catch (Exception e) {
            log.error("撤销令牌失败: {}", e.getMessage());
            throw new TokenRevocationException("令牌撤销失败", e);
        }
    }
}
```

---

## 🔒 数据安全保护

### 3.1 数据分类与分级

**数据分类标准**：
```json
{
  "dataClassification": {
    "publicData": {
      "level": 1,
      "description": "公开数据",
      "examples": ["产品介绍", "帮助文档", "公告信息"],
      "protection": {
        "encryption": "不需要",
        "accessControl": "公开访问",
        "auditLog": "基础日志"
      }
    },
    "internalData": {
      "level": 2,
      "description": "内部数据",
      "examples": ["配置信息", "统计数据", "操作日志"],
      "protection": {
        "encryption": "传输加密",
        "accessControl": "内部用户",
        "auditLog": "完整日志"
      }
    },
    "sensitiveData": {
      "level": 3,
      "description": "敏感数据",
      "examples": ["客户反馈", "分析结果", "业务报表"],
      "protection": {
        "encryption": "传输+存储加密",
        "accessControl": "授权用户",
        "auditLog": "详细审计",
        "dataLifecycle": "3年保留期"
      }
    },
    "personalData": {
      "level": 4,
      "description": "个人隐私数据",
      "examples": ["客户姓名", "联系方式", "身份证号"],
      "protection": {
        "encryption": "强加密",
        "accessControl": "严格授权",
        "auditLog": "完整审计",
        "dataLifecycle": "5年保留期",
        "anonymization": "必须脱敏"
      }
    }
  }
}
```

### 3.2 数据加密策略

**加密架构设计**：
```java
@Service
public class DataEncryptionService {
    
    // AES-256-GCM for data encryption
    private static final String DATA_ENCRYPTION_ALGORITHM = "AES/GCM/NoPadding";
    private static final int GCM_IV_LENGTH = 12;
    private static final int GCM_TAG_LENGTH = 16;
    
    // RSA-4096 for key encryption
    private static final String KEY_ENCRYPTION_ALGORITHM = "RSA/OAEP/SHA-256";
    private static final int RSA_KEY_SIZE = 4096;
    
    @Autowired
    private KeyManagementService keyManagementService;
    
    public EncryptedData encryptSensitiveData(String plainText, DataClassification classification) {
        try {
            // 1. 生成数据加密密钥
            SecretKey dataKey = generateDataEncryptionKey();
            
            // 2. 加密数据
            byte[] encryptedData = encryptWithDataKey(plainText.getBytes(StandardCharsets.UTF_8), dataKey);
            
            // 3. 使用主密钥加密数据密钥
            PublicKey masterKey = keyManagementService.getMasterPublicKey();
            byte[] encryptedDataKey = encryptDataKey(dataKey.getEncoded(), masterKey);
            
            // 4. 构建加密结果
            return EncryptedData.builder()
                .encryptedContent(Base64.getEncoder().encodeToString(encryptedData))
                .encryptedKey(Base64.getEncoder().encodeToString(encryptedDataKey))
                .algorithm(DATA_ENCRYPTION_ALGORITHM)
                .keyId(keyManagementService.getCurrentKeyId())
                .classification(classification)
                .createdAt(Instant.now())
                .build();
                
        } catch (Exception e) {
            throw new EncryptionException("数据加密失败", e);
        }
    }
    
    public String decryptSensitiveData(EncryptedData encryptedData) {
        try {
            // 1. 解密数据密钥
            PrivateKey masterKey = keyManagementService.getMasterPrivateKey(encryptedData.getKeyId());
            byte[] encryptedDataKey = Base64.getDecoder().decode(encryptedData.getEncryptedKey());
            byte[] dataKeyBytes = decryptDataKey(encryptedDataKey, masterKey);
            SecretKey dataKey = new SecretKeySpec(dataKeyBytes, "AES");
            
            // 2. 解密数据
            byte[] encryptedContent = Base64.getDecoder().decode(encryptedData.getEncryptedContent());
            byte[] decryptedData = decryptWithDataKey(encryptedContent, dataKey);
            
            return new String(decryptedData, StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            throw new DecryptionException("数据解密失败", e);
        }
    }
    
    private byte[] encryptWithDataKey(byte[] data, SecretKey key) throws Exception {
        Cipher cipher = Cipher.getInstance(DATA_ENCRYPTION_ALGORITHM);
        
        byte[] iv = new byte[GCM_IV_LENGTH];
        SecureRandom.getInstanceStrong().nextBytes(iv);
        
        GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
        cipher.init(Cipher.ENCRYPT_MODE, key, gcmSpec);
        
        byte[] encrypted = cipher.doFinal(data);
        
        // 将IV和加密数据合并
        byte[] result = new byte[GCM_IV_LENGTH + encrypted.length];
        System.arraycopy(iv, 0, result, 0, GCM_IV_LENGTH);
        System.arraycopy(encrypted, 0, result, GCM_IV_LENGTH, encrypted.length);
        
        return result;
    }
    
    private byte[] decryptWithDataKey(byte[] encryptedData, SecretKey key) throws Exception {
        // 分离IV和加密数据
        byte[] iv = new byte[GCM_IV_LENGTH];
        byte[] encrypted = new byte[encryptedData.length - GCM_IV_LENGTH];
        System.arraycopy(encryptedData, 0, iv, 0, GCM_IV_LENGTH);
        System.arraycopy(encryptedData, GCM_IV_LENGTH, encrypted, 0, encrypted.length);
        
        Cipher cipher = Cipher.getInstance(DATA_ENCRYPTION_ALGORITHM);
        GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
        cipher.init(Cipher.DECRYPT_MODE, key, gcmSpec);
        
        return cipher.doFinal(encrypted);
    }
}
```

### 3.3 数据脱敏机制

**脱敏规则引擎**：
```java
@Component
public class DataMaskingEngine {
    
    private final Map<String, MaskingStrategy> maskingStrategies;
    
    public DataMaskingEngine() {
        this.maskingStrategies = new HashMap<>();
        initializeMaskingStrategies();
    }
    
    private void initializeMaskingStrategies() {
        // 手机号脱敏
        maskingStrategies.put("phone", new PhoneMaskingStrategy());
        
        // 身份证号脱敏
        maskingStrategies.put("idCard", new IdCardMaskingStrategy());
        
        // 邮箱脱敏
        maskingStrategies.put("email", new EmailMaskingStrategy());
        
        // 姓名脱敏
        maskingStrategies.put("name", new NameMaskingStrategy());
        
        // 银行卡号脱敏
        maskingStrategies.put("bankCard", new BankCardMaskingStrategy());
    }
    
    public Object maskData(Object data, String fieldName, DataClassification classification) {
        if (data == null) {
            return null;
        }
        
        // 根据数据分类决定是否需要脱敏
        if (!requiresMasking(classification)) {
            return data;
        }
        
        // 获取字段的脱敏策略
        MaskingStrategy strategy = getMaskingStrategy(fieldName);
        if (strategy == null) {
            return data;
        }
        
        return strategy.mask(data);
    }
    
    public static class PhoneMaskingStrategy implements MaskingStrategy {
        private static final Pattern PHONE_PATTERN = Pattern.compile("(\\d{3})(\\d{4})(\\d{4})");
        
        @Override
        public Object mask(Object value) {
            if (!(value instanceof String)) {
                return value;
            }
            
            String phone = (String) value;
            Matcher matcher = PHONE_PATTERN.matcher(phone);
            
            if (matcher.matches()) {
                return matcher.group(1) + "****" + matcher.group(3);
            }
            
            return phone;
        }
    }
    
    public static class IdCardMaskingStrategy implements MaskingStrategy {
        private static final Pattern ID_CARD_PATTERN = Pattern.compile("(\\d{6})(\\d{8})(\\d{4})");
        
        @Override
        public Object mask(Object value) {
            if (!(value instanceof String)) {
                return value;
            }
            
            String idCard = (String) value;
            Matcher matcher = ID_CARD_PATTERN.matcher(idCard);
            
            if (matcher.matches()) {
                return matcher.group(1) + "********" + matcher.group(3);
            }
            
            return idCard;
        }
    }
    
    public static class EmailMaskingStrategy implements MaskingStrategy {
        private static final Pattern EMAIL_PATTERN = Pattern.compile("([^@]+)@(.+)");
        
        @Override
        public Object mask(Object value) {
            if (!(value instanceof String)) {
                return value;
            }
            
            String email = (String) value;
            Matcher matcher = EMAIL_PATTERN.matcher(email);
            
            if (matcher.matches()) {
                String username = matcher.group(1);
                String domain = matcher.group(2);
                
                if (username.length() <= 2) {
                    return "***@" + domain;
                } else {
                    return username.substring(0, 2) + "***@" + domain;
                }
            }
            
            return email;
        }
    }
}
```

### 3.4 密钥管理系统

**密钥管理架构**：
```java
@Service
public class KeyManagementService {
    
    @Autowired
    private HSMConnector hsmConnector;
    
    @Autowired
    private KeyRotationScheduler keyRotationScheduler;
    
    @Autowired
    private KeyAuditService keyAuditService;
    
    public KeyPair generateMasterKeyPair() {
        try {
            // 使用HSM生成主密钥对
            KeyPair masterKeyPair = hsmConnector.generateKeyPair(
                HSMKeyType.RSA_4096,
                "master-key-" + System.currentTimeMillis()
            );
            
            // 记录密钥生成事件
            keyAuditService.logKeyGeneration(masterKeyPair.getPublic());
            
            return masterKeyPair;
            
        } catch (Exception e) {
            throw new KeyManagementException("主密钥生成失败", e);
        }
    }
    
    public void rotateKeys() {
        try {
            // 1. 生成新的密钥对
            KeyPair newKeyPair = generateMasterKeyPair();
            
            // 2. 更新密钥版本
            String newKeyId = updateKeyVersion(newKeyPair);
            
            // 3. 重新加密数据加密密钥
            reencryptDataKeys(newKeyId);
            
            // 4. 删除旧密钥（保留一个版本用于解密历史数据）
            retireOldKeys();
            
            // 5. 记录密钥轮换事件
            keyAuditService.logKeyRotation(newKeyId);
            
        } catch (Exception e) {
            throw new KeyRotationException("密钥轮换失败", e);
        }
    }
    
    @Scheduled(cron = "0 0 2 1 */3 *") // 每季度轮换一次
    public void scheduledKeyRotation() {
        try {
            rotateKeys();
        } catch (Exception e) {
            log.error("定时密钥轮换失败", e);
            alertService.sendKeyRotationFailureAlert(e);
        }
    }
}
```

---

## 🌐 网络安全防护

### 4.1 网络架构安全

**网络分层防护**：
```yaml
# 网络安全配置
networkSecurity:
  firewall:
    rules:
      - name: "允许HTTPS入站"
        direction: inbound
        protocol: tcp
        port: 443
        source: any
        action: allow
      
      - name: "允许HTTP重定向"
        direction: inbound
        protocol: tcp
        port: 80
        source: any
        action: allow
      
      - name: "拒绝直接数据库访问"
        direction: inbound
        protocol: tcp
        port: [3306, 5432, 6379]
        source: external
        action: deny
      
      - name: "内部服务通信"
        direction: inbound
        protocol: tcp
        port: [8080, 8081, 8082]
        source: internal
        action: allow

  waf:
    enabled: true
    rules:
      - type: "SQL注入防护"
        patterns: ["union select", "drop table", "exec xp_"]
        action: block
      
      - type: "XSS防护"
        patterns: ["<script>", "javascript:", "onerror="]
        action: block
      
      - type: "路径遍历防护"
        patterns: ["../", "..\\", "/etc/passwd"]
        action: block
      
      - type: "API限流"
        rateLimits:
          - path: "/api/v1/data-ingestion/*"
            limit: "100/minute"
          - path: "/api/v1/reports/*"
            limit: "200/minute"

  ddos:
    enabled: true
    thresholds:
      requestsPerSecond: 1000
      connectionsPerIp: 100
      bandwidthMbps: 500
    mitigations:
      - type: "rate_limiting"
        action: "drop_excessive_requests"
      - type: "geo_blocking"
        blockedCountries: ["CN", "RU", "KP"]
      - type: "challenge_response"
        challengeTypes: ["captcha", "javascript"]
```

### 4.2 SSL/TLS安全配置

**TLS配置最佳实践**：
```nginx
# Nginx SSL配置
server {
    listen 443 ssl http2;
    server_name api.voc-system.com;
    
    # SSL证书配置
    ssl_certificate /etc/ssl/certs/voc-system.crt;
    ssl_certificate_key /etc/ssl/private/voc-system.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    
    # HSTS配置
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    
    # 其他安全头
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'" always;
    
    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    ssl_trusted_certificate /etc/ssl/certs/ca-chain.crt;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;
    
    location / {
        proxy_pass http://backend_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

---

## 🔍 安全监控与审计

### 5.1 安全事件监控

**监控指标体系**：
```json
{
  "securityMetrics": {
    "authentication": {
      "loginAttempts": "登录尝试次数",
      "failedLogins": "登录失败次数",
      "accountLockouts": "账户锁定次数",
      "mfaFailures": "MFA验证失败次数",
      "suspiciousLocations": "异常登录地点",
      "unusualTimes": "异常登录时间"
    },
    "authorization": {
      "privilegeEscalations": "权限提升尝试",
      "unauthorizedAccess": "未授权访问尝试",
      "dataAccessViolations": "数据访问违规",
      "adminOperations": "管理员操作次数"
    },
    "dataProtection": {
      "encryptionFailures": "加密失败次数",
      "decryptionAttempts": "解密尝试次数",
      "keyAccess": "密钥访问次数",
      "dataExports": "数据导出次数",
      "backupAccess": "备份访问次数"
    },
    "network": {
      "firewallBlocks": "防火墙阻断次数",
      "wafBlocks": "WAF阻断次数",
      "ddosAttacks": "DDoS攻击次数",
      "portScans": "端口扫描次数",
      "maliciousIps": "恶意IP访问次数"
    }
  }
}
```

**实时监控实现**：
```java
@Component
public class SecurityEventMonitor {
    
    @Autowired
    private AlertService alertService;
    
    @Autowired
    private ThreatIntelligenceService threatIntelligenceService;
    
    @EventListener
    public void handleFailedLoginEvent(FailedLoginEvent event) {
        // 检查是否为暴力破解攻击
        if (isbroughteForceAttack(event)) {
            // 立即阻断IP
            firewallService.blockIp(event.getSourceIp(), Duration.ofHours(24));
            
            // 发送安全告警
            alertService.sendSecurityAlert(
                "暴力破解攻击检测",
                String.format("IP %s 尝试暴力破解用户 %s", 
                    event.getSourceIp(), event.getUsername())
            );
        }
        
        // 记录到安全日志
        securityAuditService.logSecurityEvent(SecurityEventType.FAILED_LOGIN, event);
    }
    
    @EventListener
    public void handleUnauthorizedAccessEvent(UnauthorizedAccessEvent event) {
        // 检查是否为权限提升攻击
        if (isPrivilegeEscalationAttempt(event)) {
            // 临时冻结用户账户
            userService.suspendUser(event.getUserId(), Duration.ofMinutes(30));
            
            // 发送高优先级告警
            alertService.sendCriticalAlert(
                "权限提升攻击检测",
                String.format("用户 %s 尝试访问未授权资源 %s", 
                    event.getUsername(), event.getResource())
            );
        }
    }
    
    @EventListener
    public void handleDataExportEvent(DataExportEvent event) {
        // 检查大量数据导出
        if (isLargeDataExport(event)) {
            // 记录详细审计信息
            auditService.logDataExport(event);
            
            // 通知数据保护官
            notificationService.notifyDataProtectionOfficer(event);
        }
    }
    
    private boolean isBruteForceAttack(FailedLoginEvent event) {
        // 检查5分钟内同一IP的失败登录次数
        long recentFailures = securityEventRepository.countFailedLoginsByIp(
            event.getSourceIp(), 
            Instant.now().minus(5, ChronoUnit.MINUTES)
        );
        
        return recentFailures >= 10;
    }
}
```

### 5.2 安全审计系统

**审计日志标准**：
```json
{
  "auditLogFormat": {
    "timestamp": "2024-01-20T10:30:00.123Z",
    "eventId": "audit_12345",
    "eventType": "DATA_ACCESS",
    "severity": "INFO|WARN|ERROR|CRITICAL",
    "source": {
      "service": "voc-api",
      "component": "data-service",
      "version": "1.0.0"
    },
    "actor": {
      "userId": "user_12345",
      "username": "analyst_001",
      "roles": ["data_analyst"],
      "sessionId": "session_abc123",
      "sourceIp": "*************",
      "userAgent": "Mozilla/5.0..."
    },
    "target": {
      "resourceType": "voc_record",
      "resourceId": "record_67890",
      "dataClassification": "sensitive",
      "tenantId": "tenant_001"
    },
    "action": {
      "operation": "READ",
      "description": "查询VOC明细数据",
      "parameters": {
        "filter": "sentiment=negative",
        "limit": 100
      }
    },
    "result": {
      "status": "SUCCESS|FAILURE",
      "statusCode": 200,
      "recordCount": 85,
      "errorMessage": null
    },
    "context": {
      "requestId": "req_12345",
      "traceId": "trace_67890",
      "industryCode": "automotive",
      "regulations": ["GDPR", "PIPL"]
    }
  }
}
```

**审计服务实现**：
```java
@Service
public class SecurityAuditService {
    
    @Autowired
    private AuditLogRepository auditLogRepository;
    
    @Autowired
    private ComplianceService complianceService;
    
    public void logDataAccess(String userId, String resourceType, String resourceId, 
                             String operation, Map<String, Object> parameters) {
        
        AuditLogEntry entry = AuditLogEntry.builder()
            .eventId(generateEventId())
            .timestamp(Instant.now())
            .eventType(AuditEventType.DATA_ACCESS)
            .severity(AuditSeverity.INFO)
            .userId(userId)
            .resourceType(resourceType)
            .resourceId(resourceId)
            .operation(operation)
            .parameters(parameters)
            .sourceIp(getCurrentUserIp())
            .userAgent(getCurrentUserAgent())
            .requestId(getCurrentRequestId())
            .build();
        
        // 异步写入审计日志
        CompletableFuture.runAsync(() -> {
            auditLogRepository.save(entry);
            
            // 检查合规性要求
            complianceService.checkComplianceRequirements(entry);
        });
    }
    
    public void logSecurityEvent(SecurityEventType eventType, Object eventData) {
        AuditLogEntry entry = AuditLogEntry.builder()
            .eventId(generateEventId())
            .timestamp(Instant.now())
            .eventType(AuditEventType.SECURITY_EVENT)
            .severity(determineSeverity(eventType))
            .securityEventType(eventType)
            .eventData(objectMapper.writeValueAsString(eventData))
            .sourceIp(extractSourceIp(eventData))
            .build();
        
        // 立即写入安全日志
        auditLogRepository.save(entry);
        
        // 触发安全分析
        securityAnalysisService.analyzeSecurityEvent(entry);
    }
    
    @Scheduled(fixedRate = 300000) // 每5分钟执行
    public void generateComplianceReport() {
        try {
            // 生成合规报告
            ComplianceReport report = complianceService.generateReport(
                Instant.now().minus(5, ChronoUnit.MINUTES),
                Instant.now()
            );
            
            // 检查合规性违规
            if (report.hasViolations()) {
                alertService.sendComplianceAlert(report);
            }
            
        } catch (Exception e) {
            log.error("生成合规报告失败", e);
        }
    }
}
```

---

## 🚨 安全应急响应

### 6.1 安全事件响应流程

**事件响应矩阵**：
```json
{
  "incidentResponseMatrix": {
    "dataBreachSuspected": {
      "severity": "CRITICAL",
      "responseTime": "15分钟",
      "actions": [
        "立即隔离受影响系统",
        "通知安全团队和法务团队",
        "开始数据泄露调查",
        "准备监管机构通报",
        "启动客户通知流程"
      ],
      "stakeholders": ["CISO", "DPO", "法务总监", "CEO"]
    },
    "systemCompromise": {
      "severity": "HIGH",
      "responseTime": "30分钟",
      "actions": [
        "隔离受感染系统",
        "分析攻击向量",
        "检查横向移动",
        "重置相关账户密码",
        "加强监控"
      ],
      "stakeholders": ["安全团队", "运维团队", "业务负责人"]
    },
    "denialOfService": {
      "severity": "MEDIUM",
      "responseTime": "1小时",
      "actions": [
        "启动DDoS缓解措施",
        "分析攻击模式",
        "调整防护策略",
        "监控服务恢复",
        "通知相关用户"
      ],
      "stakeholders": ["运维团队", "网络团队"]
    }
  }
}
```

### 6.2 安全备份与恢复

**备份策略**：
```yaml
backupStrategy:
  databases:
    frequency: "daily"
    retention: "90 days"
    encryption: "AES-256"
    offsite: true
    testing: "weekly"
  
  configurations:
    frequency: "hourly"
    retention: "30 days"
    versioning: true
  
  logs:
    frequency: "real-time"
    retention: "365 days"
    compression: true
  
  secrets:
    frequency: "daily"
    retention: "indefinite"
    encryption: "envelope encryption"
    access: "break-glass only"

recoveryProcedures:
  rto: "4 hours"  # 恢复时间目标
  rpo: "1 hour"   # 恢复点目标
  
  priorities:
    - "身份认证系统"
    - "核心API服务"
    - "数据库服务"
    - "报表系统"
    - "监控系统"
```

---

## 📋 安全合规要求

### 7.1 法规合规矩阵

**合规要求映射**：
```json
{
  "complianceMatrix": {
    "GDPR": {
      "scope": "欧盟用户数据",
      "requirements": [
        "数据处理合法性基础",
        "用户同意机制",
        "数据可携带权",
        "删除权实施",
        "数据保护影响评估",
        "72小时违规通知"
      ],
      "implementation": {
        "dataMinimization": "仅收集必要数据",
        "purposeLimitation": "明确数据使用目的",
        "storageLimit": "设定数据保留期限",
        "accountability": "实施问责制原则"
      }
    },
    "PIPL": {
      "scope": "中国境内个人信息",
      "requirements": [
        "个人信息处理告知义务",
        "个人信息处理同意机制", 
        "个人信息跨境传输限制",
        "个人信息保护影响评估",
        "个人信息处理者义务"
      ],
      "implementation": {
        "dataLocalization": "敏感个人信息境内存储",
        "consentManagement": "明确同意机制",
        "rightsManagement": "个人权利行使机制"
      }
    },
    "ISO27001": {
      "scope": "信息安全管理",
      "requirements": [
        "信息安全政策",
        "风险管理",
        "资产管理",
        "访问控制",
        "密码管理",
        "事件响应"
      ]
    }
  }
}
```

### 7.2 安全评估与认证

**安全评估框架**：
```java
@Service
public class SecurityAssessmentService {
    
    @Scheduled(cron = "0 0 0 1 */3 *") // 每季度评估
    public void conductSecurityAssessment() {
        SecurityAssessmentReport report = SecurityAssessmentReport.builder()
            .assessmentId(UUID.randomUUID().toString())
            .assessmentDate(LocalDate.now())
            .assessmentType(AssessmentType.QUARTERLY)
            .build();
        
        // 1. 漏洞扫描
        VulnerabilityAssessment vulnAssessment = conductVulnerabilityAssessment();
        report.setVulnerabilityAssessment(vulnAssessment);
        
        // 2. 渗透测试
        PenetrationTestResults penTestResults = conductPenetrationTest();
        report.setPenetrationTestResults(penTestResults);
        
        // 3. 配置审计
        ConfigurationAudit configAudit = auditSecurityConfigurations();
        report.setConfigurationAudit(configAudit);
        
        // 4. 合规性检查
        ComplianceCheck complianceCheck = performComplianceCheck();
        report.setComplianceCheck(complianceCheck);
        
        // 5. 生成改进建议
        List<SecurityRecommendation> recommendations = generateRecommendations(report);
        report.setRecommendations(recommendations);
        
        // 6. 保存评估报告
        assessmentRepository.save(report);
        
        // 7. 通知相关人员
        notifyStakeholders(report);
    }
    
    private VulnerabilityAssessment conductVulnerabilityAssessment() {
        // 使用自动化工具进行漏洞扫描
        return vulnerabilityScannerService.scan();
    }
    
    private PenetrationTestResults conductPenetrationTest() {
        // 执行渗透测试（可能委托第三方）
        return penetrationTestService.execute();
    }
}
```

---

**文档维护**: 安全架构师、信息安全团队  
**审核**: CISO、合规官、法务团队  
**下次更新**: 安全策略变更时及时更新 