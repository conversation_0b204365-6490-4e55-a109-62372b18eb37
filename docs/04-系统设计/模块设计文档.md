# 通用VOC报表系统模块设计文档

**文档版本**: v1.0.0  
**创建日期**: 2024年1月  
**最后更新**: 2024年1月  
**文档状态**: 设计阶段

---

## 📋 文档概述

本文档详细描述了通用VOC报表系统各核心功能模块的设计，包括模块职责、接口定义、内部实现以及模块间的交互关系。

### 模块设计原则
- **单一职责**：每个模块只负责一个明确的业务域
- **高内聚低耦合**：模块内部高度聚合，模块间松散耦合
- **接口标准化**：统一的接口设计和数据格式
- **可插拔设计**：支持模块的动态加载和卸载
- **配置驱动**：通过配置实现模块行为控制

---

## 🔌 数据接入模块（Data Ingestion Module）

### 1.1 模块概述

**功能职责**：
- 多格式数据源适配和接入
- 实时和批量数据处理
- 数据质量检查和清洗
- 字段映射和标准化转换

**技术架构**：
```mermaid
graph TD
    subgraph "数据接入模块"
        A1[数据适配器] --> B1[数据验证器]
        B1 --> C1[数据清洗器]
        C1 --> D1[字段映射器]
        D1 --> E1[数据标准化器]
        E1 --> F1[数据路由器]
    end
    
    subgraph "外部数据源"
        G1[CSV文件]
        G2[JSON数据]
        G3[数据库]
        G4[API接口]
        G5[Excel文件]
    end
    
    G1 --> A1
    G2 --> A1
    G3 --> A1
    G4 --> A1
    G5 --> A1
    
    F1 --> H1[消息队列]
```

### 1.2 核心组件设计

#### 1.2.1 数据适配器（Data Adapter）

**组件职责**：适配不同格式的数据源，提供统一的数据读取接口

**接口定义**：
```java
public interface DataAdapter {
    /**
     * 检查数据源连接性
     */
    boolean checkConnection(DataSourceConfig config);
    
    /**
     * 读取数据
     */
    DataStream<RawDataRecord> readData(DataSourceConfig config, ReadOptions options);
    
    /**
     * 获取数据源元信息
     */
    DataSourceMetadata getMetadata(DataSourceConfig config);
    
    /**
     * 估算数据量
     */
    long estimateDataSize(DataSourceConfig config);
}
```

**具体实现**：
```java
// CSV适配器
@Component
public class CsvDataAdapter implements DataAdapter {
    
    @Override
    public DataStream<RawDataRecord> readData(DataSourceConfig config, ReadOptions options) {
        CsvParserSettings settings = new CsvParserSettings();
        settings.setHeaderExtractionEnabled(true);
        settings.setDelimiterDetectionEnabled(true);
        
        return DataStream.fromSource(new CsvFileSource(config.getFilePath(), settings))
            .map(row -> convertToRawDataRecord(row))
            .filter(record -> record != null);
    }
    
    private RawDataRecord convertToRawDataRecord(String[] row) {
        return RawDataRecord.builder()
            .id(generateId())
            .timestamp(Instant.now())
            .data(Arrays.asList(row))
            .source("CSV")
            .build();
    }
}

// JSON适配器
@Component  
public class JsonDataAdapter implements DataAdapter {
    
    @Override
    public DataStream<RawDataRecord> readData(DataSourceConfig config, ReadOptions options) {
        ObjectMapper mapper = new ObjectMapper();
        
        return DataStream.fromSource(new JsonFileSource(config.getFilePath()))
            .map(json -> {
                try {
                    JsonNode node = mapper.readTree(json);
                    return convertToRawDataRecord(node);
                } catch (Exception e) {
                    log.error("Failed to parse JSON: {}", json, e);
                    return null;
                }
            })
            .filter(Objects::nonNull);
    }
}
```

#### 1.2.2 数据验证器（Data Validator）

**组件职责**：对原始数据进行完整性、准确性、格式等验证

**验证规则配置**：
```json
{
  "validationRules": {
    "completenessRules": {
      "requiredFields": ["user_id", "feedback_text", "timestamp"],
      "minFieldCount": 5,
      "maxFieldCount": 50
    },
    "accuracyRules": {
      "timestampFormat": "yyyy-MM-dd HH:mm:ss",
      "textLengthRange": {"min": 10, "max": 5000},
      "userIdPattern": "^[A-Za-z0-9_-]+$"
    },
    "consistencyRules": {
      "channelTypes": ["store", "app", "hotline", "social", "email"],
      "userTypes": ["new", "regular", "vip"],
      "dataEncoding": "UTF-8"
    }
  }
}
```

**实现代码**：
```java
@Component
public class DataValidator {
    
    @Autowired
    private ValidationRuleEngine ruleEngine;
    
    public ValidationResult validate(RawDataRecord record, IndustryConfig config) {
        ValidationResult result = new ValidationResult();
        
        // 完整性验证
        result.merge(validateCompleteness(record, config));
        
        // 准确性验证  
        result.merge(validateAccuracy(record, config));
        
        // 一致性验证
        result.merge(validateConsistency(record, config));
        
        // 业务规则验证
        result.merge(validateBusinessRules(record, config));
        
        return result;
    }
    
    private ValidationResult validateCompleteness(RawDataRecord record, IndustryConfig config) {
        ValidationResult result = new ValidationResult();
        
        List<String> requiredFields = config.getValidationRules().getRequiredFields();
        for (String field : requiredFields) {
            if (!record.hasField(field) || record.getField(field) == null) {
                result.addError(ValidationError.missingRequiredField(field));
            }
        }
        
        return result;
    }
}
```

#### 1.2.3 数据清洗器（Data Cleaner）

**组件职责**：清理和修复数据质量问题

**清洗策略**：
```json
{
  "cleaningStrategies": {
    "textCleaning": {
      "removeHtmlTags": true,
      "removeExtraSpaces": true,
      "normalizeUnicode": true,
      "filterEmoji": false,
      "languageDetection": true
    },
    "dataStandardization": {
      "timestampNormalization": "UTC",
      "textCaseNormalization": "lowercase",
      "phoneNumberFormat": "+86-xxx-xxxx-xxxx",
      "emailValidation": true
    },
    "outlierHandling": {
      "textLengthThreshold": 10000,
      "specialCharacterRatio": 0.3,
      "repeatedContentDetection": true
    },
    "missingDataHandling": {
      "strategy": "interpolation",
      "defaultValues": {
        "channel_type": "unknown",
        "user_type": "regular"
      }
    }
  }
}
```

**实现代码**：
```java
@Component
public class DataCleaner {
    
    public CleanedDataRecord clean(RawDataRecord record, CleaningConfig config) {
        CleanedDataRecord cleaned = new CleanedDataRecord(record);
        
        // 文本清洗
        cleaned.setText(cleanText(record.getText(), config.getTextCleaning()));
        
        // 数据标准化
        cleaned = standardizeData(cleaned, config.getStandardization());
        
        // 异常值处理
        cleaned = handleOutliers(cleaned, config.getOutlierHandling());
        
        // 缺失值处理
        cleaned = handleMissingData(cleaned, config.getMissingDataHandling());
        
        return cleaned;
    }
    
    private String cleanText(String text, TextCleaningConfig config) {
        if (text == null) return "";
        
        String cleaned = text;
        
        // 移除HTML标签
        if (config.isRemoveHtmlTags()) {
            cleaned = cleaned.replaceAll("<[^>]+>", "");
        }
        
        // 移除多余空格
        if (config.isRemoveExtraSpaces()) {
            cleaned = cleaned.replaceAll("\\s+", " ").trim();
        }
        
        // Unicode规范化
        if (config.isNormalizeUnicode()) {
            cleaned = Normalizer.normalize(cleaned, Normalizer.Form.NFC);
        }
        
        return cleaned;
    }
}
```

#### 1.2.4 字段映射器（Field Mapper）

**组件职责**：将不同数据源的字段映射到标准化字段

**映射配置**：
```json
{
  "fieldMappings": {
    "automotive": {
      "sourceFields": {
        "客户姓名": "user_name",
        "联系电话": "phone_number", 
        "车型": "vehicle_model",
        "反馈内容": "feedback_text",
        "反馈时间": "feedback_time",
        "门店名称": "store_name"
      },
      "transformations": {
        "feedback_time": {
          "type": "datetime",
          "format": "yyyy-MM-dd HH:mm:ss",
          "timezone": "Asia/Shanghai"
        },
        "phone_number": {
          "type": "phone",
          "format": "+86-xxx-xxxx-xxxx",
          "validation": "^1[3-9]\\d{9}$"
        }
      }
    },
    "starbucks": {
      "sourceFields": {
        "顾客ID": "customer_id",
        "门店编号": "store_id",
        "产品名称": "product_name",
        "评价内容": "feedback_text",
        "评价时间": "feedback_time",
        "评分": "rating"
      }
    }
  }
}
```

### 1.3 模块接口设计

**RESTful API接口**：
```yaml
# 数据接入API规范
/api/v1/data-ingestion:
  post:
    summary: 提交数据接入任务
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              industryCode:
                type: string
                description: 行业代码
              dataSourceConfig:
                type: object
                description: 数据源配置
              processingOptions:
                type: object
                description: 处理选项
    responses:
      200:
        description: 接入任务已创建
        content:
          application/json:
            schema:
              type: object
              properties:
                taskId:
                  type: string
                status:
                  type: string
                  enum: [pending, processing, completed, failed]

/api/v1/data-ingestion/{taskId}/status:
  get:
    summary: 查询任务状态
    parameters:
      - name: taskId
        in: path
        required: true
        schema:
          type: string
    responses:
      200:
        description: 任务状态信息
```

---

## 🧠 智能分析模块（Intelligent Analysis Module）

### 2.1 模块概述

**功能职责**：
- 大模型AI分析接口集成
- 多维度并行分析处理
- 分析结果质量评估
- 结果缓存和优化

**核心分析维度**：
- **情感分析**：正面、负面、中性情感识别
- **意图识别**：咨询、投诉、建议、表扬等意图分类
- **主题分类**：产品、服务、价格等主题归类
- **紧急程度**：高、中、低紧急程度评估

### 2.2 核心组件设计

#### 2.2.1 AI模型管理器（AI Model Manager）

**组件职责**：管理多个AI模型，实现模型选择和负载均衡

**模型配置**：
```json
{
  "aiModels": {
    "sentimentAnalysis": {
      "models": [
        {
          "name": "openai-gpt4",
          "endpoint": "https://api.openai.com/v1/chat/completions",
          "apiKey": "${OPENAI_API_KEY}",
          "maxTokens": 4096,
          "temperature": 0.1,
          "priority": 1
        },
        {
          "name": "claude-3",
          "endpoint": "https://api.anthropic.com/v1/messages",
          "apiKey": "${CLAUDE_API_KEY}",
          "maxTokens": 4096,
          "temperature": 0.1,
          "priority": 2
        }
      ],
      "loadBalancing": "round_robin",
      "fallbackStrategy": "next_priority",
      "timeout": 30000
    }
  }
}
```

**实现代码**：
```java
@Component
public class AIModelManager {
    
    @Autowired
    private List<AIModelClient> modelClients;
    
    @Autowired
    private LoadBalancer loadBalancer;
    
    public AnalysisResult analyze(AnalysisRequest request) {
        AIModelClient selectedModel = selectModel(request.getAnalysisType());
        
        try {
            return selectedModel.analyze(request);
        } catch (ModelException e) {
            // 故障转移到备用模型
            AIModelClient fallbackModel = getFallbackModel(selectedModel);
            if (fallbackModel != null) {
                return fallbackModel.analyze(request);
            }
            throw new AnalysisException("所有模型都不可用", e);
        }
    }
    
    private AIModelClient selectModel(AnalysisType type) {
        List<AIModelClient> availableModels = getAvailableModels(type);
        return loadBalancer.select(availableModels);
    }
}
```

#### 2.2.2 情感分析引擎（Sentiment Analysis Engine）

**组件职责**：分析文本的情感倾向和情感强度

**分析提示词模板**：
```json
{
  "sentimentPrompts": {
    "automotive": {
      "systemPrompt": "你是一个专业的汽车行业情感分析专家。请分析以下客户反馈的情感倾向。",
      "userPrompt": "请分析以下汽车客户反馈的情感：\n\n反馈内容：{feedback_text}\n\n请从以下维度分析：\n1. 整体情感倾向（positive/negative/neutral）\n2. 情感强度（1-10分）\n3. 具体情感类型（满意/不满/愤怒/失望/期待等）\n4. 置信度评分（0-1）\n\n请以JSON格式返回结果。",
      "outputFormat": {
        "sentiment": "positive|negative|neutral",
        "intensity": "1-10",
        "emotion_type": "string",
        "confidence": "0-1",
        "reasoning": "string"
      }
    },
    "starbucks": {
      "systemPrompt": "你是一个专业的餐饮服务行业情感分析专家。",
      "userPrompt": "请分析以下星巴克客户反馈的情感：\n\n{feedback_text}"
    }
  }
}
```

**实现代码**：
```java
@Service
public class SentimentAnalysisEngine {
    
    @Autowired
    private AIModelManager aiModelManager;
    
    @Autowired
    private PromptTemplateManager promptManager;
    
    public SentimentResult analyzeSentiment(String text, IndustryConfig config) {
        // 构建分析请求
        AnalysisRequest request = buildSentimentRequest(text, config);
        
        // 调用AI模型分析
        AnalysisResult result = aiModelManager.analyze(request);
        
        // 解析和验证结果
        SentimentResult sentimentResult = parseSentimentResult(result);
        
        // 置信度检查
        if (sentimentResult.getConfidence() < config.getSentimentThreshold()) {
            sentimentResult.setNeedsReview(true);
        }
        
        return sentimentResult;
    }
    
    private AnalysisRequest buildSentimentRequest(String text, IndustryConfig config) {
        PromptTemplate template = promptManager.getTemplate(
            config.getIndustryCode(), "sentiment"
        );
        
        String prompt = template.render(Map.of("feedback_text", text));
        
        return AnalysisRequest.builder()
            .type(AnalysisType.SENTIMENT)
            .prompt(prompt)
            .maxTokens(1000)
            .temperature(0.1)
            .build();
    }
}
```

#### 2.2.3 意图识别引擎（Intent Recognition Engine）

**组件职责**：识别客户反馈的意图类型

**意图分类体系**：
```json
{
  "intentClassification": {
    "automotive": {
      "intents": [
        {
          "name": "complaint",
          "description": "投诉类反馈",
          "keywords": ["投诉", "问题", "故障", "不满", "质量"],
          "patterns": [".*质量问题.*", ".*故障.*", ".*不满意.*"],
          "priority": "high"
        },
        {
          "name": "consultation", 
          "description": "咨询类反馈",
          "keywords": ["咨询", "请问", "怎么", "如何", "是否"],
          "patterns": [".*怎么.*", ".*如何.*", ".*咨询.*"],
          "priority": "medium"
        },
        {
          "name": "suggestion",
          "description": "建议类反馈", 
          "keywords": ["建议", "希望", "改进", "优化", "提升"],
          "patterns": [".*建议.*", ".*希望.*", ".*改进.*"],
          "priority": "low"
        }
      ]
    }
  }
}
```

#### 2.2.4 主题分类引擎（Topic Classification Engine）

**组件职责**：对反馈内容进行主题分类

**主题分类配置**：
```json
{
  "topicClassification": {
    "automotive": {
      "topics": {
        "product_quality": {
          "name": "产品质量",
          "subtopics": ["发动机", "变速箱", "底盘", "电子系统"],
          "keywords": ["质量", "故障", "问题", "异响", "漏油"],
          "weight": 1.5
        },
        "service_quality": {
          "name": "服务质量", 
          "subtopics": ["保养", "维修", "态度", "效率"],
          "keywords": ["服务", "态度", "维修", "保养", "技师"],
          "weight": 1.2
        },
        "price_concern": {
          "name": "价格相关",
          "subtopics": ["购车价格", "保养费用", "维修费用"],
          "keywords": ["价格", "费用", "贵", "便宜", "优惠"],
          "weight": 1.0
        }
      }
    }
  }
}
```

### 2.3 批量分析优化

**批量处理策略**：
```java
@Service
public class BatchAnalysisService {
    
    @Autowired
    private AIModelManager aiModelManager;
    
    @Value("${analysis.batch.size:100}")
    private int batchSize;
    
    @Value("${analysis.batch.timeout:300}")
    private int batchTimeoutSeconds;
    
    public List<AnalysisResult> batchAnalyze(List<String> texts, IndustryConfig config) {
        // 分批处理
        List<List<String>> batches = Lists.partition(texts, batchSize);
        
        // 并行处理批次
        return batches.parallelStream()
            .map(batch -> processBatch(batch, config))
            .flatMap(List::stream)
            .collect(Collectors.toList());
    }
    
    private List<AnalysisResult> processBatch(List<String> batch, IndustryConfig config) {
        // 构建批量请求
        BatchAnalysisRequest request = BatchAnalysisRequest.builder()
            .texts(batch)
            .industryConfig(config)
            .timeout(Duration.ofSeconds(batchTimeoutSeconds))
            .build();
        
        // 调用AI模型批量分析
        return aiModelManager.batchAnalyze(request);
    }
}
```

---

## ⚙️ 配置管理模块（Configuration Management Module）

### 3.1 模块概述

**功能职责**：
- 集中化配置管理和分发
- 动态配置热更新
- 行业特定配置模板
- 配置版本控制和回滚

### 3.2 核心组件设计

#### 3.2.1 配置中心（Configuration Center）

**配置层次结构**：
```json
{
  "configurationHierarchy": {
    "global": {
      "description": "全局通用配置",
      "scope": "all_tenants",
      "priority": 1
    },
    "industry": {
      "description": "行业特定配置", 
      "scope": "industry_level",
      "priority": 2,
      "inheritsFrom": "global"
    },
    "tenant": {
      "description": "租户自定义配置",
      "scope": "tenant_level", 
      "priority": 3,
      "inheritsFrom": "industry"
    },
    "environment": {
      "description": "环境特定配置",
      "scope": "env_level",
      "priority": 4,
      "inheritsFrom": "tenant"
    }
  }
}
```

**配置管理API**：
```java
@RestController
@RequestMapping("/api/v1/configuration")
public class ConfigurationController {
    
    @Autowired
    private ConfigurationService configService;
    
    @GetMapping("/industry/{industryCode}")
    public ResponseEntity<IndustryConfig> getIndustryConfig(
            @PathVariable String industryCode,
            @RequestParam(required = false) String version) {
        
        IndustryConfig config = configService.getIndustryConfig(industryCode, version);
        return ResponseEntity.ok(config);
    }
    
    @PutMapping("/industry/{industryCode}")
    public ResponseEntity<Void> updateIndustryConfig(
            @PathVariable String industryCode,
            @RequestBody IndustryConfig config) {
        
        configService.updateIndustryConfig(industryCode, config);
        return ResponseEntity.ok().build();
    }
    
    @PostMapping("/industry/{industryCode}/validate")
    public ResponseEntity<ValidationResult> validateConfig(
            @PathVariable String industryCode,
            @RequestBody IndustryConfig config) {
        
        ValidationResult result = configService.validateConfig(config);
        return ResponseEntity.ok(result);
    }
}
```

#### 3.2.2 规则引擎（Rules Engine）

**规则定义格式**：
```json
{
  "businessRules": {
    "priorityRules": [
      {
        "name": "high_priority_complaint",
        "description": "高优先级投诉规则",
        "condition": {
          "and": [
            {"field": "sentiment", "operator": "equals", "value": "negative"},
            {"field": "intent", "operator": "equals", "value": "complaint"},
            {"field": "confidence", "operator": "greater_than", "value": 0.8}
          ]
        },
        "actions": [
          {"type": "set_priority", "value": "high"},
          {"type": "send_notification", "target": "complaint_manager"},
          {"type": "create_ticket", "urgency": "immediate"}
        ],
        "enabled": true,
        "weight": 10
      }
    ],
    "routingRules": [
      {
        "name": "route_to_quality_dept",
        "condition": {
          "or": [
            {"field": "topic", "operator": "contains", "value": "quality"},
            {"field": "topic", "operator": "contains", "value": "defect"}
          ]
        },
        "actions": [
          {"type": "route_to_department", "department": "quality"},
          {"type": "assign_to_role", "role": "quality_engineer"}
        ]
      }
    ]
  }
}
```

**规则执行引擎**：
```java
@Service
public class RulesEngineService {
    
    @Autowired
    private DroolsService droolsService;
    
    public RuleExecutionResult executeRules(VOCRecord record, IndustryConfig config) {
        // 创建规则会话
        KieSession kieSession = droolsService.createSession(config.getIndustryCode());
        
        // 插入事实对象
        kieSession.insert(record);
        kieSession.insert(config);
        
        // 执行规则
        RuleExecutionResult result = new RuleExecutionResult();
        kieSession.setGlobal("result", result);
        
        int firedRules = kieSession.fireAllRules();
        
        // 清理会话
        kieSession.dispose();
        
        result.setFiredRulesCount(firedRules);
        return result;
    }
}
```

#### 3.2.3 字典管理器（Dictionary Manager）

**多维度字典体系**：
```java
@Entity
@Table(name = "industry_dictionaries")
public class IndustryDictionary {
    
    @Id
    private String id;
    
    @Column(name = "industry_code")
    private String industryCode;
    
    @Column(name = "dictionary_type")
    @Enumerated(EnumType.STRING)
    private DictionaryType dictionaryType;
    
    @Column(name = "category")
    private String category;
    
    @Column(name = "term")
    private String term;
    
    @Column(name = "weight")
    private Double weight;
    
    @Column(name = "synonyms")
    @Convert(converter = StringListConverter.class)
    private List<String> synonyms;
    
    @Column(name = "context")
    private String context;
    
    // getters and setters
}

public enum DictionaryType {
    SENTIMENT,      // 情感词典
    INTENT,         // 意图词典  
    TOPIC,          // 主题词典
    INDUSTRY_TERM,  // 行业术语
    STOP_WORD       // 停用词
}
```

**字典管理服务**：
```java
@Service
public class DictionaryManagerService {
    
    @Autowired
    private DictionaryRepository dictionaryRepository;
    
    public void updateDictionary(String industryCode, DictionaryType type, 
                               List<DictionaryEntry> entries) {
        // 备份现有字典
        backupDictionary(industryCode, type);
        
        // 删除旧条目
        dictionaryRepository.deleteByIndustryCodeAndType(industryCode, type);
        
        // 插入新条目
        List<IndustryDictionary> dictionaries = entries.stream()
            .map(entry -> convertToEntity(entry, industryCode, type))
            .collect(Collectors.toList());
        
        dictionaryRepository.saveAll(dictionaries);
        
        // 通知缓存更新
        cacheManager.evictDictionary(industryCode, type);
    }
    
    public Map<String, Double> getSentimentWords(String industryCode) {
        List<IndustryDictionary> words = dictionaryRepository
            .findByIndustryCodeAndType(industryCode, DictionaryType.SENTIMENT);
        
        return words.stream()
            .collect(Collectors.toMap(
                IndustryDictionary::getTerm,
                IndustryDictionary::getWeight
            ));
    }
}
```

---

## 📊 报表展示模块（Reporting Module）

### 4.1 模块概述

**功能职责**：
- 灵活的报表生成和渲染
- 丰富的可视化组件
- 交互式分析支持
- 多格式数据导出

### 4.2 核心组件设计

#### 4.2.1 报表引擎（Report Engine）

**报表模板定义**：
```json
{
  "reportTemplates": {
    "sentiment_analysis_report": {
      "name": "情感分析报表",
      "description": "分析客户反馈的情感分布和趋势",
      "category": "analysis",
      "dataSource": "dwd_voc_detail",
      "parameters": [
        {
          "name": "date_range",
          "type": "date_range",
          "required": true,
          "default": "last_30_days"
        },
        {
          "name": "industry_code", 
          "type": "string",
          "required": true
        },
        {
          "name": "channel_types",
          "type": "multi_select",
          "options": ["store", "app", "hotline", "social"],
          "default": ["store", "app"]
        }
      ],
      "queries": [
        {
          "name": "sentiment_distribution",
          "sql": "SELECT sentiment, COUNT(*) as count FROM dwd_voc_detail WHERE feedback_time >= :start_date AND feedback_time <= :end_date AND industry_code = :industry_code GROUP BY sentiment"
        },
        {
          "name": "sentiment_trend",
          "sql": "SELECT DATE(feedback_time) as date, sentiment, COUNT(*) as count FROM dwd_voc_detail WHERE feedback_time >= :start_date AND feedback_time <= :end_date AND industry_code = :industry_code GROUP BY DATE(feedback_time), sentiment ORDER BY date"
        }
      ],
      "charts": [
        {
          "name": "sentiment_pie_chart",
          "type": "pie",
          "dataSource": "sentiment_distribution",
          "title": "情感分布",
          "config": {
            "labelField": "sentiment",
            "valueField": "count"
          }
        },
        {
          "name": "sentiment_line_chart", 
          "type": "line",
          "dataSource": "sentiment_trend",
          "title": "情感趋势",
          "config": {
            "xField": "date",
            "yField": "count",
            "seriesField": "sentiment"
          }
        }
      ]
    }
  }
}
```

**报表生成服务**：
```java
@Service
public class ReportGenerationService {
    
    @Autowired
    private ReportTemplateRepository templateRepository;
    
    @Autowired
    private DataQueryService dataQueryService;
    
    @Autowired
    private ChartRenderService chartRenderService;
    
    public ReportResult generateReport(String templateId, Map<String, Object> parameters) {
        // 获取报表模板
        ReportTemplate template = templateRepository.findById(templateId)
            .orElseThrow(() -> new ReportException("报表模板不存在: " + templateId));
        
        // 验证参数
        validateParameters(template, parameters);
        
        // 执行数据查询
        Map<String, List<Map<String, Object>>> queryResults = new HashMap<>();
        for (QueryConfig query : template.getQueries()) {
            List<Map<String, Object>> data = dataQueryService.executeQuery(
                query.getSql(), parameters
            );
            queryResults.put(query.getName(), data);
        }
        
        // 生成图表
        List<ChartResult> charts = template.getCharts().stream()
            .map(chartConfig -> generateChart(chartConfig, queryResults))
            .collect(Collectors.toList());
        
        // 构建报表结果
        return ReportResult.builder()
            .templateId(templateId)
            .parameters(parameters)
            .generatedTime(Instant.now())
            .queryResults(queryResults)
            .charts(charts)
            .build();
    }
    
    private ChartResult generateChart(ChartConfig config, 
                                    Map<String, List<Map<String, Object>>> queryResults) {
        List<Map<String, Object>> data = queryResults.get(config.getDataSource());
        return chartRenderService.renderChart(config, data);
    }
}
```

#### 4.2.2 数据查询服务（Data Query Service）

**查询优化策略**：
```java
@Service
public class DataQueryService {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Value("${query.cache.ttl:300}")
    private int cacheTimeToLive;
    
    public List<Map<String, Object>> executeQuery(String sql, Map<String, Object> parameters) {
        // 生成缓存键
        String cacheKey = generateCacheKey(sql, parameters);
        
        // 尝试从缓存获取
        List<Map<String, Object>> cachedResult = getCachedResult(cacheKey);
        if (cachedResult != null) {
            return cachedResult;
        }
        
        // 执行查询
        List<Map<String, Object>> result = executeQueryWithParameters(sql, parameters);
        
        // 缓存结果
        cacheResult(cacheKey, result);
        
        return result;
    }
    
    private List<Map<String, Object>> executeQueryWithParameters(String sql, 
                                                              Map<String, Object> parameters) {
        try {
            return jdbcTemplate.queryForList(sql, parameters);
        } catch (DataAccessException e) {
            throw new QueryExecutionException("查询执行失败: " + sql, e);
        }
    }
    
    private String generateCacheKey(String sql, Map<String, Object> parameters) {
        String combined = sql + parameters.toString();
        return "query:" + DigestUtils.md5Hex(combined);
    }
}
```

#### 4.2.3 导出服务（Export Service）

**多格式导出支持**：
```java
@Service
public class ReportExportService {
    
    @Autowired
    private ExcelExportHandler excelHandler;
    
    @Autowired
    private PdfExportHandler pdfHandler;
    
    @Autowired
    private CsvExportHandler csvHandler;
    
    public ExportResult exportReport(ReportResult report, ExportFormat format, 
                                   ExportOptions options) {
        switch (format) {
            case EXCEL:
                return excelHandler.export(report, options);
            case PDF:
                return pdfHandler.export(report, options);
            case CSV:
                return csvHandler.export(report, options);
            default:
                throw new UnsupportedExportFormatException("不支持的导出格式: " + format);
        }
    }
}

@Component
public class ExcelExportHandler implements ExportHandler {
    
    @Override
    public ExportResult export(ReportResult report, ExportOptions options) {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            // 创建工作表
            XSSFSheet sheet = workbook.createSheet(report.getTitle());
            
            // 写入标题
            writeHeader(sheet, report);
            
            // 写入数据
            writeData(sheet, report);
            
            // 添加图表
            addCharts(sheet, report);
            
            // 生成文件
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            
            return ExportResult.builder()
                .fileName(generateFileName(report, "xlsx"))
                .contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                .content(outputStream.toByteArray())
                .build();
                
        } catch (IOException e) {
            throw new ExportException("Excel导出失败", e);
        }
    }
}
```

---

## 🔗 模块间交互设计

### 5.1 模块交互时序图

```mermaid
sequenceDiagram
    participant Client
    participant DataIngestion
    participant Analysis
    participant Configuration
    participant Reporting
    participant Storage
    
    Client->>DataIngestion: 提交数据
    DataIngestion->>Configuration: 获取行业配置
    Configuration-->>DataIngestion: 返回配置信息
    DataIngestion->>DataIngestion: 数据清洗和验证
    DataIngestion->>Analysis: 发送清洗后数据
    Analysis->>Configuration: 获取分析配置
    Configuration-->>Analysis: 返回分析配置
    Analysis->>Analysis: AI分析处理
    Analysis->>Storage: 保存分析结果
    Storage-->>Analysis: 确认保存
    Analysis-->>DataIngestion: 返回分析结果
    DataIngestion-->>Client: 返回处理状态
    
    Client->>Reporting: 请求生成报表
    Reporting->>Storage: 查询分析数据
    Storage-->>Reporting: 返回数据
    Reporting->>Reporting: 生成报表和图表
    Reporting-->>Client: 返回报表结果
```

### 5.2 事件驱动交互

**事件定义**：
```json
{
  "eventTypes": {
    "DataIngested": {
      "description": "数据接入完成事件",
      "payload": {
        "taskId": "string",
        "industryCode": "string", 
        "recordCount": "number",
        "status": "success|failed"
      },
      "subscribers": ["AnalysisModule", "AuditModule"]
    },
    "AnalysisCompleted": {
      "description": "分析完成事件",
      "payload": {
        "taskId": "string",
        "analysisResults": "object",
        "confidence": "number"
      },
      "subscribers": ["ReportingModule", "NotificationModule"]
    },
    "ConfigurationUpdated": {
      "description": "配置更新事件", 
      "payload": {
        "industryCode": "string",
        "configType": "string",
        "version": "string"
      },
      "subscribers": ["AnalysisModule", "DataIngestionModule"]
    }
  }
}
```

**事件发布订阅**：
```java
@Component
public class EventPublisher {
    
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    
    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;
    
    public void publishDataIngestedEvent(DataIngestedEvent event) {
        // 本地事件发布
        applicationEventPublisher.publishEvent(event);
        
        // 分布式事件发布
        kafkaTemplate.send("data-ingested", event);
    }
}

@EventListener
@Component
public class AnalysisEventHandler {
    
    @Autowired
    private AnalysisService analysisService;
    
    @EventListener
    public void handleDataIngestedEvent(DataIngestedEvent event) {
        if ("success".equals(event.getStatus())) {
            // 触发数据分析
            analysisService.startAnalysis(event.getTaskId());
        }
    }
}
```

---

## 📋 模块配置示例

### 6.1 汽车行业配置

```json
{
  "industryCode": "automotive",
  "industryName": "汽车行业",
  "version": "1.0.0",
  "dataIngestion": {
    "supportedFormats": ["csv", "json", "excel", "database"],
    "validationRules": {
      "requiredFields": ["user_id", "feedback_text", "vehicle_model"],
      "maxTextLength": 5000,
      "minTextLength": 10
    },
    "cleaningRules": {
      "removePersonalInfo": true,
      "normalizeVehicleModel": true,
      "filterSpam": true
    }
  },
  "analysis": {
    "sentimentAnalysis": {
      "threshold": 0.7,
      "model": "automotive-sentiment-v1",
      "customWeights": {
        "safety": 2.0,
        "quality": 1.5,
        "service": 1.2
      }
    },
    "intentRecognition": {
      "threshold": 0.8,
      "intents": ["complaint", "consultation", "suggestion", "praise"],
      "priority": ["complaint", "consultation", "suggestion", "praise"]
    },
    "topicClassification": {
      "threshold": 0.75,
      "topics": ["product_quality", "service_quality", "price", "features"]
    }
  },
  "reporting": {
    "defaultCharts": ["sentiment_distribution", "topic_analysis", "trend_analysis"],
    "refreshInterval": 300,
    "exportFormats": ["excel", "pdf", "csv"]
  }
}
```

---

**文档维护**: 系统架构师、模块负责人  
**审核**: 技术负责人、产品经理  
**下次更新**: 模块设计变更时及时更新 