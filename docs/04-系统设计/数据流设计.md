# 通用VOC报表系统数据流设计

**文档版本**: v1.0.0  
**创建日期**: 2024年1月  
**最后更新**: 2024年1月  
**文档状态**: 设计阶段

---

## 📋 文档概述

本文档详细描述了通用VOC报表系统中数据的完整流转过程，包括数据的接入、处理、分析、存储以及展示等各个环节的设计。

### 数据流设计原则
- **实时性**：支持实时数据流处理和近实时分析
- **可靠性**：确保数据不丢失，具备容错和恢复机制
- **可扩展性**：支持数据量和并发量的线性扩展
- **一致性**：保证数据在各个环节的一致性
- **可追溯性**：完整的数据血缘和处理链路追踪

---

## 🌊 数据流总览

### 1.1 整体数据流架构

```mermaid
graph TB
    subgraph "数据源层 Data Sources"
        A1[CSV文件]
        A2[JSON数据]
        A3[数据库]
        A4[API接口]
        A5[Excel文件]
        A6[实时流数据]
    end
    
    subgraph "数据接入层 Data Ingestion"
        B1[文件适配器]
        B2[数据库适配器]
        B3[API适配器]
        B4[流数据适配器]
        B5[数据验证器]
        B6[数据清洗器]
    end
    
    subgraph "消息队列层 Message Queue"
        C1[原始数据队列]
        C2[清洗数据队列]
        C3[分析结果队列]
    end
    
    subgraph "数据处理层 Data Processing"
        D1[字段映射]
        D2[数据标准化]
        D3[AI分析引擎]
        D4[规则引擎]
        D5[结果合并]
    end
    
    subgraph "数据存储层 Data Storage"
        E1[原始数据存储]
        E2[DWD明细表]
        E3[DWS汇总表]
        E4[DWT主题表]
        E5[缓存层]
    end
    
    subgraph "数据服务层 Data Service"
        F1[查询服务]
        F2[报表服务]
        F3[导出服务]
        F4[API服务]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B2
    A4 --> B3
    A5 --> B1
    A6 --> B4
    
    B1 --> B5
    B2 --> B5
    B3 --> B5
    B4 --> B5
    B5 --> B6
    B6 --> C1
    
    C1 --> D1
    D1 --> D2
    D2 --> D3
    D3 --> D4
    D4 --> D5
    D5 --> C2
    
    C2 --> E2
    E2 --> E3
    E3 --> E4
    E2 --> E5
    
    E2 --> F1
    E3 --> F2
    E4 --> F3
    E5 --> F4
```

### 1.2 数据流分层说明

**数据源层（Data Sources）**：
- 多样化的数据输入源，包括文件、数据库、API、实时流等
- 支持结构化和非结构化数据格式
- 提供统一的数据接入接口

**数据接入层（Data Ingestion）**：
- 负责从各种数据源采集数据
- 进行初步的数据验证和清洗
- 转换为统一的内部数据格式

**消息队列层（Message Queue）**：
- 解耦数据接入和处理模块
- 提供数据缓冲和削峰能力
- 支持数据的异步处理

**数据处理层（Data Processing）**：
- 执行数据转换和标准化
- 进行AI分析和业务规则处理
- 生成标准化的分析结果

**数据存储层（Data Storage）**：
- 分层存储不同粒度的数据
- 提供高性能的数据访问能力
- 支持数据的生命周期管理

**数据服务层（Data Service）**：
- 对外提供数据查询和分析服务
- 支持多种数据输出格式
- 提供RESTful API接口

---

## 📥 数据接入流设计

### 2.1 批量数据接入流

**批量处理流程**：
```mermaid
sequenceDiagram
    participant Client
    participant FileUpload
    participant DataValidator
    participant DataCleaner
    participant MessageQueue
    participant DataProcessor
    participant Storage
    
    Client->>FileUpload: 上传数据文件
    FileUpload->>FileUpload: 文件格式检测
    FileUpload->>DataValidator: 数据验证
    DataValidator->>DataValidator: 格式验证
    DataValidator->>DataValidator: 业务规则验证
    DataValidator->>DataCleaner: 验证通过
    DataCleaner->>DataCleaner: 数据清洗
    DataCleaner->>DataCleaner: 标准化处理
    DataCleaner->>MessageQueue: 发送清洗数据
    MessageQueue->>DataProcessor: 消费数据
    DataProcessor->>DataProcessor: AI分析处理
    DataProcessor->>Storage: 保存处理结果
    Storage-->>Client: 返回处理状态
```

**批量接入配置**：
```json
{
  "batchIngestionConfig": {
    "fileUpload": {
      "maxFileSize": "100MB",
      "supportedFormats": ["csv", "xlsx", "json", "xml"],
      "tempStorage": "/tmp/uploads",
      "retentionDays": 7
    },
    "validation": {
      "maxRecordCount": 100000,
      "timeoutSeconds": 300,
      "errorThreshold": 0.05,
      "sampleValidation": true,
      "sampleSize": 1000
    },
    "processing": {
      "batchSize": 1000,
      "parallelWorkers": 5,
      "retryAttempts": 3,
      "deadLetterQueue": true
    }
  }
}
```

**实现代码**：
```java
@Service
public class BatchDataIngestionService {
    
    @Autowired
    private FileUploadService fileUploadService;
    
    @Autowired
    private DataValidationService validationService;
    
    @Autowired
    private DataCleaningService cleaningService;
    
    @Autowired
    private MessageQueueService messageQueueService;
    
    public IngestionResult processBatchData(MultipartFile file, IngestionConfig config) {
        try {
            // 1. 文件上传和解析
            UploadResult uploadResult = fileUploadService.uploadAndParse(file);
            
            // 2. 数据验证
            ValidationResult validationResult = validationService.validateBatch(
                uploadResult.getRecords(), config.getValidationRules()
            );
            
            if (!validationResult.isValid()) {
                return IngestionResult.failed(validationResult.getErrors());
            }
            
            // 3. 数据清洗
            List<CleanedRecord> cleanedRecords = cleaningService.cleanBatch(
                validationResult.getValidRecords(), config.getCleaningRules()
            );
            
            // 4. 分批发送到消息队列
            String taskId = UUID.randomUUID().toString();
            List<List<CleanedRecord>> batches = Lists.partition(
                cleanedRecords, config.getBatchSize()
            );
            
            for (List<CleanedRecord> batch : batches) {
                BatchMessage message = BatchMessage.builder()
                    .taskId(taskId)
                    .batchId(UUID.randomUUID().toString())
                    .records(batch)
                    .config(config)
                    .timestamp(Instant.now())
                    .build();
                
                messageQueueService.sendBatchMessage(message);
            }
            
            return IngestionResult.success(taskId, cleanedRecords.size());
            
        } catch (Exception e) {
            log.error("批量数据接入失败", e);
            return IngestionResult.failed("系统错误: " + e.getMessage());
        }
    }
}
```

### 2.2 实时数据接入流

**实时处理流程**：
```mermaid
graph LR
    A[实时数据源] --> B[流数据接收器]
    B --> C[实时验证]
    C --> D[流式清洗]
    D --> E[消息队列]
    E --> F[流式分析]
    F --> G[实时存储]
    G --> H[实时更新缓存]
```

**流处理配置**：
```yaml
# Kafka配置
kafka:
  bootstrap-servers: localhost:9092
  topics:
    raw-data: voc-raw-data
    cleaned-data: voc-cleaned-data
    analysis-results: voc-analysis-results
  consumer:
    group-id: voc-processor
    auto-offset-reset: latest
    max-poll-records: 500
  producer:
    batch-size: 16384
    linger-ms: 5
    compression-type: gzip

# Flink配置  
flink:
  parallelism: 4
  checkpoint:
    interval: 60000
    timeout: 300000
  watermark:
    interval: 5000
```

**流处理实现**：
```java
@Component
public class RealTimeDataIngestionJob {
    
    public void createIngestionJob(StreamExecutionEnvironment env) {
        // 1. 创建Kafka数据源
        FlinkKafkaConsumer<RawDataRecord> kafkaConsumer = new FlinkKafkaConsumer<>(
            "voc-raw-data",
            new RawDataRecordDeserializer(),
            getKafkaProperties()
        );
        
        DataStream<RawDataRecord> rawDataStream = env.addSource(kafkaConsumer);
        
        // 2. 数据验证和过滤
        DataStream<RawDataRecord> validDataStream = rawDataStream
            .filter(new ValidationFunction())
            .name("data-validation");
        
        // 3. 数据清洗
        DataStream<CleanedDataRecord> cleanedDataStream = validDataStream
            .map(new DataCleaningFunction())
            .name("data-cleaning");
        
        // 4. 发送到下游队列
        FlinkKafkaProducer<CleanedDataRecord> kafkaProducer = new FlinkKafkaProducer<>(
            "voc-cleaned-data",
            new CleanedDataRecordSerializer(),
            getKafkaProperties()
        );
        
        cleanedDataStream.addSink(kafkaProducer).name("kafka-sink");
    }
    
    public static class ValidationFunction implements FilterFunction<RawDataRecord> {
        @Override
        public boolean filter(RawDataRecord record) throws Exception {
            return record != null 
                && record.getText() != null 
                && record.getText().length() >= 10
                && record.getText().length() <= 5000;
        }
    }
    
    public static class DataCleaningFunction implements MapFunction<RawDataRecord, CleanedDataRecord> {
        @Override
        public CleanedDataRecord map(RawDataRecord record) throws Exception {
            String cleanedText = cleanText(record.getText());
            
            return CleanedDataRecord.builder()
                .id(record.getId())
                .originalText(record.getText())
                .cleanedText(cleanedText)
                .timestamp(record.getTimestamp())
                .source(record.getSource())
                .build();
        }
        
        private String cleanText(String text) {
            return text.trim()
                .replaceAll("\\s+", " ")
                .replaceAll("[\\p{Cntrl}&&[^\r\n\t]]", "");
        }
    }
}
```

---

## 🔄 数据处理流设计

### 3.1 AI分析处理流

**分析处理管道**：
```mermaid
graph TD
    A[清洗数据队列] --> B{数据路由}
    B -->|情感分析| C[情感分析队列]
    B -->|意图识别| D[意图识别队列] 
    B -->|主题分类| E[主题分类队列]
    
    C --> F[情感分析器]
    D --> G[意图识别器]
    E --> H[主题分类器]
    
    F --> I[结果合并器]
    G --> I
    H --> I
    
    I --> J[规则引擎]
    J --> K[优先级计算]
    K --> L[分析结果队列]
```

**并行分析配置**：
```json
{
  "analysisProcessingConfig": {
    "parallelAnalysis": {
      "enabled": true,
      "maxConcurrency": 10,
      "queueCapacity": 1000,
      "timeout": 30000
    },
    "analysisTypes": {
      "sentiment": {
        "enabled": true,
        "priority": 1,
        "batchSize": 50,
        "model": "sentiment-analysis-v1"
      },
      "intent": {
        "enabled": true,
        "priority": 2,
        "batchSize": 30,
        "model": "intent-recognition-v1"
      },
      "topic": {
        "enabled": true,
        "priority": 3,
        "batchSize": 40,
        "model": "topic-classification-v1"
      }
    },
    "resultMerging": {
      "strategy": "wait_all",
      "timeout": 60000,
      "partialResultHandling": "retry"
    }
  }
}
```

**分析处理实现**：
```java
@Service
public class AnalysisProcessingService {
    
    @Autowired
    private SentimentAnalysisService sentimentService;
    
    @Autowired
    private IntentRecognitionService intentService;
    
    @Autowired
    private TopicClassificationService topicService;
    
    @Autowired
    private ResultMergingService resultMergingService;
    
    @Async("analysisExecutor")
    public CompletableFuture<AnalysisResult> processAnalysis(CleanedDataRecord record, 
                                                            IndustryConfig config) {
        
        // 创建分析任务
        List<CompletableFuture<AnalysisComponent>> analysisTasks = Arrays.asList(
            // 情感分析
            CompletableFuture.supplyAsync(() -> 
                sentimentService.analyze(record.getCleanedText(), config)),
                
            // 意图识别  
            CompletableFuture.supplyAsync(() -> 
                intentService.recognize(record.getCleanedText(), config)),
                
            // 主题分类
            CompletableFuture.supplyAsync(() -> 
                topicService.classify(record.getCleanedText(), config))
        );
        
        // 等待所有分析完成
        return CompletableFuture.allOf(analysisTasks.toArray(new CompletableFuture[0]))
            .thenApply(v -> {
                List<AnalysisComponent> components = analysisTasks.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList());
                
                // 合并分析结果
                return resultMergingService.mergeResults(record, components, config);
            })
            .exceptionally(throwable -> {
                log.error("分析处理失败: recordId={}", record.getId(), throwable);
                return AnalysisResult.failed(record.getId(), throwable.getMessage());
            });
    }
}
```

### 3.2 规则引擎处理流

**规则处理流程**：
```mermaid
sequenceDiagram
    participant AnalysisEngine
    participant RulesEngine
    participant ConfigService
    participant PriorityCalculator
    participant NotificationService
    participant Storage
    
    AnalysisEngine->>RulesEngine: 分析结果
    RulesEngine->>ConfigService: 获取业务规则
    ConfigService-->>RulesEngine: 返回规则配置
    RulesEngine->>RulesEngine: 执行规则匹配
    RulesEngine->>PriorityCalculator: 计算优先级
    PriorityCalculator-->>RulesEngine: 返回优先级
    RulesEngine->>NotificationService: 触发通知
    RulesEngine->>Storage: 保存处理结果
    Storage-->>RulesEngine: 确认保存
    RulesEngine-->>AnalysisEngine: 返回最终结果
```

**规则执行引擎**：
```java
@Component
public class RulesExecutionEngine {
    
    @Autowired
    private ConfigurationService configService;
    
    @Autowired
    private DroolsService droolsService;
    
    @Autowired
    private NotificationService notificationService;
    
    public ProcessedResult executeRules(AnalysisResult analysisResult, 
                                      IndustryConfig industryConfig) {
        
        // 1. 获取业务规则
        BusinessRules rules = configService.getBusinessRules(
            industryConfig.getIndustryCode()
        );
        
        // 2. 创建规则会话
        KieSession kieSession = droolsService.createSession(rules);
        
        // 3. 设置全局变量
        ProcessedResult result = new ProcessedResult(analysisResult);
        kieSession.setGlobal("result", result);
        kieSession.setGlobal("notificationService", notificationService);
        
        // 4. 插入事实对象
        kieSession.insert(analysisResult);
        kieSession.insert(industryConfig);
        
        // 5. 执行规则
        int firedRules = kieSession.fireAllRules();
        
        // 6. 清理资源
        kieSession.dispose();
        
        result.setFiredRulesCount(firedRules);
        return result;
    }
}
```

---

## 💾 数据存储流设计

### 3.1 分层存储架构

**存储分层策略**：
```mermaid
graph TB
    subgraph "实时层 Real-time Layer"
        A1[内存缓存Redis]
        A2[实时数据库ClickHouse]
    end
    
    subgraph "明细层 Detail Layer"
        B1[DWD原始明细表]
        B2[DWD分析明细表]
        B3[DWD扩展明细表]
    end
    
    subgraph "汇总层 Summary Layer"
        C1[DWS日汇总表]
        C2[DWS周汇总表]
        C3[DWS月汇总表]
    end
    
    subgraph "主题层 Topic Layer"
        D1[DWT情感主题表]
        D2[DWT意图主题表]
        D3[DWT产品主题表]
    end
    
    subgraph "归档层 Archive Layer"
        E1[历史数据归档]
        E2[冷数据存储]
    end
    
    A1 --> B1
    A2 --> B2
    B1 --> C1
    B2 --> C2
    C1 --> D1
    C2 --> D2
    C3 --> D3
    D1 --> E1
    D2 --> E2
```

### 3.2 DWD明细表设计

**主表结构**：
```sql
-- DWD VOC明细表
CREATE TABLE dwd_voc_detail (
    -- 主键和分区字段
    id BIGINT PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL,
    industry_code VARCHAR(20) NOT NULL,
    feedback_date DATE NOT NULL,
    
    -- 时间字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    feedback_time TIMESTAMP NOT NULL,
    processed_time TIMESTAMP,
    
    -- 原始数据字段
    user_id VARCHAR(100),
    store_id VARCHAR(100),
    product_id VARCHAR(100),
    original_text TEXT NOT NULL,
    cleaned_text TEXT,
    data_source VARCHAR(50) NOT NULL,
    channel_type VARCHAR(20) NOT NULL,
    
    -- 分析结果字段
    sentiment VARCHAR(20),
    sentiment_confidence DECIMAL(5,4),
    sentiment_intensity INTEGER,
    intent VARCHAR(50),
    intent_confidence DECIMAL(5,4),
    topic VARCHAR(100),
    topic_confidence DECIMAL(5,4),
    urgency_level VARCHAR(20),
    user_type VARCHAR(20),
    
    -- 业务字段
    priority_score INTEGER DEFAULT 0,
    processing_status VARCHAR(20) DEFAULT 'pending',
    assigned_to VARCHAR(100),
    resolution_status VARCHAR(20),
    
    -- 质量字段
    data_quality_score DECIMAL(3,2),
    validation_errors TEXT,
    
    -- 扩展字段
    business_fields JSONB,
    tags JSONB,
    metadata JSONB
)
PARTITION BY RANGE (feedback_date);

-- 创建分区
CREATE TABLE dwd_voc_detail_2024_01 PARTITION OF dwd_voc_detail
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- 创建索引
CREATE INDEX idx_dwd_voc_tenant_industry ON dwd_voc_detail (tenant_id, industry_code);
CREATE INDEX idx_dwd_voc_feedback_time ON dwd_voc_detail (feedback_time);
CREATE INDEX idx_dwd_voc_sentiment_intent ON dwd_voc_detail (sentiment, intent);
CREATE INDEX idx_dwd_voc_data_source ON dwd_voc_detail (data_source, channel_type);
CREATE INDEX idx_dwd_voc_processing_status ON dwd_voc_detail (processing_status);
```

**数据写入流程**：
```java
@Service
public class DWDDataWriteService {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Transactional
    public void writeDWDRecord(ProcessedResult processedResult) {
        try {
            // 1. 插入明细表
            insertDWDDetail(processedResult);
            
            // 2. 更新实时缓存
            updateRealTimeCache(processedResult);
            
            // 3. 触发下游处理
            triggerDownstreamProcessing(processedResult);
            
        } catch (Exception e) {
            log.error("DWD数据写入失败: {}", processedResult.getId(), e);
            throw new DataWriteException("DWD数据写入失败", e);
        }
    }
    
    private void insertDWDDetail(ProcessedResult result) {
        String sql = """
            INSERT INTO dwd_voc_detail (
                id, tenant_id, industry_code, feedback_date,
                feedback_time, user_id, store_id, product_id,
                original_text, cleaned_text, data_source, channel_type,
                sentiment, sentiment_confidence, intent, intent_confidence,
                topic, topic_confidence, urgency_level, user_type,
                priority_score, data_quality_score, business_fields
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?::jsonb)
            """;
        
        jdbcTemplate.update(sql,
            result.getId(),
            result.getTenantId(),
            result.getIndustryCode(),
            result.getFeedbackDate(),
            result.getFeedbackTime(),
            result.getUserId(),
            result.getStoreId(),
            result.getProductId(),
            result.getOriginalText(),
            result.getCleanedText(),
            result.getDataSource(),
            result.getChannelType(),
            result.getSentiment(),
            result.getSentimentConfidence(),
            result.getIntent(),
            result.getIntentConfidence(),
            result.getTopic(),
            result.getTopicConfidence(),
            result.getUrgencyLevel(),
            result.getUserType(),
            result.getPriorityScore(),
            result.getDataQualityScore(),
            objectMapper.writeValueAsString(result.getBusinessFields())
        );
    }
    
    private void updateRealTimeCache(ProcessedResult result) {
        // 更新实时统计缓存
        String cacheKey = String.format("realtime_stats:%s:%s", 
            result.getTenantId(), result.getIndustryCode());
        
        redisTemplate.opsForHash().increment(cacheKey, "total_count", 1);
        redisTemplate.opsForHash().increment(cacheKey, 
            "sentiment:" + result.getSentiment(), 1);
        redisTemplate.opsForHash().increment(cacheKey, 
            "intent:" + result.getIntent(), 1);
        
        // 设置过期时间
        redisTemplate.expire(cacheKey, Duration.ofHours(24));
    }
}
```

### 3.3 数据汇总流设计

**汇总处理流程**：
```mermaid
graph LR
    A[DWD明细表] --> B[汇总任务调度器]
    B --> C[日汇总处理]
    B --> D[周汇总处理]
    B --> E[月汇总处理]
    
    C --> F[DWS日汇总表]
    D --> G[DWS周汇总表]
    E --> H[DWS月汇总表]
    
    F --> I[主题汇总]
    G --> I
    H --> I
    
    I --> J[DWT主题表]
```

**汇总SQL示例**：
```sql
-- 日汇总表结构
CREATE TABLE dws_voc_daily_summary (
    summary_date DATE PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL,
    industry_code VARCHAR(20) NOT NULL,
    
    -- 总体统计
    total_count INTEGER DEFAULT 0,
    processed_count INTEGER DEFAULT 0,
    pending_count INTEGER DEFAULT 0,
    
    -- 情感统计
    positive_count INTEGER DEFAULT 0,
    negative_count INTEGER DEFAULT 0,
    neutral_count INTEGER DEFAULT 0,
    avg_sentiment_confidence DECIMAL(5,4),
    
    -- 意图统计
    complaint_count INTEGER DEFAULT 0,
    consultation_count INTEGER DEFAULT 0,
    suggestion_count INTEGER DEFAULT 0,
    praise_count INTEGER DEFAULT 0,
    
    -- 渠道统计
    store_count INTEGER DEFAULT 0,
    app_count INTEGER DEFAULT 0,
    hotline_count INTEGER DEFAULT 0,
    social_count INTEGER DEFAULT 0,
    
    -- 质量统计
    avg_quality_score DECIMAL(3,2),
    high_quality_count INTEGER DEFAULT 0,
    low_quality_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 日汇总ETL作业
WITH daily_stats AS (
    SELECT 
        DATE(feedback_time) as summary_date,
        tenant_id,
        industry_code,
        COUNT(*) as total_count,
        COUNT(CASE WHEN processing_status = 'completed' THEN 1 END) as processed_count,
        COUNT(CASE WHEN processing_status = 'pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN sentiment = 'positive' THEN 1 END) as positive_count,
        COUNT(CASE WHEN sentiment = 'negative' THEN 1 END) as negative_count,
        COUNT(CASE WHEN sentiment = 'neutral' THEN 1 END) as neutral_count,
        AVG(sentiment_confidence) as avg_sentiment_confidence,
        COUNT(CASE WHEN intent = 'complaint' THEN 1 END) as complaint_count,
        COUNT(CASE WHEN intent = 'consultation' THEN 1 END) as consultation_count,
        COUNT(CASE WHEN intent = 'suggestion' THEN 1 END) as suggestion_count,
        COUNT(CASE WHEN intent = 'praise' THEN 1 END) as praise_count,
        COUNT(CASE WHEN channel_type = 'store' THEN 1 END) as store_count,
        COUNT(CASE WHEN channel_type = 'app' THEN 1 END) as app_count,
        COUNT(CASE WHEN channel_type = 'hotline' THEN 1 END) as hotline_count,
        COUNT(CASE WHEN channel_type = 'social' THEN 1 END) as social_count,
        AVG(data_quality_score) as avg_quality_score,
        COUNT(CASE WHEN data_quality_score >= 0.8 THEN 1 END) as high_quality_count,
        COUNT(CASE WHEN data_quality_score < 0.6 THEN 1 END) as low_quality_count
    FROM dwd_voc_detail
    WHERE DATE(feedback_time) = CURRENT_DATE - INTERVAL '1 day'
    GROUP BY DATE(feedback_time), tenant_id, industry_code
)
INSERT INTO dws_voc_daily_summary (
    summary_date, tenant_id, industry_code,
    total_count, processed_count, pending_count,
    positive_count, negative_count, neutral_count, avg_sentiment_confidence,
    complaint_count, consultation_count, suggestion_count, praise_count,
    store_count, app_count, hotline_count, social_count,
    avg_quality_score, high_quality_count, low_quality_count
)
SELECT * FROM daily_stats
ON CONFLICT (summary_date, tenant_id, industry_code) 
DO UPDATE SET
    total_count = EXCLUDED.total_count,
    processed_count = EXCLUDED.processed_count,
    -- ... 其他字段更新
    updated_at = CURRENT_TIMESTAMP;
```

---

## 🔄 数据血缘追踪

### 4.1 血缘关系设计

**血缘关系模型**：
```json
{
  "dataLineage": {
    "entities": [
      {
        "id": "raw_data_csv",
        "type": "data_source",
        "name": "原始CSV文件",
        "schema": ["user_id", "feedback_text", "timestamp"]
      },
      {
        "id": "dwd_voc_detail",
        "type": "table",
        "name": "DWD VOC明细表",
        "schema": ["id", "original_text", "sentiment", "intent"]
      },
      {
        "id": "dws_daily_summary",
        "type": "table", 
        "name": "DWS日汇总表",
        "schema": ["summary_date", "total_count", "positive_count"]
      }
    ],
    "relationships": [
      {
        "from": "raw_data_csv",
        "to": "dwd_voc_detail",
        "type": "data_flow",
        "transformations": ["validation", "cleaning", "ai_analysis"],
        "fields_mapping": {
          "feedback_text": "original_text",
          "timestamp": "feedback_time"
        }
      },
      {
        "from": "dwd_voc_detail",
        "to": "dws_daily_summary",
        "type": "aggregation",
        "transformations": ["group_by_date", "count", "average"],
        "fields_mapping": {
          "sentiment": "positive_count,negative_count,neutral_count",
          "feedback_time": "summary_date"
        }
      }
    ]
  }
}
```

**血缘追踪实现**：
```java
@Component
public class DataLineageTracker {
    
    @Autowired
    private LineageRepository lineageRepository;
    
    public void trackDataFlow(String sourceId, String targetId, 
                            DataTransformation transformation) {
        LineageRecord record = LineageRecord.builder()
            .sourceEntityId(sourceId)
            .targetEntityId(targetId)
            .transformationType(transformation.getType())
            .transformationDetails(transformation.getDetails())
            .fieldMappings(transformation.getFieldMappings())
            .processTime(Instant.now())
            .build();
        
        lineageRepository.save(record);
    }
    
    public List<LineageRecord> getUpstreamLineage(String entityId) {
        return lineageRepository.findUpstreamByTargetEntityId(entityId);
    }
    
    public List<LineageRecord> getDownstreamLineage(String entityId) {
        return lineageRepository.findDownstreamBySourceEntityId(entityId);
    }
    
    public DataImpactAnalysis analyzeImpact(String entityId, String changeType) {
        // 分析数据变更的影响范围
        List<LineageRecord> downstream = getDownstreamLineage(entityId);
        
        Set<String> impactedEntities = downstream.stream()
            .map(LineageRecord::getTargetEntityId)
            .collect(Collectors.toSet());
        
        return DataImpactAnalysis.builder()
            .sourceEntity(entityId)
            .changeType(changeType)
            .impactedEntities(impactedEntities)
            .impactLevel(calculateImpactLevel(impactedEntities.size()))
            .build();
    }
}
```

---

## 📊 数据质量监控

### 5.1 质量监控指标

**质量监控维度**：
```json
{
  "dataQualityMetrics": {
    "completeness": {
      "description": "数据完整性",
      "metrics": [
        "required_field_completeness_rate",
        "record_completeness_rate",
        "data_volume_trend"
      ],
      "thresholds": {
        "warning": 0.95,
        "critical": 0.90
      }
    },
    "accuracy": {
      "description": "数据准确性",
      "metrics": [
        "format_error_rate",
        "validation_pass_rate",
        "ai_analysis_confidence"
      ],
      "thresholds": {
        "warning": 0.92,
        "critical": 0.85
      }
    },
    "consistency": {
      "description": "数据一致性",
      "metrics": [
        "duplicate_rate",
        "schema_compliance_rate",
        "cross_source_consistency"
      ],
      "thresholds": {
        "warning": 0.98,
        "critical": 0.95
      }
    },
    "timeliness": {
      "description": "数据时效性",
      "metrics": [
        "processing_latency",
        "data_freshness",
        "sla_compliance_rate"
      ],
      "thresholds": {
        "warning": 300,
        "critical": 600
      }
    }
  }
}
```

**质量监控实现**：
```java
@Service
public class DataQualityMonitorService {
    
    @Autowired
    private DataQualityRepository qualityRepository;
    
    @Autowired
    private AlertService alertService;
    
    @Scheduled(fixedRate = 300000) // 每5分钟执行
    public void monitorDataQuality() {
        List<QualityMetric> metrics = calculateQualityMetrics();
        
        for (QualityMetric metric : metrics) {
            // 保存质量指标
            qualityRepository.save(metric);
            
            // 检查阈值并告警
            checkThresholdsAndAlert(metric);
        }
    }
    
    private List<QualityMetric> calculateQualityMetrics() {
        List<QualityMetric> metrics = new ArrayList<>();
        
        // 计算完整性指标
        QualityMetric completeness = calculateCompleteness();
        metrics.add(completeness);
        
        // 计算准确性指标
        QualityMetric accuracy = calculateAccuracy();
        metrics.add(accuracy);
        
        // 计算一致性指标
        QualityMetric consistency = calculateConsistency();
        metrics.add(consistency);
        
        // 计算时效性指标
        QualityMetric timeliness = calculateTimeliness();
        metrics.add(timeliness);
        
        return metrics;
    }
    
    private void checkThresholdsAndAlert(QualityMetric metric) {
        QualityThreshold threshold = getThreshold(metric.getMetricType());
        
        if (metric.getValue() < threshold.getCritical()) {
            alertService.sendCriticalAlert(
                "数据质量严重告警: " + metric.getMetricName() + 
                " 当前值: " + metric.getValue() + 
                " 阈值: " + threshold.getCritical()
            );
        } else if (metric.getValue() < threshold.getWarning()) {
            alertService.sendWarningAlert(
                "数据质量预警: " + metric.getMetricName() + 
                " 当前值: " + metric.getValue() + 
                " 阈值: " + threshold.getWarning()
            );
        }
    }
}
```

---

## 🚀 性能优化策略

### 6.1 数据流性能优化

**批处理优化**：
- **批量大小调优**：根据内存和处理能力调整批量大小
- **并行处理**：多线程并行处理数据批次
- **内存管理**：优化内存使用，避免OOM
- **垃圾回收优化**：调优JVM垃圾回收参数

**流处理优化**：
- **背压控制**：实现背压机制防止数据积压
- **检查点优化**：优化Flink检查点配置
- **状态管理**：高效的状态存储和恢复
- **水印策略**：合理的水印设置处理乱序数据

**缓存优化**：
```java
@Configuration
public class CacheOptimizationConfig {
    
    @Bean
    public CacheManager cacheManager() {
        RedisCacheManager.Builder builder = RedisCacheManager
            .RedisCacheManagerBuilder
            .fromConnectionFactory(redisConnectionFactory())
            .cacheDefaults(cacheConfiguration());
        
        return builder.build();
    }
    
    private RedisCacheConfiguration cacheConfiguration() {
        return RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(30))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
    }
}
```

### 6.2 存储性能优化

**分区策略优化**：
```sql
-- 时间分区优化
CREATE TABLE dwd_voc_detail_optimized (
    -- 字段定义同前
) PARTITION BY RANGE (feedback_date);

-- 创建月度分区
CREATE TABLE dwd_voc_detail_2024_01 PARTITION OF dwd_voc_detail_optimized
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- 子分区优化（按行业）
CREATE TABLE dwd_voc_detail_2024_01_automotive 
    PARTITION OF dwd_voc_detail_2024_01
    FOR VALUES IN ('automotive');
```

**索引优化策略**：
```sql
-- 复合索引优化
CREATE INDEX CONCURRENTLY idx_voc_multi_dimensional 
ON dwd_voc_detail (tenant_id, industry_code, feedback_date, sentiment);

-- 部分索引优化
CREATE INDEX CONCURRENTLY idx_voc_negative_sentiment 
ON dwd_voc_detail (feedback_time, urgency_level) 
WHERE sentiment = 'negative';

-- 表达式索引
CREATE INDEX CONCURRENTLY idx_voc_text_length 
ON dwd_voc_detail (length(cleaned_text)) 
WHERE length(cleaned_text) > 100;
```

---

**文档维护**: 数据架构师、系统架构师  
**审核**: 技术负责人、DBA  
**下次更新**: 数据流设计变更时及时更新 