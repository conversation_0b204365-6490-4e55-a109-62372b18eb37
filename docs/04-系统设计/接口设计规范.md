# 通用VOC报表系统接口设计规范

**文档版本**: v1.0.0  
**创建日期**: 2024年1月  
**最后更新**: 2024年1月  
**文档状态**: 设计阶段

---

## 📋 文档概述

本文档定义了通用VOC报表系统的API接口设计标准和规范，包括RESTful API设计原则、接口安全规范、数据格式标准、错误处理机制等。

### 设计原则
- **RESTful设计**：遵循REST架构风格和HTTP语义
- **统一规范**：保持接口设计的一致性和可预测性
- **安全第一**：确保接口的安全性和数据保护
- **易于使用**：提供清晰的文档和友好的错误提示
- **版本管理**：支持API版本演进和向后兼容

---

## 🌐 RESTful API设计规范

### 1.1 URL设计规范

**基础URL结构**：
```
https://api.voc-system.com/api/v1/{module}/{resource}
```

**URL设计原则**：
- **使用名词而非动词**：URL应该表示资源，而非操作
- **使用复数形式**：资源名称使用复数形式
- **层次结构清晰**：通过URL体现资源的层次关系
- **参数传递**：查询参数用于过滤、排序、分页

**URL示例**：
```bash
# 正确示例
GET /api/v1/data-ingestion/tasks
POST /api/v1/data-ingestion/tasks
GET /api/v1/data-ingestion/tasks/12345
PUT /api/v1/data-ingestion/tasks/12345
DELETE /api/v1/data-ingestion/tasks/12345

# 嵌套资源
GET /api/v1/reports/sentiment-analysis/charts
GET /api/v1/configurations/industries/automotive/dictionaries

# 查询参数
GET /api/v1/voc-records?sentiment=negative&page=1&size=20&sort=feedback_time,desc
```

### 1.2 HTTP方法规范

**标准HTTP方法使用**：

| 方法 | 用途 | 示例 | 幂等性 |
|------|------|------|--------|
| GET | 查询资源 | `GET /api/v1/reports/123` | 是 |
| POST | 创建资源 | `POST /api/v1/data-ingestion/tasks` | 否 |
| PUT | 更新整个资源 | `PUT /api/v1/configurations/industries/automotive` | 是 |
| PATCH | 部分更新资源 | `PATCH /api/v1/users/123` | 否 |
| DELETE | 删除资源 | `DELETE /api/v1/reports/123` | 是 |

**扩展HTTP方法**：
```bash
# 批量操作
POST /api/v1/voc-records/batch-analysis
POST /api/v1/reports/batch-generate

# 自定义操作
POST /api/v1/data-ingestion/tasks/12345/retry
POST /api/v1/configurations/industries/automotive/validate
POST /api/v1/reports/123/export
```

### 1.3 响应状态码规范

**常用状态码定义**：

| 状态码 | 含义 | 使用场景 |
|--------|------|----------|
| 200 | 成功 | GET、PUT、PATCH成功 |
| 201 | 已创建 | POST成功创建资源 |
| 202 | 已接受 | 异步任务已接受 |
| 204 | 无内容 | DELETE成功 |
| 400 | 请求错误 | 参数错误、格式错误 |
| 401 | 未授权 | 认证失败 |
| 403 | 禁止访问 | 权限不足 |
| 404 | 资源不存在 | 资源未找到 |
| 409 | 冲突 | 资源冲突 |
| 422 | 不可处理实体 | 业务逻辑错误 |
| 429 | 请求过多 | 限流 |
| 500 | 服务器错误 | 系统内部错误 |

---

## 📝 数据格式规范

### 2.1 请求数据格式

**JSON格式规范**：
```json
{
  "// 标准请求格式示例": "",
  "industryCode": "automotive",
  "dataSourceConfig": {
    "type": "csv",
    "filePath": "/uploads/feedback_data.csv",
    "delimiter": ",",
    "encoding": "UTF-8"
  },
  "processingOptions": {
    "enableValidation": true,
    "enableCleaning": true,
    "batchSize": 1000
  },
  "metadata": {
    "requestId": "req_12345",
    "timestamp": "2024-01-20T10:30:00Z",
    "userAgent": "VOC-Client/1.0.0"
  }
}
```

**字段命名规范**：
- **使用camelCase**：JSON字段使用驼峰命名
- **语义清晰**：字段名称要有明确的语义
- **避免缩写**：除非是通用缩写，否则使用完整单词
- **布尔字段前缀**：布尔字段使用is、has、enable等前缀

**数据类型规范**：
```json
{
  "dataTypeExamples": {
    "string": "文本字符串",
    "integer": 123,
    "number": 123.45,
    "boolean": true,
    "array": ["item1", "item2"],
    "object": {"key": "value"},
    "null": null,
    "datetime": "2024-01-20T10:30:00Z",
    "date": "2024-01-20",
    "uuid": "550e8400-e29b-41d4-a716-************"
  }
}
```

### 2.2 响应数据格式

**统一响应格式**：
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "// 实际业务数据": ""
  },
  "meta": {
    "requestId": "req_12345",
    "timestamp": "2024-01-20T10:30:00Z",
    "version": "v1.0.0"
  }
}
```

**分页响应格式**：
```json
{
  "success": true,
  "code": "200", 
  "message": "查询成功",
  "data": {
    "items": [
      {
        "id": "voc_001",
        "feedbackText": "产品质量很好",
        "sentiment": "positive",
        "feedbackTime": "2024-01-20T10:30:00Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "pageSize": 20,
      "totalItems": 150,
      "totalPages": 8,
      "hasNext": true,
      "hasPrevious": false
    }
  },
  "meta": {
    "requestId": "req_12345",
    "timestamp": "2024-01-20T10:30:00Z"
  }
}
```

**错误响应格式**：
```json
{
  "success": false,
  "code": "400",
  "message": "请求参数错误",
  "error": {
    "type": "VALIDATION_ERROR",
    "details": [
      {
        "field": "industryCode",
        "message": "行业代码不能为空",
        "rejectedValue": null
      },
      {
        "field": "dataSourceConfig.filePath",
        "message": "文件路径格式不正确",
        "rejectedValue": "invalid-path"
      }
    ]
  },
  "meta": {
    "requestId": "req_12345",
    "timestamp": "2024-01-20T10:30:00Z"
  }
}
```

---

## 🔐 接口安全规范

### 3.1 认证授权机制

**JWT Token认证**：
```yaml
# 请求头格式
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Token结构
Header:
{
  "alg": "HS256",
  "typ": "JWT"
}

Payload:
{
  "sub": "user_12345",
  "iat": 1642665000,
  "exp": 1642751400,
  "roles": ["analyst", "report_viewer"],
  "tenantId": "tenant_001",
  "permissions": ["read:reports", "write:configurations"]
}
```

**API密钥认证**：
```yaml
# 请求头格式
X-API-Key: sk_live_abc123def456ghi789
X-API-Secret: secret_xyz789uvw456rst123

# 签名验证
X-Signature: sha256=5d41402abc4b2a76b9719d911017c592
X-Timestamp: 1642665000
```

### 3.2 权限控制模型

**RBAC权限模型**：
```json
{
  "roles": {
    "system_admin": {
      "description": "系统管理员",
      "permissions": ["*"]
    },
    "tenant_admin": {
      "description": "租户管理员",
      "permissions": [
        "read:*",
        "write:configurations",
        "write:users",
        "write:reports"
      ]
    },
    "data_analyst": {
      "description": "数据分析师",
      "permissions": [
        "read:voc-records",
        "read:reports",
        "write:reports",
        "read:configurations"
      ]
    },
    "report_viewer": {
      "description": "报表查看者",
      "permissions": [
        "read:reports",
        "export:reports"
      ]
    }
  },
  "resources": {
    "voc-records": ["read", "write", "delete"],
    "reports": ["read", "write", "delete", "export"],
    "configurations": ["read", "write", "delete"],
    "users": ["read", "write", "delete"]
  }
}
```

**权限验证中间件**：
```java
@Component
public class PermissionInterceptor implements HandlerInterceptor {
    
    @Autowired
    private JwtTokenProvider tokenProvider;
    
    @Autowired
    private PermissionService permissionService;
    
    @Override
    public boolean preHandle(HttpServletRequest request, 
                           HttpServletResponse response, 
                           Object handler) throws Exception {
        
        // 1. 获取Token
        String token = extractToken(request);
        if (token == null) {
            throw new UnauthorizedException("缺少认证信息");
        }
        
        // 2. 验证Token
        if (!tokenProvider.validateToken(token)) {
            throw new UnauthorizedException("认证信息无效");
        }
        
        // 3. 获取用户信息
        UserDetails user = tokenProvider.getUserFromToken(token);
        
        // 4. 检查权限
        String resource = extractResource(request);
        String action = extractAction(request);
        
        if (!permissionService.hasPermission(user, resource, action)) {
            throw new ForbiddenException("权限不足");
        }
        
        // 5. 设置上下文
        SecurityContextHolder.getContext().setAuthentication(
            new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities())
        );
        
        return true;
    }
}
```

### 3.3 数据安全规范

**数据脱敏规则**：
```json
{
  "dataMaskingRules": {
    "phoneNumber": {
      "pattern": "^(\\d{3})(\\d{4})(\\d{4})$",
      "replacement": "$1****$3",
      "example": "13812345678 -> 138****5678"
    },
    "email": {
      "pattern": "^([^@]+)@(.+)$",
      "replacement": "***@$2",
      "example": "<EMAIL> -> ***@example.com"
    },
    "idCard": {
      "pattern": "^(\\d{6})(\\d{8})(\\d{4})$",
      "replacement": "$1********$3",
      "example": "123456789012345678 -> 123456********5678"
    },
    "customerName": {
      "pattern": "^(.)(.+)(.)$",
      "replacement": "$1**$3",
      "example": "张三丰 -> 张*丰"
    }
  }
}
```

**敏感数据加密**：
```java
@Service
public class DataEncryptionService {
    
    private static final String ALGORITHM = "AES/GCM/NoPadding";
    private static final int GCM_IV_LENGTH = 12;
    private static final int GCM_TAG_LENGTH = 16;
    
    @Value("${encryption.key}")
    private String encryptionKey;
    
    public String encrypt(String plainText) {
        try {
            SecretKeySpec secretKey = new SecretKeySpec(
                Base64.getDecoder().decode(encryptionKey), "AES"
            );
            
            byte[] iv = new byte[GCM_IV_LENGTH];
            SecureRandom.getInstanceStrong().nextBytes(iv);
            
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            GCMParameterSpec parameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, parameterSpec);
            
            byte[] encryptedData = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            
            // 将IV和加密数据合并
            byte[] encryptedWithIv = new byte[GCM_IV_LENGTH + encryptedData.length];
            System.arraycopy(iv, 0, encryptedWithIv, 0, GCM_IV_LENGTH);
            System.arraycopy(encryptedData, 0, encryptedWithIv, GCM_IV_LENGTH, encryptedData.length);
            
            return Base64.getEncoder().encodeToString(encryptedWithIv);
        } catch (Exception e) {
            throw new EncryptionException("数据加密失败", e);
        }
    }
    
    public String decrypt(String encryptedText) {
        try {
            byte[] encryptedWithIv = Base64.getDecoder().decode(encryptedText);
            
            // 提取IV和加密数据
            byte[] iv = new byte[GCM_IV_LENGTH];
            byte[] encryptedData = new byte[encryptedWithIv.length - GCM_IV_LENGTH];
            System.arraycopy(encryptedWithIv, 0, iv, 0, GCM_IV_LENGTH);
            System.arraycopy(encryptedWithIv, GCM_IV_LENGTH, encryptedData, 0, encryptedData.length);
            
            SecretKeySpec secretKey = new SecretKeySpec(
                Base64.getDecoder().decode(encryptionKey), "AES"
            );
            
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            GCMParameterSpec parameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, parameterSpec);
            
            byte[] decryptedData = cipher.doFinal(encryptedData);
            return new String(decryptedData, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new DecryptionException("数据解密失败", e);
        }
    }
}
```

---

## 🔧 接口详细设计

### 4.1 数据接入接口

**接口概览**：
```yaml
openapi: 3.0.3
info:
  title: VOC数据接入API
  version: 1.0.0
  description: 通用VOC报表系统数据接入接口

paths:
  /api/v1/data-ingestion/tasks:
    post:
      summary: 创建数据接入任务
      tags: [数据接入]
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DataIngestionRequest'
      responses:
        '201':
          description: 任务创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataIngestionResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    get:
      summary: 查询数据接入任务列表
      tags: [数据接入]
      security:
        - bearerAuth: []
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [pending, processing, completed, failed]
        - name: industryCode
          in: query
          schema:
            type: string
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: size
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskListResponse'

  /api/v1/data-ingestion/tasks/{taskId}:
    get:
      summary: 查询数据接入任务详情
      tags: [数据接入]
      security:
        - bearerAuth: []
      parameters:
        - name: taskId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskDetailResponse'
        '404':
          description: 任务不存在

  /api/v1/data-ingestion/tasks/{taskId}/retry:
    post:
      summary: 重试失败的数据接入任务
      tags: [数据接入]
      security:
        - bearerAuth: []
      parameters:
        - name: taskId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 重试成功
        '404':
          description: 任务不存在
        '409':
          description: 任务状态不允许重试
```

**数据模型定义**：
```yaml
components:
  schemas:
    DataIngestionRequest:
      type: object
      required:
        - industryCode
        - dataSourceConfig
      properties:
        industryCode:
          type: string
          description: 行业代码
          example: automotive
        dataSourceConfig:
          $ref: '#/components/schemas/DataSourceConfig'
        processingOptions:
          $ref: '#/components/schemas/ProcessingOptions'
        metadata:
          type: object
          additionalProperties: true

    DataSourceConfig:
      type: object
      required:
        - type
      properties:
        type:
          type: string
          enum: [csv, json, excel, database, api]
        filePath:
          type: string
          description: 文件路径（文件类型数据源）
        connectionString:
          type: string
          description: 数据库连接字符串（数据库类型数据源）
        apiEndpoint:
          type: string
          description: API端点（API类型数据源）
        credentials:
          $ref: '#/components/schemas/DataSourceCredentials'
        options:
          type: object
          additionalProperties: true

    ProcessingOptions:
      type: object
      properties:
        enableValidation:
          type: boolean
          default: true
        enableCleaning:
          type: boolean
          default: true
        batchSize:
          type: integer
          minimum: 100
          maximum: 10000
          default: 1000
        parallelWorkers:
          type: integer
          minimum: 1
          maximum: 10
          default: 3

    DataIngestionResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                taskId:
                  type: string
                  description: 任务ID
                status:
                  type: string
                  enum: [pending, processing, completed, failed]
                estimatedRecordCount:
                  type: integer
                  description: 预估记录数量
                createdAt:
                  type: string
                  format: date-time

    BaseResponse:
      type: object
      required:
        - success
        - code
        - message
      properties:
        success:
          type: boolean
        code:
          type: string
        message:
          type: string
        meta:
          $ref: '#/components/schemas/ResponseMeta'

    ErrorResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            error:
              $ref: '#/components/schemas/ErrorDetail'

    ErrorDetail:
      type: object
      properties:
        type:
          type: string
          description: 错误类型
        details:
          type: array
          items:
            $ref: '#/components/schemas/ValidationError'

    ValidationError:
      type: object
      properties:
        field:
          type: string
          description: 字段名
        message:
          type: string
          description: 错误信息
        rejectedValue:
          description: 被拒绝的值
```

### 4.2 配置管理接口

**行业配置管理**：
```yaml
/api/v1/configurations/industries:
  get:
    summary: 获取支持的行业列表
    tags: [配置管理]
    responses:
      '200':
        description: 获取成功
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                data:
                  type: array
                  items:
                    $ref: '#/components/schemas/IndustryInfo'

  post:
    summary: 创建新行业配置
    tags: [配置管理]
    security:
      - bearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/IndustryConfigRequest'
    responses:
      '201':
        description: 创建成功

/api/v1/configurations/industries/{industryCode}:
  get:
    summary: 获取行业配置详情
    tags: [配置管理]
    parameters:
      - name: industryCode
        in: path
        required: true
        schema:
          type: string
      - name: version
        in: query
        schema:
          type: string
        description: 配置版本，不指定则返回最新版本
    responses:
      '200':
        description: 获取成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/BaseResponse'
                - type: object
                  properties:
                    data:
                      $ref: '#/components/schemas/IndustryConfig'

  put:
    summary: 更新行业配置
    tags: [配置管理]
    security:
      - bearerAuth: []
    parameters:
      - name: industryCode
        in: path
        required: true
        schema:
          type: string
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/IndustryConfigRequest'
    responses:
      '200':
        description: 更新成功

  delete:
    summary: 删除行业配置
    tags: [配置管理]
    security:
      - bearerAuth: []
    parameters:
      - name: industryCode
        in: path
        required: true
        schema:
          type: string
    responses:
      '204':
        description: 删除成功

/api/v1/configurations/industries/{industryCode}/validate:
  post:
    summary: 验证行业配置
    tags: [配置管理]
    security:
      - bearerAuth: []
    parameters:
      - name: industryCode
        in: path
        required: true
        schema:
          type: string
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/IndustryConfigRequest'
    responses:
      '200':
        description: 验证结果
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/BaseResponse'
                - type: object
                  properties:
                    data:
                      $ref: '#/components/schemas/ValidationResult'
```

### 4.3 报表服务接口

**报表生成接口**：
```yaml
/api/v1/reports:
  get:
    summary: 获取报表列表
    tags: [报表服务]
    security:
      - bearerAuth: []
    parameters:
      - name: type
        in: query
        schema:
          type: string
          enum: [sentiment, intent, topic, summary]
      - name: industryCode
        in: query
        schema:
          type: string
      - name: status
        in: query
        schema:
          type: string
          enum: [generating, completed, failed]
    responses:
      '200':
        description: 获取成功

  post:
    summary: 创建报表生成任务
    tags: [报表服务]
    security:
      - bearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ReportGenerationRequest'
    responses:
      '201':
        description: 任务创建成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/BaseResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        reportId:
                          type: string
                        status:
                          type: string
                        estimatedCompletionTime:
                          type: string
                          format: date-time

/api/v1/reports/{reportId}:
  get:
    summary: 获取报表详情
    tags: [报表服务]
    security:
      - bearerAuth: []
    parameters:
      - name: reportId
        in: path
        required: true
        schema:
          type: string
    responses:
      '200':
        description: 获取成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/BaseResponse'
                - type: object
                  properties:
                    data:
                      $ref: '#/components/schemas/ReportDetail'

/api/v1/reports/{reportId}/export:
  post:
    summary: 导出报表
    tags: [报表服务]
    security:
      - bearerAuth: []
    parameters:
      - name: reportId
        in: path
        required: true
        schema:
          type: string
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              format:
                type: string
                enum: [excel, pdf, csv]
              options:
                type: object
                additionalProperties: true
    responses:
      '200':
        description: 导出成功
        content:
          application/json:
            schema:
              type: object
              properties:
                downloadUrl:
                  type: string
                fileName:
                  type: string
                expiresAt:
                  type: string
                  format: date-time
```

---

## 📊 接口监控与限流

### 5.1 接口限流规则

**限流配置**：
```yaml
rateLimiting:
  rules:
    # 全局限流
    global:
      rpm: 10000  # 每分钟请求数
      rph: 600000 # 每小时请求数
      
    # 用户级限流
    perUser:
      rpm: 1000
      rph: 60000
      
    # 接口级限流
    perApi:
      data-ingestion:
        rpm: 100
        rph: 6000
        burst: 20
      reports:
        rpm: 500
        rph: 30000
        burst: 50
      configurations:
        rpm: 200
        rph: 12000
        burst: 30
        
    # 特殊用户组限流
    vipUsers:
      rpm: 5000
      rph: 300000
```

**限流实现**：
```java
@Component
public class RateLimitingInterceptor implements HandlerInterceptor {
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @Autowired
    private RateLimitingConfig rateLimitingConfig;
    
    @Override
    public boolean preHandle(HttpServletRequest request, 
                           HttpServletResponse response, 
                           Object handler) throws Exception {
        
        String userId = getCurrentUserId(request);
        String apiPath = request.getRequestURI();
        
        // 检查用户级限流
        if (!checkUserRateLimit(userId)) {
            throw new RateLimitExceededException("用户请求频率超限");
        }
        
        // 检查接口级限流
        if (!checkApiRateLimit(apiPath)) {
            throw new RateLimitExceededException("接口请求频率超限");
        }
        
        return true;
    }
    
    private boolean checkUserRateLimit(String userId) {
        String key = "rate_limit:user:" + userId + ":minute";
        String countStr = redisTemplate.opsForValue().get(key);
        
        int count = countStr != null ? Integer.parseInt(countStr) : 0;
        int limit = rateLimitingConfig.getPerUserRpm();
        
        if (count >= limit) {
            return false;
        }
        
        // 增加计数
        redisTemplate.opsForValue().increment(key);
        redisTemplate.expire(key, Duration.ofMinutes(1));
        
        return true;
    }
}
```

### 5.2 接口监控指标

**监控指标定义**：
```json
{
  "apiMetrics": {
    "performance": {
      "responseTime": {
        "p50": "50th percentile response time",
        "p95": "95th percentile response time", 
        "p99": "99th percentile response time",
        "max": "Maximum response time"
      },
      "throughput": {
        "rps": "Requests per second",
        "rpm": "Requests per minute",
        "rph": "Requests per hour"
      }
    },
    "availability": {
      "uptime": "Service uptime percentage",
      "errorRate": "Error rate percentage",
      "successRate": "Success rate percentage"
    },
    "errors": {
      "4xxErrors": "Client error count",
      "5xxErrors": "Server error count",
      "timeoutErrors": "Timeout error count",
      "connectionErrors": "Connection error count"
    },
    "business": {
      "dataIngestionRate": "Data ingestion rate",
      "analysisCompletionRate": "Analysis completion rate",
      "reportGenerationRate": "Report generation rate"
    }
  }
}
```

**监控实现**：
```java
@Component
public class ApiMetricsCollector {
    
    private final MeterRegistry meterRegistry;
    private final Timer.Sample sample;
    
    public ApiMetricsCollector(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }
    
    @EventListener
    public void handleApiRequestEvent(ApiRequestEvent event) {
        // 记录响应时间
        Timer.Sample.stop(meterRegistry.timer("api.request.duration",
            "method", event.getMethod(),
            "uri", event.getUri(),
            "status", String.valueOf(event.getStatus())
        ));
        
        // 记录请求计数
        meterRegistry.counter("api.request.count",
            "method", event.getMethod(),
            "uri", event.getUri(),
            "status", String.valueOf(event.getStatus())
        ).increment();
        
        // 记录错误计数
        if (event.getStatus() >= 400) {
            meterRegistry.counter("api.request.errors",
                "method", event.getMethod(),
                "uri", event.getUri(),
                "status", String.valueOf(event.getStatus())
            ).increment();
        }
    }
    
    @Scheduled(fixedRate = 60000) // 每分钟执行
    public void recordBusinessMetrics() {
        // 记录业务指标
        long activeIngestionTasks = getActiveIngestionTaskCount();
        meterRegistry.gauge("business.ingestion.active_tasks", activeIngestionTasks);
        
        long completedAnalyses = getCompletedAnalysisCount();
        meterRegistry.gauge("business.analysis.completed_count", completedAnalyses);
        
        long generatedReports = getGeneratedReportCount();
        meterRegistry.gauge("business.reports.generated_count", generatedReports);
    }
}
```

---

## 📋 接口测试规范

### 6.1 单元测试

**控制器测试示例**：
```java
@WebMvcTest(DataIngestionController.class)
class DataIngestionControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private DataIngestionService dataIngestionService;
    
    @Test
    void shouldCreateIngestionTaskSuccessfully() throws Exception {
        // Given
        DataIngestionRequest request = DataIngestionRequest.builder()
            .industryCode("automotive")
            .dataSourceConfig(DataSourceConfig.builder()
                .type("csv")
                .filePath("/test/data.csv")
                .build())
            .build();
        
        DataIngestionResponse expectedResponse = DataIngestionResponse.builder()
            .taskId("task_123")
            .status("pending")
            .build();
        
        when(dataIngestionService.createIngestionTask(any(DataIngestionRequest.class)))
            .thenReturn(expectedResponse);
        
        // When & Then
        mockMvc.perform(post("/api/v1/data-ingestion/tasks")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
                .header("Authorization", "Bearer " + validToken))
            .andExpect(status().isCreated())
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.data.taskId").value("task_123"))
            .andExpect(jsonPath("$.data.status").value("pending"));
    }
    
    @Test
    void shouldReturnBadRequestWhenInvalidInput() throws Exception {
        // Given
        DataIngestionRequest invalidRequest = DataIngestionRequest.builder()
            .build(); // 缺少必需字段
        
        // When & Then
        mockMvc.perform(post("/api/v1/data-ingestion/tasks")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidRequest))
                .header("Authorization", "Bearer " + validToken))
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.success").value(false))
            .andExpect(jsonPath("$.error.type").value("VALIDATION_ERROR"));
    }
}
```

### 6.2 集成测试

**接口集成测试**：
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.jpa.hibernate.ddl-auto=create-drop"
})
class DataIngestionIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Autowired
    private DataIngestionRepository repository;
    
    @Test
    void shouldCompleteDataIngestionFlow() {
        // 1. 创建接入任务
        DataIngestionRequest request = createValidRequest();
        ResponseEntity<DataIngestionResponse> createResponse = restTemplate.postForEntity(
            "/api/v1/data-ingestion/tasks", 
            request, 
            DataIngestionResponse.class
        );
        
        assertThat(createResponse.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        String taskId = createResponse.getBody().getData().getTaskId();
        
        // 2. 查询任务状态
        ResponseEntity<TaskDetailResponse> detailResponse = restTemplate.getForEntity(
            "/api/v1/data-ingestion/tasks/" + taskId,
            TaskDetailResponse.class
        );
        
        assertThat(detailResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(detailResponse.getBody().getData().getTaskId()).isEqualTo(taskId);
        
        // 3. 验证数据库状态
        Optional<DataIngestionTask> task = repository.findById(taskId);
        assertThat(task).isPresent();
        assertThat(task.get().getStatus()).isEqualTo(TaskStatus.PENDING);
    }
}
```

### 6.3 性能测试

**压力测试配置**：
```yaml
# JMeter测试计划
TestPlan:
  name: "VOC API Performance Test"
  threadGroups:
    - name: "Data Ingestion Load Test"
      threads: 100
      rampUp: 60
      duration: 300
      requests:
        - name: "Create Ingestion Task"
          method: POST
          path: "/api/v1/data-ingestion/tasks"
          assertions:
            - responseTime: 3000
            - httpCode: 201
            
    - name: "Report Generation Load Test"
      threads: 50
      rampUp: 30
      duration: 600
      requests:
        - name: "Generate Report"
          method: POST
          path: "/api/v1/reports"
          assertions:
            - responseTime: 5000
            - httpCode: 201
```

---

**文档维护**: 接口架构师、API开发团队  
**审核**: 技术负责人、安全专家  
**下次更新**: 接口设计变更时及时更新 