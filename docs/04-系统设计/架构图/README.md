# 通用VOC报表系统架构图

本目录包含了通用VOC报表系统的各种架构图，提供了两种格式：

## 文件格式

### DrawIO格式 (.drawio)
- 使用Draw.io或diagrams.net工具打开和编辑
- 支持详细的图形样式和布局
- 适合复杂的技术架构图

### Markdown格式 (.md)
- 使用Markdown编辑器或支持Mermaid的工具查看
- 包含Mermaid图表，支持GitHub、GitLab等平台直接渲染
- 文本格式，便于版本控制和在线查看

## 架构图列表

### 1. 系统架构图
- **文件**: `系统架构图.drawio` / `系统架构图.md`
- **描述**: 展示整个系统的分层架构，从用户接入层到基础设施层
- **特点**: 
  - 用户接入层：多行业用户界面
  - 网关接入层：统一API网关和负载均衡
  - AI智能配置管理层：零代码配置和动态规则
  - 业务服务层：跨行业统一服务
  - AI分析处理层：大模型分析和智能处理
  - 数据存储层：分层数据存储
  - 基础设施层：云原生基础设施

### 2. 模块架构图
- **文件**: `模块架构图.drawio` / `模块架构图.md`
- **描述**: 详细展示各个功能模块的内部结构和关系
- **特点**:
  - 数据接入与智能识别模块
  - AI智能配置管理模块
  - AI分析处理与行业适配模块
  - 动态规则引擎与配置管理模块
  - 统一报表与可视化模块
  - 分层数据存储模块
  - 系统管理与智能监控模块
  - 业务流程与扩展模块

### 3. 网络架构图
- **文件**: `网络架构图.drawio` / `网络架构图.md`
- **描述**: 展示网络拓扑和安全架构
- **特点**:
  - 互联网接入区域
  - 智能边界防护层
  - 多云统一VPC网络
  - 公网接入子网
  - DMZ隔离子网
  - AI服务子网
  - K8s容器网络集群
  - 分层数据存储子网
  - 智能监控管理子网

### 4. 部署架构图
- **文件**: `部署架构图.drawio` / `部署架构图.md`
- **描述**: 展示系统部署和运维架构
- **特点**:
  - 公网接入区域
  - DMZ区域
  - Kubernetes集群
  - 数据存储层
  - 监控运维层
  - 外部服务集成

### 5. 详细完整流程图
- **文件**: `通用VOC报表系统详细完整流程图.drawio` / `通用VOC报表系统详细完整流程图.md`
- **描述**: 展示完整的业务流程，包含前端接口调用
- **特点**:
  - 数据源层
  - 智能配置管理层
  - AI智能分析处理层
  - 数据存储层
  - 跨行业统一业务服务层
  - 跨行业统一报表展示层
  - 前端Web层
  - 监控告警层

## 使用方法

### 查看DrawIO文件
1. 使用Draw.io (https://app.diagrams.net/)
2. 打开对应的.drawio文件
3. 可以查看、编辑和导出

### 查看Markdown文件
1. 在支持Mermaid的Markdown编辑器中打开
2. 使用在线Mermaid编辑器 (https://mermaid.live/)
3. 在GitHub/GitLab等平台直接查看

### 在GitHub中查看
```markdown
```mermaid
graph TB
    %% 这里粘贴.mermaid文件的内容
```
```

## 技术特点

### 跨行业统一
- 支持汽车、星巴克、政府、手机、美妆等多个行业
- 统一的架构设计，降低开发和维护成本

### AI驱动
- AI配置识别引擎自动分析数据特征
- 大模型多维度并行分析
- 智能优化和成本控制

### 零代码配置
- 可视化配置编辑
- 实时预览效果
- 热更新支持

### 云原生架构
- Kubernetes容器编排
- 微服务架构
- 弹性扩展能力

### 智能监控
- 全链路监控
- 智能告警
- 故障自愈

## 更新说明

- 2024-01-20: 创建初始架构图
- 修复了XML实体引用错误
- 优化了文字定位和显示效果
- 添加了Markdown格式支持，包含Mermaid图表
- 删除了单独的.mermaid文件，整合到.md文件中

## 注意事项

1. DrawIO文件包含详细的样式信息，适合详细查看和编辑
2. Markdown文件包含Mermaid图表，便于版本控制和在线查看
3. 两种格式内容保持一致，可以根据需要选择使用
4. 建议在修改架构时同时更新两种格式
5. Markdown文件在GitHub、GitLab等平台可以直接渲染Mermaid图表
