<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" version="28.0.7">
  <diagram name="VOC模块架构图" id="voc-module-architecture">
    <mxGraphModel dx="1378" dy="769" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="2054" pageHeight="1569" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="title" value="通用VOC报表系统 - 跨行业模块架构图" style="text;strokeColor=none;fillColor=none;html=1;fontSize=24;fontStyle=1;verticalAlign=middle;align=center;fontColor=#2c3e50;" parent="1" vertex="1">
          <mxGeometry x="400" width="554" height="40" as="geometry" />
        </mxCell>
        <mxCell id="data-ingestion-module" value="统一数据接入与智能识别模块 Unified Data Ingestion &amp;amp; Smart Recognition Module" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=16;fontStyle=1;verticalAlign=top;align=center;textOpacity=80;" parent="1" vertex="1">
          <mxGeometry x="60" y="80" width="650" height="215" as="geometry" />
        </mxCell>
        <mxCell id="format-adapters" value="多格式适配器集群" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="80" y="110" width="120" height="25" as="geometry" />
        </mxCell>
        <mxCell id="csv-adapter" value="CSV/Excel适配器&#xa;智能列识别&#xa;格式自动推断" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="80" y="140" width="110" height="60" as="geometry" />
        </mxCell>
        <mxCell id="api-adapter" value="API适配器&#xa;REST/GraphQL&#xa;动态Schema解析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="210" y="140" width="110" height="60" as="geometry" />
        </mxCell>
        <mxCell id="db-adapter" value="数据库适配器&#xa;MySQL/PostgreSQL&#xa;多数据源连接池" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="340" y="140" width="110" height="60" as="geometry" />
        </mxCell>
        <mxCell id="stream-adapter" value="流数据适配器&#xa;Kafka/Pulsar&#xa;实时数据接入" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="470" y="140" width="110" height="60" as="geometry" />
        </mxCell>
        <mxCell id="file-adapter" value="文件适配器&#xa;JSON/XML/TXT&#xa;智能编码检测" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="600" y="140" width="110" height="60" as="geometry" />
        </mxCell>
        <mxCell id="data-processors" value="智能数据处理引擎" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="80" y="210" width="140" height="25" as="geometry" />
        </mxCell>
        <mxCell id="ai-field-mapper" value="AI字段映射器&#xa;智能字段匹配&#xa;语义理解" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="80" y="235" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="quality-checker" value="质量检查器&#xa;数据质量评估&#xa;异常检测" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="220" y="235" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="data-cleaner" value="智能清洗器&#xa;缺失值填充&#xa;异常值处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="360" y="235" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="data-transformer" value="数据转换器&#xa;格式标准化&#xa;类型转换" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="500" y="235" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ai-config-module" value="AI智能配置管理模块 AI-Driven Configuration Management Module" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;fontSize=16;fontStyle=1;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="750" y="80" width="650" height="200" as="geometry" />
        </mxCell>
        <mxCell id="ai-recognition-engine" value="AI配置识别引擎&#xa;数据特征自动识别&#xa;行业模式智能推荐" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#2e7d32;" parent="1" vertex="1">
          <mxGeometry x="780" y="110" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="pattern-analyzer" value="模式分析器&#xa;数据模式识别&#xa;业务场景推断" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#2e7d32;" parent="1" vertex="1">
          <mxGeometry x="940" y="110" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="template-recommender" value="模板推荐器&#xa;行业模板匹配&#xa;相似度计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#2e7d32;" parent="1" vertex="1">
          <mxGeometry x="1100" y="110" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="config-generator" value="配置生成器&#xa;自动配置生成&#xa;最佳实践应用" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#2e7d32;" parent="1" vertex="1">
          <mxGeometry x="1260" y="110" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="nocode-platform" value="零代码配置平台&#xa;可视化配置编辑&#xa;拖拽式界面设计" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#a5d6a7;strokeColor=#388e3c;" parent="1" vertex="1">
          <mxGeometry x="780" y="200" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="visual-editor" value="可视化编辑器&#xa;组件拖拽&#xa;实时预览" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#a5d6a7;strokeColor=#388e3c;" parent="1" vertex="1">
          <mxGeometry x="940" y="200" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="template-designer" value="模板设计器&#xa;模板创建编辑&#xa;复用管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#a5d6a7;strokeColor=#388e3c;" parent="1" vertex="1">
          <mxGeometry x="1100" y="200" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="config-validator" value="配置验证器&#xa;规则冲突检测&#xa;依赖关系验证" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#a5d6a7;strokeColor=#388e3c;" parent="1" vertex="1">
          <mxGeometry x="1260" y="200" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="ai-analysis-module" value="AI智能分析处理模块 AI Analysis &amp; Processing Module" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;fontSize=16;fontStyle=1;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="50" y="320" width="650" height="200" as="geometry" />
        </mxCell>
        <mxCell id="llm-analysis-engine" value="大模型分析引擎&#xa;火山大模型集成&#xa;多维度并行分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc02;strokeColor=#ff6f00;" parent="1" vertex="1">
          <mxGeometry x="80" y="350" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="sentiment-engine" value="情感分析引擎&#xa;细粒度情感识别&#xa;情感强度评分" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" parent="1" vertex="1">
          <mxGeometry x="240" y="350" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="intent-engine" value="意图识别引擎&#xa;用户意图分类&#xa;行为预测" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" parent="1" vertex="1">
          <mxGeometry x="380" y="350" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="topic-engine" value="主题分类引擎&#xa;动态主题发现&#xa;层次化分类" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" parent="1" vertex="1">
          <mxGeometry x="520" y="350" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="result-processor" value="结果处理优化器&#xa;质量检查・置信度评估&#xa;成本控制・缓存优化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ff9800;strokeColor=#e65100;" parent="1" vertex="1">
          <mxGeometry x="80" y="440" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="batch-processor" value="批量处理器&#xa;大规模数据处理&#xa;并行计算调度" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ff9800;strokeColor=#e65100;" parent="1" vertex="1">
          <mxGeometry x="250" y="440" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="realtime-processor" value="实时处理器&#xa;流式数据分析&#xa;低延迟响应" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ff9800;strokeColor=#e65100;" parent="1" vertex="1">
          <mxGeometry x="420" y="440" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="industry-adapter-module" value="跨行业适配模块 Cross-Industry Adaptation Module" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=16;fontStyle=1;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="750" y="320" width="650" height="200" as="geometry" />
        </mxCell>
        <mxCell id="auto-adapter" value="汽车行业适配器&#xa;车型・配置・客户&#xa;DMS系统集成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" parent="1" vertex="1">
          <mxGeometry x="780" y="350" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="starbucks-adapter" value="星巴克餐饮适配器&#xa;门店・产品・服务&#xa;POS系统集成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" parent="1" vertex="1">
          <mxGeometry x="920" y="350" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="gov-adapter" value="政府信访适配器&#xa;案件・流程・绩效&#xa;OA系统集成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" parent="1" vertex="1">
          <mxGeometry x="1060" y="350" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="phone-adapter" value="手机通讯适配器&#xa;用户・产品・服务&#xa;CRM系统集成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" parent="1" vertex="1">
          <mxGeometry x="1200" y="350" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cosmetic-adapter" value="美妆行业适配器&#xa;产品・用户・渠道&#xa;电商系统集成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" parent="1" vertex="1">
          <mxGeometry x="780" y="430" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="custom-adapter" value="自定义行业适配器&#xa;灵活配置&#xa;快速扩展" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" parent="1" vertex="1">
          <mxGeometry x="920" y="430" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="business-abstraction" value="统一业务抽象层&#xa;标准化接口&#xa;业务逻辑封装" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#81c784;strokeColor=#2e7d32;" parent="1" vertex="1">
          <mxGeometry x="1060" y="430" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="config-router" value="配置路由器&#xa;动态配置分发&#xa;多租户隔离" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#81c784;strokeColor=#2e7d32;" parent="1" vertex="1">
          <mxGeometry x="1220" y="430" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rules-config-module" value="动态规则引擎与配置管理模块 Dynamic Rules Engine &amp; Configuration Module" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=16;fontStyle=1;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="50" y="550" width="650" height="210" as="geometry" />
        </mxCell>
        <mxCell id="dynamic-rules-engine" value="动态规则引擎&#xa;规则实时更新&#xa;热加载机制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="80" y="590" width="130" height="60" as="geometry" />
        </mxCell>
        <mxCell id="sentiment-rules" value="情感分析规则&#xa;行业情感词典&#xa;上下文语义规则" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;" parent="1" vertex="1">
          <mxGeometry x="230" y="590" width="110" height="60" as="geometry" />
        </mxCell>
        <mxCell id="intent-rules" value="意图识别规则&#xa;行为模式匹配&#xa;用户画像规则" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;" parent="1" vertex="1">
          <mxGeometry x="360" y="590" width="110" height="60" as="geometry" />
        </mxCell>
        <mxCell id="topic-rules" value="主题分类规则&#xa;层次分类树&#xa;动态标签规则" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;" parent="1" vertex="1">
          <mxGeometry x="490" y="590" width="110" height="60" as="geometry" />
        </mxCell>
        <mxCell id="priority-rules" value="优先级规则&#xa;业务重要性评级&#xa;紧急程度计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;" parent="1" vertex="1">
          <mxGeometry x="620" y="590" width="110" height="60" as="geometry" />
        </mxCell>
        <mxCell id="config-center" value="统一配置中心&#xa;配置版本管理&#xa;环境隔离" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f4c3;strokeColor=#827717;" parent="1" vertex="1">
          <mxGeometry x="80" y="670" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="hot-reload-engine" value="热更新引擎&#xa;配置实时生效&#xa;平滑切换" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f4c3;strokeColor=#827717;" parent="1" vertex="1">
          <mxGeometry x="220" y="670" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="approval-workflow" value="审批工作流&#xa;配置变更审批&#xa;风险评估" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f4c3;strokeColor=#827717;" parent="1" vertex="1">
          <mxGeometry x="360" y="670" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rollback-manager" value="回滚管理器&#xa;配置回滚&#xa;快照恢复" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f4c3;strokeColor=#827717;" parent="1" vertex="1">
          <mxGeometry x="500" y="670" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="report-visualization-module" value="统一报表与可视化模块 Unified Report &amp; Visualization Module" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e0f2f1;strokeColor=#00695c;fontSize=16;fontStyle=1;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="750" y="550" width="650" height="210" as="geometry" />
        </mxCell>
        <mxCell id="report-engine" value="配置驱动报表引擎&#xa;模板化报表生成&#xa;多格式输出" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b2dfdb;strokeColor=#00695c;" parent="1" vertex="1">
          <mxGeometry x="780" y="590" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="chart-generator" value="智能图表生成器&#xa;自动图表推荐&#xa;交互式可视化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b2dfdb;strokeColor=#00695c;" parent="1" vertex="1">
          <mxGeometry x="940" y="590" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dashboard-builder" value="仪表板构建器&#xa;拖拽式布局&#xa;响应式设计" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b2dfdb;strokeColor=#00695c;" parent="1" vertex="1">
          <mxGeometry x="1100" y="590" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="export-manager" value="多格式导出器&#xa;PDF・Excel・图片&#xa;定时导出任务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b2dfdb;strokeColor=#00695c;" parent="1" vertex="1">
          <mxGeometry x="1260" y="590" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ui-components" value="可复用UI组件库&#xa;跨行业组件标准化&#xa;主题定制・品牌适配" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4db6ac;strokeColor=#00695c;" parent="1" vertex="1">
          <mxGeometry x="780" y="670" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="template-renderer" value="模板渲染引擎&#xa;动态模板解析&#xa;数据绑定・条件渲染" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4db6ac;strokeColor=#00695c;" parent="1" vertex="1">
          <mxGeometry x="950" y="670" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="interactive-processor" value="交互处理器&#xa;用户操作响应&#xa;实时数据更新" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4db6ac;strokeColor=#00695c;" parent="1" vertex="1">
          <mxGeometry x="1120" y="670" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="storage-module" value="分层数据存储模块 Layered Data Storage Module" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=16;fontStyle=1;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="50" y="800" width="650" height="200" as="geometry" />
        </mxCell>
        <mxCell id="data-warehouse-manager" value="数据仓库管理器&#xa;StarRocks集群管理&#xa;分区・索引优化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="80" y="830" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ods-manager" value="ODS层管理器&#xa;原始数据存储&#xa;数据分区・压缩" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;" parent="1" vertex="1">
          <mxGeometry x="240" y="830" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dwd-manager" value="DWD层管理器&#xa;明细数据层&#xa;数据清洗・标准化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;" parent="1" vertex="1">
          <mxGeometry x="380" y="830" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dws-manager" value="DWS层管理器&#xa;汇总数据层&#xa;预聚合・指标计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;" parent="1" vertex="1">
          <mxGeometry x="520" y="830" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cache-manager" value="分布式缓存管理器&#xa;Redis集群管理&#xa;热点数据・会话缓存" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcdd2;strokeColor=#d32f2f;" parent="1" vertex="1">
          <mxGeometry x="80" y="910" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="object-storage-manager" value="对象存储管理器&#xa;MinIO集群管理&#xa;文件・备份・归档" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcdd2;strokeColor=#d32f2f;" parent="1" vertex="1">
          <mxGeometry x="240" y="910" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="lineage-tracker" value="数据血缘追踪器&#xa;血缘关系管理&#xa;影响分析・依赖管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcdd2;strokeColor=#d32f2f;" parent="1" vertex="1">
          <mxGeometry x="400" y="910" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="system-management-module" value="系统管理与智能监控模块 System Management &amp; Smart Monitoring Module" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=16;fontStyle=1;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="750" y="800" width="650" height="200" as="geometry" />
        </mxCell>
        <mxCell id="user-manager" value="用户管理器&#xa;多租户用户管理&#xa;角色・权限控制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="780" y="830" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="auth-manager" value="认证管理器&#xa;SSO・LDAP集成&#xa;多因子认证" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="920" y="830" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="permission-manager" value="权限管理器&#xa;细粒度权限控制&#xa;数据行级权限" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="1060" y="830" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="audit-logger" value="审计日志器&#xa;操作日志记录&#xa;合规性审计" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="1200" y="830" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="intelligent-monitor" value="智能监控管理器&#xa;多层次监控体系&#xa;异常智能检测" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="780" y="910" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="alert-manager" value="智能告警管理器&#xa;告警规则引擎&#xa;多渠道告警推送" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="940" y="910" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="auto-ops" value="自动化运维器&#xa;故障自愈・性能优化&#xa;容量规划・扩缩容" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="1100" y="910" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="workflow-extension-module" value="业务流程与扩展模块 Business Workflow &amp; Extension Module" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;fontSize=16;fontStyle=1;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="50" y="1040" width="1350" height="150" as="geometry" />
        </mxCell>
        <mxCell id="workflow-engine" value="工作流引擎&#xa;业务流程编排&#xa;状态机管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;" parent="1" vertex="1">
          <mxGeometry x="80" y="1080" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="task-scheduler" value="任务调度器&#xa;定时任务管理&#xa;依赖调度・重试机制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;" parent="1" vertex="1">
          <mxGeometry x="220" y="1080" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="notification-engine" value="通知引擎&#xa;多渠道消息推送&#xa;模板化・个性化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;" parent="1" vertex="1">
          <mxGeometry x="360" y="1080" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="api-manager" value="API管理器&#xa;接口版本控制&#xa;限流・熔断・监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f4c3;strokeColor=#827717;" parent="1" vertex="1">
          <mxGeometry x="520" y="1080" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="integration-hub" value="集成中心&#xa;第三方系统集成&#xa;数据同步・协议适配" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f4c3;strokeColor=#827717;" parent="1" vertex="1">
          <mxGeometry x="660" y="1080" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="plugin-manager" value="插件管理器&#xa;功能扩展机制&#xa;热插拔・版本管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f4c3;strokeColor=#827717;" parent="1" vertex="1">
          <mxGeometry x="800" y="1080" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="open-platform" value="开放平台&#xa;SDK・API文档&#xa;开发者生态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;" parent="1" vertex="1">
          <mxGeometry x="940" y="1080" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="marketplace" value="应用市场&#xa;第三方应用商店&#xa;组件・模板・插件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;" parent="1" vertex="1">
          <mxGeometry x="1080" y="1080" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="developer-tools" value="开发者工具&#xa;调试・测试・部署&#xa;CLI・IDE插件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;" parent="1" vertex="1">
          <mxGeometry x="1220" y="1080" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ingestion-to-ai-config" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#1976d2;" parent="1" source="ai-field-mapper" target="ai-recognition-engine" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ai-config-to-analysis" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#388e3c;" parent="1" source="config-generator" target="llm-analysis-engine" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1330" y="300" />
              <mxPoint x="150" y="300" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="analysis-to-industry" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#f57c00;" parent="1" source="result-processor" target="auto-adapter" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="industry-to-rules" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#4caf50;" parent="1" source="business-abstraction" target="dynamic-rules-engine" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1130" y="540" />
              <mxPoint x="145" y="540" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="rules-to-report" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#d6b656;" parent="1" source="hot-reload-engine" target="report-engine" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="report-to-storage" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#00695c;" parent="1" source="chart-generator" target="data-warehouse-manager" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1010" y="780" />
              <mxPoint x="150" y="780" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="storage-to-system" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#6c8ebf;" parent="1" source="lineage-tracker" target="intelligent-monitor" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="system-to-workflow" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#666666;" parent="1" source="alert-manager" target="workflow-engine" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1010" y="1020" />
              <mxPoint x="140" y="1020" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="module-features" value="跨行业统一・AI驱动・零代码配置・热更新・组件复用・智能监控・开放扩展" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#f9a825;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="100" y="50" width="1200" height="20" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
