<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" version="28.0.7">
  <diagram name="VOC部署架构图" id="voc-deployment-architecture">
    <mxGraphModel dx="1378" dy="769" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="2054" pageHeight="1569" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="title" value="通用VOC报表系统 - 跨行业统一部署架构图" style="text;strokeColor=none;fillColor=none;html=1;fontSize=24;fontStyle=1;verticalAlign=middle;align=center;fontColor=#2c3e50;" parent="1" vertex="1">
          <mxGeometry x="750" y="20" width="554" height="40" as="geometry" />
        </mxCell>
        <mxCell id="public-zone" value="公网接入区域 Public Access Zone" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=16;fontStyle=1;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="50" y="80" width="1950" height="120" as="geometry" />
        </mxCell>
        <mxCell id="global-cdn" value="全球CDN节点&#xa;阿里云・腾讯云・AWS&#xa;多云CDN加速" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="100" y="110" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="intelligent-dns" value="智能DNS&#xa;阿里云DNS・腾讯云DNSPod&#xa;地理位置路由" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="280" y="110" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="waf-security" value="Web应用防火墙&#xa;阿里云WAF・腾讯云WAF&#xa;AI威胁检测" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="460" y="110" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ssl-certificate" value="SSL证书管理&#xa;Let&#39;s Encrypt・阿里云SSL&#xa;自动续期" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="640" y="110" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="api-security-gateway" value="API安全网关&#xa;Kong・APISIX&#xa;认证・限流・监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="820" y="110" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="industry-portals" value="行业门户集群&#xa;汽车・星巴克・政府・手机・美妆&#xa;多租户隔离部署" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="1000" y="110" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="enterprise-gateway" value="企业专网网关&#xa;VPN・专线接入&#xa;混合云连接" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="1180" y="110" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dmz-zone" value="DMZ区域 DMZ Zone" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=16;fontStyle=1;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="50" y="250" width="1550" height="150" as="geometry" />
        </mxCell>
        <mxCell id="api-gateway-cluster" value="API网关集群&#xa;Spring Cloud Gateway&#xa;2个实例" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="100" y="290" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="nginx-cluster" value="Nginx集群&#xa;反向代理&#xa;2个实例" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="300" y="290" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bastion-host" value="堡垒机&#xa;运维跳板&#xa;安全接入" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="500" y="290" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="vpn-gateway" value="VPN网关&#xa;企业接入&#xa;专线连接" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="700" y="290" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="k8s-cluster" value="Kubernetes集群 K8s Cluster" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=16;fontStyle=1;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="50" y="450" width="800" height="300" as="geometry" />
        </mxCell>
        <mxCell id="master-nodes" value="Master节点 (3个)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="80" y="490" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="master1" value="Master-1&#xa;4C8G&#xa;etcd + kube-apiserver" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="80" y="540" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="master2" value="Master-2&#xa;4C8G&#xa;kube-scheduler" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="220" y="540" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="master3" value="Master-3&#xa;4C8G&#xa;kube-controller" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="360" y="540" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="worker-nodes" value="Worker节点 (6个)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="520" y="490" width="300" height="40" as="geometry" />
        </mxCell>
        <mxCell id="worker1" value="Worker-1&#xa;8C16G&#xa;业务服务节点" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="520" y="540" width="90" height="60" as="geometry" />
        </mxCell>
        <mxCell id="worker2" value="Worker-2&#xa;8C16G&#xa;业务服务节点" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="620" y="540" width="90" height="60" as="geometry" />
        </mxCell>
        <mxCell id="worker3" value="Worker-3&#xa;16C32G&#xa;AI分析节点" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="730" y="540" width="90" height="60" as="geometry" />
        </mxCell>
        <mxCell id="worker4" value="Worker-4&#xa;8C16G&#xa;报表服务节点" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="520" y="620" width="90" height="60" as="geometry" />
        </mxCell>
        <mxCell id="worker5" value="Worker-5&#xa;4C8G&#xa;配置服务节点" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="620" y="620" width="90" height="60" as="geometry" />
        </mxCell>
        <mxCell id="worker6" value="Worker-6&#xa;4C8G&#xa;监控服务节点" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="730" y="620" width="90" height="60" as="geometry" />
        </mxCell>
        <mxCell id="pod-deployment" value="应用Pod部署示意" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="80" y="620" width="400" height="40" as="geometry" />
        </mxCell>
        <mxCell id="ingestion-pods" value="数据接入服务&#xa;3个Pod" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="80" y="680" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="analysis-pods" value="分析服务&#xa;5个Pod" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="180" y="680" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="report-pods" value="报表服务&#xa;3个Pod" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="280" y="680" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="config-pods" value="配置服务&#xa;2个Pod" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="380" y="680" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="data-storage-zone" value="数据存储区域 Data Storage Zone" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=16;fontStyle=1;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="900" y="450" width="700" height="300" as="geometry" />
        </mxCell>
        <mxCell id="postgresql-cluster" value="PostgreSQL集群&#xa;主从复制&#xa;16C32G * 3" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="930" y="490" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="clickhouse-cluster" value="ClickHouse集群&#xa;分布式分析&#xa;16C32G * 4" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="1080" y="490" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="redis-cluster" value="Redis集群&#xa;主从哨兵&#xa;8C16G * 6" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="1230" y="490" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="kafka-cluster" value="Kafka集群&#xa;消息队列&#xa;8C16G * 3" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="1380" y="490" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="elasticsearch-cluster" value="Elasticsearch集群&#xa;搜索引擎&#xa;16C32G * 3" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="930" y="570" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="minio-cluster" value="MinIO集群&#xa;对象存储&#xa;8C16G * 4" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="1080" y="570" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="influxdb-cluster" value="InfluxDB集群&#xa;时序数据库&#xa;8C16G * 2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="1230" y="570" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="nas-storage" value="NAS存储&#xa;共享文件系统&#xa;100TB" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="1380" y="570" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="backup-area" value="备份区域&#xa;异地备份&#xa;冷存储" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="930" y="650" width="570" height="60" as="geometry" />
        </mxCell>
        <mxCell id="monitoring-zone" value="监控运维区域 Monitoring Zone" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=16;fontStyle=1;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="50" y="800" width="1550" height="150" as="geometry" />
        </mxCell>
        <mxCell id="prometheus" value="Prometheus&#xa;指标采集&#xa;4C8G * 2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="100" y="840" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="grafana" value="Grafana&#xa;监控面板&#xa;4C8G * 2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="250" y="840" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="elk-stack" value="ELK Stack&#xa;日志分析&#xa;16C32G * 3" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="400" y="840" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="jaeger" value="Jaeger&#xa;链路追踪&#xa;8C16G * 2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="550" y="840" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="alertmanager" value="AlertManager&#xa;告警管理&#xa;4C8G * 2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="700" y="840" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="jenkins" value="Jenkins&#xa;CI/CD&#xa;8C16G * 1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="850" y="840" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="harbor" value="Harbor&#xa;镜像仓库&#xa;8C16G * 1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="1000" y="840" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="sonarqube" value="SonarQube&#xa;代码质量&#xa;4C8G * 1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="1150" y="840" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="external-zone" value="外部服务区域 External Services Zone" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=16;fontStyle=1;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="50" y="1000" width="1550" height="100" as="geometry" />
        </mxCell>
        <mxCell id="openai-service" value="OpenAI服务&#xa;大语言模型&#xa;API调用" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="100" y="1030" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="aliyun-services" value="阿里云服务&#xa;短信・邮件・OSS&#xa;云服务集成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="300" y="1030" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="ldap-service" value="企业LDAP&#xa;身份认证&#xa;SSO集成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="500" y="1030" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="third-party-crm" value="第三方CRM&#xa;数据源集成&#xa;API对接" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="700" y="1030" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="public-to-dmz" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="api-gateway-cluster" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dmz-to-k8s" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="api-gateway-cluster" target="worker1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="k8s-to-storage" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="worker2" target="postgresql-cluster" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="monitoring-connection" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="worker6" target="prometheus" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="network-annotations" value="网络配置说明" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fffacd;strokeColor=#d6b656;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1350" y="290" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="vpc-config" value="VPC: 10.0.0.0/16&#xa;公网子网: 10.0.1.0/24&#xa;DMZ子网: 10.0.2.0/24&#xa;K8s子网: 10.0.10.0/22&#xa;数据子网: 10.0.20.0/24&#xa;管理子网: 10.0.30.0/24" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fffacd;strokeColor=#d6b656;align=left;" parent="1" vertex="1">
          <mxGeometry x="1350" y="340" width="200" height="100" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
