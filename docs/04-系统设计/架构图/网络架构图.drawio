<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" version="28.0.7">
  <diagram name="VOC网络架构图" id="voc-network-architecture">
    <mxGraphModel dx="1378" dy="769" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1854" pageHeight="1469" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="title" value="通用VOC报表系统 - 跨行业统一网络架构图" style="text;strokeColor=none;fillColor=none;html=1;fontSize=24;fontStyle=1;verticalAlign=middle;align=center;fontColor=#2c3e50;" parent="1" vertex="1">
          <mxGeometry x="650" y="20" width="554" height="40" as="geometry" />
        </mxCell>
        <mxCell id="internet-zone" value="互联网接入区域 Internet Access Zone" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=16;fontStyle=1;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="50" y="80" width="1750" height="100" as="geometry" />
        </mxCell>
        <mxCell id="user-browser" value="用户浏览器&#xa;多行业用户&#xa;Web控制台" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="120" y="110" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="mobile-app-client" value="移动应用客户端&#xa;跨平台支持&#xa;iOS/Android" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="280" y="110" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="industry-systems" value="行业专业系统&#xa;CRM・ERP・DMS&#xa;POS・OA系统" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="440" y="110" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="ai-model-services" value="AI模型服务&#xa;火山・OpenAI・Claude&#xa;大模型API接入" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="600" y="110" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="third-party-data" value="第三方数据源&#xa;社交媒体・调研平台&#xa;行业数据接口" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="760" y="110" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="enterprise-network" value="企业专网&#xa;VPN・专线接入&#xa;混合云连接" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="920" y="110" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="cdn-edge" value="全球CDN节点&#xa;静态资源加速&#xa;边缘计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="1080" y="110" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="security-boundary" value="智能边界防护层 Intelligent Security Boundary" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#d79b00;fontSize=16;fontStyle=1;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="50" y="210" width="1750" height="100" as="geometry" />
        </mxCell>
        <mxCell id="ddos-protection" value="DDoS防护&#xa;流量清洗・攻击识别&#xa;100Gbps防护能力" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="100" y="240" width="130" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ai-waf" value="AI智能WAF&#xa;Web应用防护&#xa;机器学习威胁检测" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="260" y="240" width="130" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ssl-termination" value="SSL/TLS卸载&#xa;证书自动管理&#xa;TLS 1.3・HSTS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="420" y="240" width="130" height="60" as="geometry" />
        </mxCell>
        <mxCell id="intelligent-rate-limiting" value="智能流控&#xa;动态QPS控制&#xa;行业差异化限流" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="580" y="240" width="130" height="60" as="geometry" />
        </mxCell>
        <mxCell id="api-security" value="API安全网关&#xa;接口鉴权・加密&#xa;API令牌管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="740" y="240" width="130" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zero-trust" value="零信任网络&#xa;身份验证・授权&#xa;最小权限原则" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="900" y="240" width="130" height="60" as="geometry" />
        </mxCell>
        <mxCell id="threat-intelligence" value="威胁情报&#xa;实时威胁检测&#xa;行为分析・预警" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="1060" y="240" width="130" height="60" as="geometry" />
        </mxCell>
        <mxCell id="multi-cloud-vpc" value="多云统一VPC网络 Multi-Cloud Unified VPC (10.0.0.0/12)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=16;fontStyle=1;verticalAlign=middle;align=center;" parent="1" vertex="1">
          <mxGeometry x="50" y="340" width="1750" height="750" as="geometry" />
        </mxCell>
        <mxCell id="public-subnet" value="公网接入子网 Public Access Subnet (10.0.1.0/24)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="100" y="380" width="1650" height="80" as="geometry" />
        </mxCell>
        <mxCell id="nat-gateway-cluster" value="NAT网关集群&#xa;10.0.1.10-12&#xa;高可用出网代理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="130" y="410" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="slb-cluster" value="负载均衡集群&#xa;10.0.1.20-22&#xa;智能流量分发" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="280" y="410" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="vpn-gateway-ha" value="VPN网关HA&#xa;10.0.1.30-31&#xa;企业专线冗余" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="430" y="410" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="api-gateway-cluster" value="API网关集群&#xa;10.0.1.40-42&#xa;跨行业统一入口" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="580" y="410" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="config-gateway" value="配置网关&#xa;10.0.1.50-51&#xa;热更新配置分发" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="730" y="410" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="industry-gateway" value="行业网关&#xa;*********-65&#xa;多租户隔离" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="880" y="410" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="eip-pool-extended" value="弹性IP池&#xa;120.78.xx.xx&#xa;50个IP・智能调度" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="1030" y="410" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="dmz-subnet" value="DMZ隔离子网 DMZ Isolation Subnet (********/22)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=14;fontStyle=1;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="100" y="490" width="800" height="100" as="geometry" />
        </mxCell>
        <mxCell id="nginx-proxy-cluster" value="Nginx代理集群&#xa;*********-13&#xa;反向代理・SSL终端" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="130" y="520" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="bastion-cluster" value="堡垒机集群&#xa;*********-21&#xa;安全运维跳板" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="280" y="520" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="log-collector-cluster" value="日志收集集群&#xa;10.0.2.30-32&#xa;多行业日志聚合" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="430" y="520" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="monitoring-proxy" value="监控代理集群&#xa;10.0.2.40-41&#xa;指标采集・数据转发" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="580" y="520" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="config-proxy" value="配置代理&#xa;10.0.2.50&#xa;配置热更新中继" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="730" y="520" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="ai-service-subnet" value="AI服务子网 AI Service Subnet (10.0.3.0/22)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=14;fontStyle=1;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="950" y="490" width="800" height="100" as="geometry" />
        </mxCell>
        <mxCell id="ai-gateway" value="AI模型网关&#xa;10.0.3.10-11&#xa;多模型负载均衡" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;" parent="1" vertex="1">
          <mxGeometry x="980" y="520" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="ai-config-service" value="AI配置服务&#xa;10.0.3.20-22&#xa;智能配置识别" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;" parent="1" vertex="1">
          <mxGeometry x="1130" y="520" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="ai-analysis-cluster" value="AI分析集群&#xa;10.0.3.30-35&#xa;多维度并行分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;" parent="1" vertex="1">
          <mxGeometry x="1280" y="520" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="ai-model-cache" value="模型缓存&#xa;10.0.3.40-41&#xa;模型结果缓存" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;" parent="1" vertex="1">
          <mxGeometry x="1430" y="520" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="ai-cost-control" value="成本控制&#xa;10.0.3.50&#xa;AI调用成本优化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;" parent="1" vertex="1">
          <mxGeometry x="1580" y="520" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="k8s-container-subnet" value="K8s容器网络集群 Container Network Cluster (10.0.10.0/20)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;fontStyle=1;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="100" y="620" width="1650" height="120" as="geometry" />
        </mxCell>
        <mxCell id="master-nodes-network" value="Master节点网络&#xa;10.0.10.10-15&#xa;6节点HA集群" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="130" y="650" width="130" height="50" as="geometry" />
        </mxCell>
        <mxCell id="business-worker-nodes" value="业务Worker节点&#xa;10.0.10.20-35&#xa;16节点・多行业隔离" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="290" y="650" width="130" height="50" as="geometry" />
        </mxCell>
        <mxCell id="ai-worker-nodes" value="AI专用Worker节点&#xa;10.0.10.40-47&#xa;8节点・GPU加速" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="450" y="650" width="130" height="50" as="geometry" />
        </mxCell>
        <mxCell id="config-worker-nodes" value="配置管理节点&#xa;10.0.10.50-53&#xa;4节点・热更新专用" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="610" y="650" width="130" height="50" as="geometry" />
        </mxCell>
        <mxCell id="pod-network-cidr" value="Pod网络CIDR&#xa;10.0.11.0/20&#xa;容器内部通信" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="770" y="650" width="130" height="50" as="geometry" />
        </mxCell>
        <mxCell id="service-network-cidr" value="Service网络&#xa;10.0.12.0/20&#xa;服务发现・负载均衡" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="930" y="650" width="130" height="50" as="geometry" />
        </mxCell>
        <mxCell id="ingress-controller-cluster" value="Ingress控制器&#xa;10.0.10.100-102&#xa;3节点・流量入口" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="1090" y="650" width="130" height="50" as="geometry" />
        </mxCell>
        <mxCell id="network-policy" value="网络策略引擎&#xa;Calico・微分段&#xa;多租户网络隔离" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="1250" y="650" width="130" height="50" as="geometry" />
        </mxCell>
        <mxCell id="cni-network" value="CNI网络插件&#xa;Flannel・Calico&#xa;高性能网络" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="1410" y="650" width="130" height="50" as="geometry" />
        </mxCell>
        <mxCell id="data-storage-subnet" value="分层数据存储子网 Layered Data Storage Subnet (10.0.20.0/22)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;fontStyle=1;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="100" y="770" width="1650" height="120" as="geometry" />
        </mxCell>
        <mxCell id="starRocks-cluster" value="StarRocks集群&#xa;10.0.20.10-16&#xa;7节点・OLAP分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="130" y="800" width="130" height="50" as="geometry" />
        </mxCell>
        <mxCell id="postgresql-cluster" value="PostgreSQL集群&#xa;10.0.20.20-25&#xa;6节点・读写分离" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="290" y="800" width="130" height="50" as="geometry" />
        </mxCell>
        <mxCell id="redis-cluster-net" value="Redis集群&#xa;10.0.20.30-39&#xa;10节点・分片集群" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="450" y="800" width="130" height="50" as="geometry" />
        </mxCell>
        <mxCell id="kafka-cluster-net" value="Kafka集群&#xa;10.0.20.40-46&#xa;7节点・高吞吐" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="610" y="800" width="130" height="50" as="geometry" />
        </mxCell>
        <mxCell id="elasticsearch-cluster-net" value="Elasticsearch集群&#xa;10.0.20.50-55&#xa;6节点・全文检索" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="770" y="800" width="130" height="50" as="geometry" />
        </mxCell>
        <mxCell id="minio-cluster-net" value="MinIO集群&#xa;10.0.20.60-67&#xa;8节点・对象存储" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="930" y="800" width="130" height="50" as="geometry" />
        </mxCell>
        <mxCell id="influxdb-cluster-net" value="InfluxDB集群&#xa;10.0.20.70-73&#xa;4节点・监控数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="1090" y="800" width="130" height="50" as="geometry" />
        </mxCell>
        <mxCell id="backup-storage-net" value="异地备份存储&#xa;10.0.20.80-81&#xa;2节点・灾备" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="1250" y="800" width="130" height="50" as="geometry" />
        </mxCell>
        <mxCell id="data-lineage-net" value="数据血缘系统&#xa;10.0.20.90-91&#xa;2节点・血缘追踪" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="1410" y="800" width="130" height="50" as="geometry" />
        </mxCell>
        <mxCell id="monitoring-subnet" value="智能监控管理子网 Smart Monitoring Subnet (10.0.30.0/22)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="100" y="920" width="1650" height="150" as="geometry" />
        </mxCell>
        <mxCell id="prometheus-cluster-net" value="Prometheus集群&#xa;10.0.30.10-13&#xa;4节点・指标采集" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="130" y="950" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="grafana-cluster-net" value="Grafana集群&#xa;10.0.30.20-22&#xa;3节点・可视化面板" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="280" y="950" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="elk-cluster-net" value="ELK Stack集群&#xa;10.0.30.30-35&#xa;6节点・日志分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="430" y="950" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="jaeger-cluster-net" value="Jaeger集群&#xa;10.0.30.40-42&#xa;3节点・链路追踪" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="580" y="950" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="jenkins-cluster-net" value="Jenkins集群&#xa;10.0.30.50-51&#xa;2节点・CI/CD" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="730" y="950" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="harbor-cluster-net" value="Harbor集群&#xa;10.0.30.60-61&#xa;2节点・镜像仓库" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="880" y="950" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="aiops-cluster" value="AIOps集群&#xa;10.0.30.70-71&#xa;2节点・智能运维" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="1030" y="950" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="config-management-cluster" value="配置管理集群&#xa;10.0.30.80-82&#xa;3节点・配置中心" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="1180" y="950" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="hot-update-service" value="热更新服务&#xa;10.0.30.90-91&#xa;2节点・配置热推送" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="1330" y="950" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="network-monitor" value="网络性能监控&#xa;流量分析・延迟监控&#xa;网络拓扑可视化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="130" y="1010" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="security-monitor" value="安全事件监控&#xa;入侵检测・威胁分析&#xa;安全态势感知" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="310" y="1010" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="business-monitor" value="业务指标监控&#xa;行业KPI・SLA监控&#xa;用户体验监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="490" y="1010" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="traffic-flow-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=3;strokeColor=#FF6600;" parent="1" source="user-browser" target="ai-waf" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="traffic-label-1" value="用户流量" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=10;fontColor=#FF6600;fontStyle=1;" parent="traffic-flow-1" vertex="1" connectable="0">
          <mxGeometry x="-0.1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="traffic-flow-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=3;strokeColor=#2196F3;" parent="1" source="slb-cluster" target="nginx-proxy-cluster" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="traffic-label-2" value="负载均衡" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=10;fontColor=#2196F3;fontStyle=1;" parent="traffic-flow-2" vertex="1" connectable="0">
          <mxGeometry x="-0.1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="traffic-flow-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=3;strokeColor=#4CAF50;" parent="1" source="ai-model-services" target="ai-gateway" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="traffic-label-3" value="AI模型调用" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=10;fontColor=#4CAF50;fontStyle=1;" parent="traffic-flow-3" vertex="1" connectable="0">
          <mxGeometry x="-0.1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="traffic-flow-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#9C27B0;" parent="1" source="config-proxy" target="config-management-cluster" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="traffic-label-4" value="配置同步" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=10;fontColor=#9C27B0;fontStyle=1;" parent="traffic-flow-4" vertex="1" connectable="0">
          <mxGeometry x="-0.1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="traffic-flow-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#FF9800;" parent="1" source="business-worker-nodes" target="starRocks-cluster" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="traffic-label-5" value="数据存储" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=10;fontColor=#FF9800;fontStyle=1;" parent="traffic-flow-5" vertex="1" connectable="0">
          <mxGeometry x="-0.1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="security-rules-title" value="网络安全规则与策略" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fffacd;strokeColor=#d6b656;fontSize=16;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1300" y="1110" width="450" height="30" as="geometry" />
        </mxCell>
        <mxCell id="security-rules-detail" value="安全组规则配置：&#xa;• 公网子网：仅允许443/80端口入站，启用DDoS防护&#xa;• DMZ子网：仅允许负载均衡器访问8080端口，内部隔离&#xa;• AI服务子网：仅允许业务容器访问AI接口，API密钥验证&#xa;• K8s容器网络：Pod间通信通过网络策略控制，微分段隔离&#xa;• 数据存储子网：仅允许容器网络访问数据库端口，加密传输&#xa;• 监控管理子网：仅允许堡垒机SSH访问，操作审计日志&#xa;&#xa;出站规则：&#xa;• 允许访问外部AI模型API (443)・DNS解析 (53)・时间同步 (123)&#xa;• 允许企业系统集成接口 (443/80)・第三方数据源API (443)&#xa;• 禁止其他出站流量，零信任网络模型&#xa;&#xa;网络监控：&#xa;• 全流量监控・异常行为检测・威胁情报联动&#xa;• 网络性能监控・延迟分析・带宽利用率监控&#xa;• 跨行业网络隔离・多租户安全保障" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fffacd;strokeColor=#d6b656;align=left;fontSize=11;" parent="1" vertex="1">
          <mxGeometry x="1300" y="1150" width="450" height="280" as="geometry" />
        </mxCell>
        <mxCell id="network-performance-title" value="网络性能指标" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f8ff;strokeColor=#6c8ebf;fontSize=16;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="100" y="1110" width="300" height="30" as="geometry" />
        </mxCell>
        <mxCell id="network-performance-detail" value="带宽配置：&#xa;• 公网出口：10Gbps (多线BGP)&#xa;• 内网互联：25Gbps (高速内网)&#xa;• 存储网络：40Gbps (SSD存储网络)&#xa;• AI服务网络：100Gbps (GPU高速互联)&#xa;• 管理网络：1Gbps (运维管理)&#xa;&#xa;延迟要求：&#xa;• API响应延迟：&lt; 10ms (P99)&#xa;• 数据库查询延迟：&lt; 5ms (P95)&#xa;• 缓存访问延迟：&lt; 1ms (P99)&#xa;• AI模型调用延迟：&lt; 100ms (P90)&#xa;• 配置热更新延迟：&lt; 3s (全网)&#xa;• 监控数据采集：&lt; 30s (周期)&#xa;&#xa;高可用设计：&#xa;• 双活数据中心・异地灾备&#xa;• 网络设备冗余・链路备份&#xa;• 自动故障切换 &lt; 30s" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f8ff;strokeColor=#6c8ebf;align=left;fontSize=11;" parent="1" vertex="1">
          <mxGeometry x="100" y="1150" width="300" height="280" as="geometry" />
        </mxCell>
        <mxCell id="dns-config-title" value="DNS与域名配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5dc;strokeColor=#d6b656;fontSize=16;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="450" y="1110" width="300" height="30" as="geometry" />
        </mxCell>
        <mxCell id="dns-config-detail" value="域名解析策略：&#xa;• voc.company.com → 智能负载均衡器&#xa;• api.voc.company.com → 统一API网关&#xa;• admin.voc.company.com → 管理后台&#xa;• ai.voc.company.com → AI服务网关&#xa;• config.voc.company.com → 配置管理中心&#xa;• monitor.voc.company.com → 监控面板&#xa;&#xa;行业子域名：&#xa;• auto.voc.company.com → 汽车行业&#xa;• retail.voc.company.com → 星巴克餐饮&#xa;• gov.voc.company.com → 政府信访&#xa;• telecom.voc.company.com → 手机通讯&#xa;• beauty.voc.company.com → 美妆行业&#xa;&#xa;内部DNS：&#xa;• *.k8s.local → Kubernetes Service&#xa;• *.ai.local → AI服务集群&#xa;• *.db.local → 数据库集群&#xa;• *.cache.local → 缓存集群&#xa;• *.mgmt.local → 管理服务&#xa;&#xa;DNS服务：阿里云PrivateZone + CoreDNS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5dc;strokeColor=#d6b656;align=left;fontSize=11;" parent="1" vertex="1">
          <mxGeometry x="450" y="1150" width="300" height="280" as="geometry" />
        </mxCell>
        <mxCell id="network-features" value="跨行业统一・多云混合・AI专网・配置热更新・零信任安全・智能监控・弹性扩展" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#f9a825;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="100" y="60" width="1550" height="20" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
