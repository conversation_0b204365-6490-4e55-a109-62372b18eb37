<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" version="28.0.7">
  <diagram name="VOC系统架构图" id="voc-system-architecture">
    <mxGraphModel dx="1837" dy="1025" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1854" pageHeight="1369" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="title" value="通用VOC报表系统 - 跨行业统一架构图" style="text;strokeColor=none;fillColor=none;html=1;fontSize=24;fontStyle=1;verticalAlign=middle;align=center;fontColor=#2c3e50;" parent="1" vertex="1">
          <mxGeometry x="650" y="20" width="554" height="40" as="geometry" />
        </mxCell>
        <mxCell id="user-layer" value="&lt;div style=&quot;&quot;&gt;&lt;span style=&quot;&quot;&gt;用户接入层 Multi-Channel User Access Layer&lt;/span&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=16;fontStyle=1;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="52" y="90" width="1648" height="120" as="geometry" />
        </mxCell>
        <mxCell id="web-console" value="Web管理控制台&#xa;多行业仪表板&#xa;配置管理界面" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="100" y="120" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="mobile-app" value="移动端应用&#xa;跨平台支持&#xa;离线分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="300" y="120" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="api-client" value="API客户端&#xa;第三方系统&#xa;行业定制接口" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="500" y="120" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="industry-portals" value="行业门户&#xa;汽车・星巴克・政府&#xa;手机・美妆・自定义" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="700" y="120" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="config-wizard" value="配置向导界面&#xa;零代码配置&#xa;可视化编辑" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="900" y="120" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="gateway-layer" value="统一网关接入层 Unified Gateway Layer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=16;fontStyle=1;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="50" y="230" width="1650" height="100" as="geometry" />
        </mxCell>
        <mxCell id="api-gateway" value="统一API网关&#xa;Spring Cloud Gateway&#xa;路由・认证・限流・版本控制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="150" y="260" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="load-balancer" value="智能负载均衡&#xa;Nginx + 流量调度&#xa;行业路由・A/B测试" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="400" y="260" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="config-gateway" value="配置驱动网关&#xa;动态路由配置&#xa;热更新支持" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="650" y="260" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="industry-gateway" value="行业适配网关&#xa;多租户隔离&#xa;个性化配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="900" y="260" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ai-config-layer" value="AI智能配置管理层 AI-Driven Configuration Layer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;fontSize=16;fontStyle=1;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="50" y="360" width="1650" height="150" as="geometry" />
        </mxCell>
        <mxCell id="ai-config-engine" value="AI配置识别引擎&#xa;数据特征自动识别&#xa;行业模板智能推荐" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#2e7d32;" parent="1" vertex="1">
          <mxGeometry x="100" y="390" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="nocode-platform" value="零代码配置平台&#xa;可视化拖拽编辑&#xa;实时预览・模板复用" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#2e7d32;" parent="1" vertex="1">
          <mxGeometry x="320" y="390" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="dynamic-rules" value="动态规则引擎&#xa;情感・意图・主题规则&#xa;热更新・版本管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#2e7d32;" parent="1" vertex="1">
          <mxGeometry x="540" y="390" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="config-center" value="统一配置中心&#xa;多环境配置管理&#xa;配置审批・灰度发布" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#2e7d32;" parent="1" vertex="1">
          <mxGeometry x="760" y="390" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="QR163K9ZpPpgA4sJcRCm-1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="hot-reload" target="config-center" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="hot-reload" value="热更新引擎&#xa;配置实时生效&#xa;无需重启・平滑切换" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#2e7d32;" parent="1" vertex="1">
          <mxGeometry x="980" y="390" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="template-manager" value="行业模板管理&#xa;多行业模板库&#xa;继承・扩展・定制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#2e7d32;" parent="1" vertex="1">
          <mxGeometry x="1200" y="390" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="config-validation" value="配置验证引擎&#xa;智能冲突检测&#xa;依赖关系验证" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#2e7d32;" parent="1" vertex="1">
          <mxGeometry x="1420" y="390" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="service-layer" value="跨行业统一业务服务层 Cross-Industry Business Service Layer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=16;fontStyle=1;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="50" y="540" width="1650" height="150" as="geometry" />
        </mxCell>
        <mxCell id="data-ingestion" value="统一数据接入服务&#xa;多格式・多来源支持&#xa;智能格式识别・质量检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="100" y="570" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ai-analysis" value="AI智能分析服务&#xa;情感・意图・主题分析&#xa;批量处理・成本控制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="320" y="570" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="report-service" value="统一报表服务&#xa;配置驱动报表生成&#xa;多格式导出・实时更新" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="540" y="570" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="industry-adapter" value="行业适配服务&#xa;多行业业务逻辑&#xa;个性化处理・标准化接口" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="760" y="570" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="user-service" value="统一用户管理服务&#xa;多租户・权限控制&#xa;SSO・企业集成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="980" y="570" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="notification-service" value="智能通知服务&#xa;多渠道消息推送&#xa;模板化・个性化通知" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="1200" y="570" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="quality-service" value="数据质量服务&#xa;质量监控・异常检测&#xa;数据血缘・影响分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="1420" y="570" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ai-layer" value="AI智能分析处理层 AI Analysis &amp;amp; Processing Layer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;fontSize=16;fontStyle=1;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="50" y="720" width="1650" height="150" as="geometry" />
        </mxCell>
        <mxCell id="llm-engine" value="大模型分析引擎&#xa;火山大模型・OpenAI&#xa;多维度并行分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc02;strokeColor=#ff6f00;" parent="1" vertex="1">
          <mxGeometry x="150" y="750" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="batch-engine" value="批量处理引擎&#xa;大规模数据处理&#xa;并行计算・负载均衡" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc02;strokeColor=#ff6f00;" parent="1" vertex="1">
          <mxGeometry x="370" y="750" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="result-optimizer" value="结果优化引擎&#xa;质量检查・阈值调整&#xa;置信度评估・成本控制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc02;strokeColor=#ff6f00;" parent="1" vertex="1">
          <mxGeometry x="590" y="750" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="model-manager" value="模型管理服务&#xa;模型版本控制&#xa;A/B测试・性能监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc02;strokeColor=#ff6f00;" parent="1" vertex="1">
          <mxGeometry x="810" y="750" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="feature-engine" value="特征工程引擎&#xa;特征提取・转换&#xa;特征存储・复用" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc02;strokeColor=#ff6f00;" parent="1" vertex="1">
          <mxGeometry x="1030" y="750" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="QR163K9ZpPpgA4sJcRCm-2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="insight-engine" target="feature-engine" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="insight-engine" value="深度洞察引擎&#xa;趋势分析・异常检测&#xa;预测建模・业务洞察" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc02;strokeColor=#ff6f00;" parent="1" vertex="1">
          <mxGeometry x="1250" y="750" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="data-layer" value="分层数据存储层 Layered Data Storage Layer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=16;fontStyle=1;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="50" y="900" width="1660" height="150" as="geometry" />
        </mxCell>
        <mxCell id="data-warehouse" value="数据仓库 StarRocks&#xa;ODS・DWD・DWS・ADS&#xa;OLAP查询・实时分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="100" y="930" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="oltp-db" value="事务数据库&#xa;PostgreSQL集群&#xa;配置・用户・元数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="320" y="930" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="cache-redis" value="分布式缓存&#xa;Redis Cluster&#xa;热点数据・会话・配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="540" y="930" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="message-queue" value="消息队列&#xa;Apache Kafka&#xa;流处理・事件驱动" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="760" y="930" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="QR163K9ZpPpgA4sJcRCm-3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="search-engine" target="object-storage" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="search-engine" value="搜索引擎&#xa;Elasticsearch&#xa;全文检索・日志分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="980" y="930" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="object-storage" value="对象存储&#xa;MinIO集群&#xa;文件・备份・数据湖" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="1200" y="930" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="lineage-system" value="数据血缘系统&#xa;Atlas・血缘追踪&#xa;影响分析・依赖管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="1420" y="930" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="infrastructure-layer" value="云原生基础设施层 Cloud-Native Infrastructure Layer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=16;fontStyle=1;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="50" y="1080" width="1660" height="150" as="geometry" />
        </mxCell>
        <mxCell id="kubernetes" value="容器编排平台&#xa;Kubernetes集群&#xa;微服务部署・自动扩缩容" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="100" y="1110" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="service-mesh" value="服务网格&#xa;Istio・Envoy&#xa;服务通信・安全・观测" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="320" y="1110" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="monitoring" value="智能监控平台&#xa;Prometheus + Grafana&#xa;多层监控・智能告警" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="540" y="1110" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="logging" value="统一日志平台&#xa;ELK Stack + Jaeger&#xa;日志聚合・链路追踪" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="760" y="1110" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="cicd" value="DevOps平台&#xa;GitLab CI + ArgoCD&#xa;持续集成・GitOps部署" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="980" y="1110" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="security" value="安全防护平台&#xa;WAF + 零信任网络&#xa;威胁检测・合规审计" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="1200" y="1110" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="auto-ops" value="智能运维平台&#xa;AIOps・自动化运维&#xa;故障自愈・性能优化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="1420" y="1110" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="external-layer" value="外部集成与扩展层 External Integration &amp;amp; Extension Layer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=16;fontStyle=1;verticalAlign=top;align=center;" parent="1" vertex="1">
          <mxGeometry x="50" y="1260" width="1660" height="100" as="geometry" />
        </mxCell>
        <mxCell id="ai-models" value="大模型服务集群&#xa;火山・OpenAI・Claude&#xa;多模型支持・智能路由" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="120" y="1290" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="enterprise-systems" value="企业系统集成&#xa;CRM・ERP・OA&#xa;数据同步・业务协同" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="340" y="1290" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="industry-systems" value="行业专业系统&#xa;汽车DMS・政府OA&#xa;星巴克POS・手机CRM" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="560" y="1290" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cloud-services" value="云服务集成&#xa;阿里云・AWS・Azure&#xa;存储・计算・AI服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="780" y="1290" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="sso-identity" value="身份认证集成&#xa;LDAP・SAML・OAuth&#xa;企业SSO・多因子认证" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="1000" y="1290" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="third-party-data" value="第三方数据源&#xa;社交媒体・调研平台&#xa;公开数据・行业报告" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="1220" y="1290" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="api-ecosystem" value="API生态系统&#xa;开放API・SDK&#xa;第三方开发・应用市场" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="1440" y="1290" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="user-to-gateway" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#1976d2;" parent="1" source="web-console" target="api-gateway" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="gateway-to-config" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#388e3c;" parent="1" source="config-gateway" target="ai-config-engine" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="config-to-service" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#f57c00;" parent="1" source="dynamic-rules" target="ai-analysis" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="service-to-ai" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#ff6f00;" parent="1" source="ai-analysis" target="llm-engine" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ai-to-storage" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#6c8ebf;" parent="1" source="batch-engine" target="data-warehouse" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="storage-to-infra" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#9673a6;" parent="1" source="message-queue" target="kubernetes" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="architecture-features" value="架构特色亮点" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#f9a825;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="100" y="60" width="1600" height="20" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
