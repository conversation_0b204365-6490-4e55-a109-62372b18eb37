<?xml version="1.0" encoding="UTF-8"?>
<mxGraphModel dx="2074" dy="1496" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1697" pageHeight="1600" background="#ffffff" math="0" shadow="0">
  <root>
    <mxCell id="0"/>
    <mxCell id="1" parent="0"/>
    
    <!-- 标题 -->
    <mxCell id="title-1" value="通用VOC报表系统 - 详细完整流程图 (含前端接口调用)" style="text;strokeColor=none;fillColor=none;html=1;fontSize=28;fontStyle=1;verticalAlign=middle;align=center;fontColor=#2c3e50;" vertex="1" parent="1">
      <mxGeometry x="600" y="20" width="497" height="50" as="geometry"/>
    </mxCell>
    
    <!-- 左侧：数据源和接入层 -->
    <mxCell id="datasource-layer" value="数据源层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontSize=14;fontStyle=1;" vertex="1" parent="1">
      <mxGeometry x="50" y="100" width="300" height="480" as="geometry"/>
    </mxCell>
    
    <!-- 多行业数据源 -->
    <mxCell id="auto-industry" value="汽车行业" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#c62828;fontSize=12;" vertex="1" parent="datasource-layer">
      <mxGeometry x="20" y="40" width="80" height="40" as="geometry"/>
    </mxCell>
    <mxCell id="starbucks-industry" value="星巴克餐饮" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontSize=12;" vertex="1" parent="datasource-layer">
      <mxGeometry x="110" y="40" width="80" height="40" as="geometry"/>
    </mxCell>
    <mxCell id="gov-industry" value="政府信访" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#ef6c00;fontSize=12;" vertex="1" parent="datasource-layer">
      <mxGeometry x="200" y="40" width="80" height="40" as="geometry"/>
    </mxCell>
    <mxCell id="phone-industry" value="手机通讯" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=12;" vertex="1" parent="datasource-layer">
      <mxGeometry x="20" y="90" width="80" height="40" as="geometry"/>
    </mxCell>
    <mxCell id="cosmetic-industry" value="美妆行业" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#ad1457;fontSize=12;" vertex="1" parent="datasource-layer">
      <mxGeometry x="110" y="90" width="80" height="40" as="geometry"/>
    </mxCell>
    <mxCell id="custom-industry" value="自定义行业" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#424242;fontSize=12;" vertex="1" parent="datasource-layer">
      <mxGeometry x="200" y="90" width="80" height="40" as="geometry"/>
    </mxCell>
    
    <!-- 数据来源渠道 -->
    <mxCell id="datasource-channels" value="数据来源渠道" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;fontSize=13;fontStyle=1;" vertex="1" parent="datasource-layer">
      <mxGeometry x="20" y="150" width="260" height="20" as="geometry"/>
    </mxCell>
    
    <mxCell id="csv-source" value="CSV/Excel文件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;" vertex="1" parent="datasource-layer">
      <mxGeometry x="20" y="180" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="api-source" value="API接口" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;" vertex="1" parent="datasource-layer">
      <mxGeometry x="110" y="180" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="db-source" value="数据库直连" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;" vertex="1" parent="datasource-layer">
      <mxGeometry x="200" y="180" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="stream-source" value="实时流数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;" vertex="1" parent="datasource-layer">
      <mxGeometry x="20" y="225" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="kafka-source" value="Kafka消息" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;" vertex="1" parent="datasource-layer">
      <mxGeometry x="110" y="225" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="third-party" value="第三方系统" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;" vertex="1" parent="datasource-layer">
      <mxGeometry x="200" y="225" width="80" height="35" as="geometry"/>
    </mxCell>
    
    <!-- 数据接入处理 -->
    <mxCell id="ingestion-process" value="数据接入处理" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;fontSize=13;fontStyle=1;" vertex="1" parent="datasource-layer">
      <mxGeometry x="20" y="280" width="260" height="20" as="geometry"/>
    </mxCell>
    
    <mxCell id="format-adapter" value="格式适配器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;" vertex="1" parent="datasource-layer">
      <mxGeometry x="20" y="310" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="quality-check" value="质量检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;" vertex="1" parent="datasource-layer">
      <mxGeometry x="110" y="310" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="field-mapping" value="字段映射" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;" vertex="1" parent="datasource-layer">
      <mxGeometry x="200" y="310" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="data-clean" value="数据清洗" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;" vertex="1" parent="datasource-layer">
      <mxGeometry x="20" y="355" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="data-validate" value="数据验证" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;" vertex="1" parent="datasource-layer">
      <mxGeometry x="110" y="355" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="data-transform" value="数据转换" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;" vertex="1" parent="datasource-layer">
      <mxGeometry x="200" y="355" width="80" height="35" as="geometry"/>
    </mxCell>
    
    <!-- 数据队列 -->
    <mxCell id="message-queue" value="消息队列 (Kafka)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8eaf6;strokeColor=#3f51b5;fontSize=12;fontStyle=1;" vertex="1" parent="datasource-layer">
      <mxGeometry x="80" y="420" width="140" height="40" as="geometry"/>
    </mxCell>
    
    <!-- 配置管理层 -->
    <mxCell id="config-layer" value="智能配置管理层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;fontSize=14;fontStyle=1;" vertex="1" parent="1">
      <mxGeometry x="380" y="100" width="320" height="480" as="geometry"/>
    </mxCell>
    
    <!-- AI配置识别引擎 -->
    <mxCell id="ai-config-engine" value="AI配置识别引擎" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#2e7d32;fontSize=13;fontStyle=1;" vertex="1" parent="config-layer">
      <mxGeometry x="60" y="40" width="200" height="40" as="geometry"/>
    </mxCell>
    
    <mxCell id="industry-analysis" value="行业特征分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;" vertex="1" parent="config-layer">
      <mxGeometry x="20" y="100" width="90" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="config-recommend" value="配置智能推荐" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;" vertex="1" parent="config-layer">
      <mxGeometry x="120" y="100" width="90" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="template-match" value="模板自动匹配" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;" vertex="1" parent="config-layer">
      <mxGeometry x="220" y="100" width="90" height="35" as="geometry"/>
    </mxCell>
    
    <!-- 零代码配置平台 -->
    <mxCell id="nocode-platform" value="零代码配置平台" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#a5d6a7;strokeColor=#388e3c;fontSize=13;fontStyle=1;" vertex="1" parent="config-layer">
      <mxGeometry x="60" y="155" width="200" height="40" as="geometry"/>
    </mxCell>
    
    <mxCell id="visual-config" value="可视化配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;" vertex="1" parent="config-layer">
      <mxGeometry x="20" y="215" width="90" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="drag-drop" value="拖拽式编辑" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;" vertex="1" parent="config-layer">
      <mxGeometry x="120" y="215" width="90" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="realtime-preview" value="实时预览" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;" vertex="1" parent="config-layer">
      <mxGeometry x="220" y="215" width="90" height="35" as="geometry"/>
    </mxCell>
    
    <!-- 动态规则引擎 -->
    <mxCell id="dynamic-rules" value="动态规则引擎" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#81c784;strokeColor=#2e7d32;fontSize=13;fontStyle=1;" vertex="1" parent="config-layer">
      <mxGeometry x="60" y="270" width="200" height="40" as="geometry"/>
    </mxCell>
    
    <mxCell id="sentiment-rules" value="情感分析规则" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;" vertex="1" parent="config-layer">
      <mxGeometry x="20" y="330" width="90" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="intent-rules" value="意图识别规则" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;" vertex="1" parent="config-layer">
      <mxGeometry x="120" y="330" width="90" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="topic-rules" value="主题分类规则" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;" vertex="1" parent="config-layer">
      <mxGeometry x="220" y="330" width="90" height="35" as="geometry"/>
    </mxCell>
    
    <!-- 配置热更新 -->
    <mxCell id="hot-update" value="配置热更新引擎" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#66bb6a;strokeColor=#1b5e20;fontSize=13;fontStyle=1;" vertex="1" parent="config-layer">
      <mxGeometry x="60" y="385" width="200" height="40" as="geometry"/>
    </mxCell>
    
    <!-- AI分析处理层 -->
    <mxCell id="ai-analysis-layer" value="AI智能分析处理层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;fontSize=14;fontStyle=1;" vertex="1" parent="1">
      <mxGeometry x="730" y="100" width="350" height="480" as="geometry"/>
    </mxCell>
    
    <!-- 大模型分析引擎 -->
    <mxCell id="llm-engine" value="大模型分析引擎 (火山大模型)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc02;strokeColor=#ff6f00;fontSize=13;fontStyle=1;" vertex="1" parent="ai-analysis-layer">
      <mxGeometry x="75" y="40" width="200" height="40" as="geometry"/>
    </mxCell>
    
    <!-- 多维度分析 -->
    <mxCell id="multi-analysis" value="多维度并行分析" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;fontSize=12;fontStyle=1;" vertex="1" parent="ai-analysis-layer">
      <mxGeometry x="125" y="100" width="100" height="20" as="geometry"/>
    </mxCell>
    
    <mxCell id="sentiment-analysis" value="情感分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="ai-analysis-layer">
      <mxGeometry x="20" y="130" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="intent-analysis" value="意图识别" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="ai-analysis-layer">
      <mxGeometry x="110" y="130" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="topic-analysis" value="主题分类" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="ai-analysis-layer">
      <mxGeometry x="200" y="130" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="urgency-analysis" value="紧急程度" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="ai-analysis-layer">
      <mxGeometry x="290" y="130" width="50" height="35" as="geometry"/>
    </mxCell>
    
    <mxCell id="user-type" value="用户类型" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="ai-analysis-layer">
      <mxGeometry x="20" y="175" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="channel-type" value="渠道分类" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="ai-analysis-layer">
      <mxGeometry x="110" y="175" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="confidence-score" value="置信度评分" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="ai-analysis-layer">
      <mxGeometry x="200" y="175" width="80" height="35" as="geometry"/>
    </mxCell>
    
    <!-- 分析结果优化 -->
    <mxCell id="result-optimization" value="分析结果优化引擎" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffb74d;strokeColor=#ef6c00;fontSize=13;fontStyle=1;" vertex="1" parent="ai-analysis-layer">
      <mxGeometry x="75" y="230" width="200" height="40" as="geometry"/>
    </mxCell>
    
    <mxCell id="quality-check-ai" value="质量检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="ai-analysis-layer">
      <mxGeometry x="20" y="285" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="threshold-adjust" value="阈值调整" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="ai-analysis-layer">
      <mxGeometry x="110" y="285" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="result-cache" value="结果缓存" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="ai-analysis-layer">
      <mxGeometry x="200" y="285" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="cost-control" value="成本控制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="ai-analysis-layer">
      <mxGeometry x="290" y="285" width="50" height="35" as="geometry"/>
    </mxCell>
    
    <!-- 批量处理引擎 -->
    <mxCell id="batch-engine" value="批量处理引擎" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ff9800;strokeColor=#e65100;fontSize=13;fontStyle=1;" vertex="1" parent="ai-analysis-layer">
      <mxGeometry x="75" y="340" width="200" height="40" as="geometry"/>
    </mxCell>
    
    <mxCell id="parallel-process" value="并行处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="ai-analysis-layer">
      <mxGeometry x="20" y="395" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="batch-optimize" value="批量优化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="ai-analysis-layer">
      <mxGeometry x="110" y="395" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="load-balance" value="负载均衡" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="ai-analysis-layer">
      <mxGeometry x="200" y="395" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="fault-tolerance" value="容错处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="ai-analysis-layer">
      <mxGeometry x="290" y="395" width="50" height="35" as="geometry"/>
    </mxCell>
    
    <!-- 数据存储层 -->
    <mxCell id="storage-layer" value="数据存储层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e8eaf6;strokeColor=#303f9f;fontSize=14;fontStyle=1;" vertex="1" parent="1">
      <mxGeometry x="1110" y="100" width="300" height="480" as="geometry"/>
    </mxCell>
    
    <!-- 数据仓库 -->
    <mxCell id="data-warehouse" value="数据仓库 (StarRocks)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c5cae9;strokeColor=#303f9f;fontSize=13;fontStyle=1;" vertex="1" parent="storage-layer">
      <mxGeometry x="50" y="40" width="200" height="40" as="geometry"/>
    </mxCell>
    
    <!-- 数据分层 -->
    <mxCell id="ods-layer" value="ODS原始数据层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;" vertex="1" parent="storage-layer">
      <mxGeometry x="20" y="100" width="120" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="dwd-layer" value="DWD明细数据层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;" vertex="1" parent="storage-layer">
      <mxGeometry x="160" y="100" width="120" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="dws-layer" value="DWS汇总数据层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;" vertex="1" parent="storage-layer">
      <mxGeometry x="20" y="145" width="120" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="ads-layer" value="ADS应用数据层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;" vertex="1" parent="storage-layer">
      <mxGeometry x="160" y="145" width="120" height="35" as="geometry"/>
    </mxCell>
    
    <!-- 缓存层 -->
    <mxCell id="cache-layer" value="缓存层 (Redis)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcdd2;strokeColor=#d32f2f;fontSize=13;fontStyle=1;" vertex="1" parent="storage-layer">
      <mxGeometry x="50" y="200" width="200" height="40" as="geometry"/>
    </mxCell>
    
    <mxCell id="hot-data" value="热点数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#c62828;" vertex="1" parent="storage-layer">
      <mxGeometry x="20" y="260" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="session-cache" value="会话缓存" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#c62828;" vertex="1" parent="storage-layer">
      <mxGeometry x="110" y="260" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="config-cache" value="配置缓存" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#c62828;" vertex="1" parent="storage-layer">
      <mxGeometry x="200" y="260" width="80" height="35" as="geometry"/>
    </mxCell>
    
    <!-- 对象存储 -->
    <mxCell id="object-storage" value="对象存储 (MinIO)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dcedc8;strokeColor=#689f38;fontSize=13;fontStyle=1;" vertex="1" parent="storage-layer">
      <mxGeometry x="50" y="315" width="200" height="40" as="geometry"/>
    </mxCell>
    
    <mxCell id="file-storage" value="文件存储" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#558b2f;" vertex="1" parent="storage-layer">
      <mxGeometry x="20" y="375" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="backup-storage" value="备份存储" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#558b2f;" vertex="1" parent="storage-layer">
      <mxGeometry x="110" y="375" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="log-storage" value="日志存储" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#558b2f;" vertex="1" parent="storage-layer">
      <mxGeometry x="200" y="375" width="80" height="35" as="geometry"/>
    </mxCell>
    
    <!-- 数据血缘 -->
    <mxCell id="data-lineage" value="数据血缘追踪" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#f9a825;fontSize=13;fontStyle=1;" vertex="1" parent="storage-layer">
      <mxGeometry x="50" y="430" width="200" height="40" as="geometry"/>
    </mxCell>
    
    <!-- 业务服务层 - 细化版本 -->
    <mxCell id="service-layer" value="跨行业统一业务服务层 (API接口层)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=14;fontStyle=1;" vertex="1" parent="1">
      <mxGeometry x="50" y="620" width="630" height="420" as="geometry"/>
    </mxCell>
    
    <!-- 统一API网关 -->
    <mxCell id="unified-api-gateway" value="统一API网关 (Gateway)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ce93d8;strokeColor=#8e24aa;fontSize=13;fontStyle=1;" vertex="1" parent="service-layer">
      <mxGeometry x="220" y="35" width="190" height="40" as="geometry"/>
    </mxCell>
    
    <!-- 第一行：配置驱动API -->
    <mxCell id="config-driven-apis" value="配置驱动API层" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;fontSize=12;fontStyle=1;" vertex="1" parent="service-layer">
      <mxGeometry x="275" y="90" width="80" height="25" as="geometry"/>
    </mxCell>
    
    <mxCell id="industry-config-api" value="行业配置获取&#xa;/api/v1/config/&#xa;industry/{type}" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=10;" vertex="1" parent="service-layer">
      <mxGeometry x="30" y="125" width="110" height="45" as="geometry"/>
    </mxCell>
    <mxCell id="dynamic-rules-api" value="动态规则管理&#xa;/api/v1/rules/&#xa;dynamic" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=10;" vertex="1" parent="service-layer">
      <mxGeometry x="160" y="125" width="110" height="45" as="geometry"/>
    </mxCell>
    <mxCell id="template-api" value="行业模板获取&#xa;/api/v1/template/&#xa;{industryId}" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=10;" vertex="1" parent="service-layer">
      <mxGeometry x="290" y="125" width="110" height="45" as="geometry"/>
    </mxCell>
    <mxCell id="hot-update-api" value="配置热更新&#xa;/api/v1/config/&#xa;hotupdate" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=10;" vertex="1" parent="service-layer">
      <mxGeometry x="420" y="125" width="110" height="45" as="geometry"/>
    </mxCell>
    
    <!-- VOC分析标准化API -->
    <mxCell id="voc-standards-apis" value="VOC分析标准化API层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ba68c8;strokeColor=#7b1fa2;fontSize=13;fontStyle=1;" vertex="1" parent="service-layer">
      <mxGeometry x="220" y="190" width="190" height="40" as="geometry"/>
    </mxCell>
    
    <!-- 第二行：分析API -->
    <mxCell id="sentiment-api" value="情感分析服务&#xa;/api/v1/analysis/&#xa;sentiment" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=10;" vertex="1" parent="service-layer">
      <mxGeometry x="30" y="245" width="110" height="45" as="geometry"/>
    </mxCell>
    <mxCell id="intent-api" value="意图识别服务&#xa;/api/v1/analysis/&#xa;intent" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=10;" vertex="1" parent="service-layer">
      <mxGeometry x="160" y="245" width="110" height="45" as="geometry"/>
    </mxCell>
    <mxCell id="topic-api" value="主题分类服务&#xa;/api/v1/analysis/&#xa;topic" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=10;" vertex="1" parent="service-layer">
      <mxGeometry x="290" y="245" width="110" height="45" as="geometry"/>
    </mxCell>
    <mxCell id="batch-analysis-api" value="批量分析服务&#xa;/api/v1/analysis/&#xa;batch" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=10;" vertex="1" parent="service-layer">
      <mxGeometry x="420" y="245" width="110" height="45" as="geometry"/>
    </mxCell>
    <mxCell id="quality-api" value="质量标准服务&#xa;/api/v1/quality/&#xa;standard" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=10;" vertex="1" parent="service-layer">
      <mxGeometry x="550" y="245" width="70" height="45" as="geometry"/>
    </mxCell>
    
    <!-- 数据服务API -->
    <mxCell id="data-service-apis" value="统一数据服务API层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ab47bc;strokeColor=#7b1fa2;fontSize=13;fontStyle=1;" vertex="1" parent="service-layer">
      <mxGeometry x="220" y="310" width="190" height="40" as="geometry"/>
    </mxCell>
    
    <!-- 第三行：数据API -->
    <mxCell id="data-query-api" value="数据查询服务&#xa;/api/v1/data/&#xa;query" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=10;" vertex="1" parent="service-layer">
      <mxGeometry x="30" y="365" width="110" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="report-api" value="报表生成服务&#xa;/api/v1/report/&#xa;generate" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=10;" vertex="1" parent="service-layer">
      <mxGeometry x="160" y="365" width="110" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="export-api" value="数据导出服务&#xa;/api/v1/export/&#xa;{format}" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=10;" vertex="1" parent="service-layer">
      <mxGeometry x="290" y="365" width="110" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="realtime-api" value="实时数据服务&#xa;/api/v1/realtime/&#xa;data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=10;" vertex="1" parent="service-layer">
      <mxGeometry x="420" y="365" width="110" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="user-auth-api" value="用户认证服务&#xa;/api/v1/auth/&#xa;user" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=10;" vertex="1" parent="service-layer">
      <mxGeometry x="550" y="365" width="70" height="35" as="geometry"/>
    </mxCell>
    
    <!-- 报表展示层 - 细化版本 -->
    <mxCell id="presentation-layer" value="跨行业统一报表展示层 (前端调用层)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e0f2f1;strokeColor=#00695c;fontSize=14;fontStyle=1;" vertex="1" parent="1">
      <mxGeometry x="710" y="620" width="630" height="420" as="geometry"/>
    </mxCell>
    
    <!-- 统一前端路由 -->
    <mxCell id="unified-frontend-router" value="统一前端路由 (Vue Router)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#80cbc4;strokeColor=#00695c;fontSize=13;fontStyle=1;" vertex="1" parent="presentation-layer">
      <mxGeometry x="220" y="35" width="190" height="40" as="geometry"/>
    </mxCell>
    
    <!-- 配置驱动前端组件 -->
    <mxCell id="config-driven-components" value="配置驱动前端组件层" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;fontSize=12;fontStyle=1;" vertex="1" parent="presentation-layer">
      <mxGeometry x="265" y="90" width="100" height="25" as="geometry"/>
    </mxCell>
    
    <!-- 第一行：页面组件 -->
    <mxCell id="industry-dashboard" value="行业仪表板&#xa;/dashboard/{industry}" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b2dfdb;strokeColor=#00695c;fontSize=10;" vertex="1" parent="presentation-layer">
      <mxGeometry x="30" y="125" width="110" height="45" as="geometry"/>
    </mxCell>
    <mxCell id="config-panel" value="配置管理面板&#xa;/config/manage" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b2dfdb;strokeColor=#00695c;fontSize=10;" vertex="1" parent="presentation-layer">
      <mxGeometry x="160" y="125" width="110" height="45" as="geometry"/>
    </mxCell>
    <mxCell id="analysis-workspace" value="分析工作台&#xa;/analysis/workspace" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b2dfdb;strokeColor=#00695c;fontSize=10;" vertex="1" parent="presentation-layer">
      <mxGeometry x="290" y="125" width="110" height="45" as="geometry"/>
    </mxCell>
    <mxCell id="report-builder" value="报表构建器&#xa;/report/builder" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b2dfdb;strokeColor=#00695c;fontSize=10;" vertex="1" parent="presentation-layer">
      <mxGeometry x="420" y="125" width="110" height="45" as="geometry"/>
    </mxCell>
    <mxCell id="cross-industry-compare" value="跨行业对比&#xa;/compare/industry" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b2dfdb;strokeColor=#00695c;fontSize=10;" vertex="1" parent="presentation-layer">
      <mxGeometry x="550" y="125" width="70" height="45" as="geometry"/>
    </mxCell>
    
    <!-- 可复用可视化组件库 -->
    <mxCell id="reusable-ui-components" value="可复用可视化组件库" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4db6ac;strokeColor=#00695c;fontSize=13;fontStyle=1;" vertex="1" parent="presentation-layer">
      <mxGeometry x="220" y="190" width="190" height="40" as="geometry"/>
    </mxCell>
    
    <!-- 第二行：UI组件 -->
    <mxCell id="sentiment-chart-component" value="SentimentChart&#xa;情感分析图表" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b2dfdb;strokeColor=#00695c;fontSize=10;" vertex="1" parent="presentation-layer">
      <mxGeometry x="30" y="245" width="110" height="45" as="geometry"/>
    </mxCell>
    <mxCell id="trend-chart-component" value="TrendChart&#xa;趋势分析组件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b2dfdb;strokeColor=#00695c;fontSize=10;" vertex="1" parent="presentation-layer">
      <mxGeometry x="160" y="245" width="110" height="45" as="geometry"/>
    </mxCell>
    <mxCell id="wordcloud-component" value="WordCloud&#xa;词云分析组件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b2dfdb;strokeColor=#00695c;fontSize=10;" vertex="1" parent="presentation-layer">
      <mxGeometry x="290" y="245" width="110" height="45" as="geometry"/>
    </mxCell>
    <mxCell id="ai-insight-component" value="AIInsight&#xa;AI洞察组件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b2dfdb;strokeColor=#00695c;fontSize=10;" vertex="1" parent="presentation-layer">
      <mxGeometry x="420" y="245" width="110" height="45" as="geometry"/>
    </mxCell>
    <mxCell id="export-component" value="ExportModule&#xa;导出功能模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b2dfdb;strokeColor=#00695c;fontSize=10;" vertex="1" parent="presentation-layer">
      <mxGeometry x="550" y="245" width="70" height="45" as="geometry"/>
    </mxCell>
    
    <!-- 前端状态管理与API调用层 -->
    <mxCell id="frontend-state-management" value="前端状态管理与API调用层 (Vuex/Pinia)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#26a69a;strokeColor=#00695c;fontSize=13;fontStyle=1;" vertex="1" parent="presentation-layer">
      <mxGeometry x="180" y="310" width="270" height="40" as="geometry"/>
    </mxCell>
    
    <!-- 第三行：前端服务 -->
    <mxCell id="config-store" value="ConfigStore&#xa;配置状态管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b2dfdb;strokeColor=#00695c;fontSize=10;" vertex="1" parent="presentation-layer">
      <mxGeometry x="30" y="365" width="110" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="data-store" value="DataStore&#xa;数据状态管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b2dfdb;strokeColor=#00695c;fontSize=10;" vertex="1" parent="presentation-layer">
      <mxGeometry x="160" y="365" width="110" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="report-store" value="ReportStore&#xa;报表状态管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b2dfdb;strokeColor=#00695c;fontSize=10;" vertex="1" parent="presentation-layer">
      <mxGeometry x="290" y="365" width="110" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="user-store" value="UserStore&#xa;用户状态管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b2dfdb;strokeColor=#00695c;fontSize=10;" vertex="1" parent="presentation-layer">
      <mxGeometry x="420" y="365" width="110" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="api-service" value="ApiService&#xa;统一API调用" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#b2dfdb;strokeColor=#00695c;fontSize=10;" vertex="1" parent="presentation-layer">
      <mxGeometry x="550" y="365" width="70" height="35" as="geometry"/>
    </mxCell>
    
    <!-- 前端Web层 -->
    <mxCell id="frontend-web-layer" value="前端Web层 (Vue.js 3 + TypeScript)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=14;fontStyle=1;" vertex="1" parent="1">
      <mxGeometry x="1370" y="620" width="300" height="420" as="geometry"/>
    </mxCell>
    
    <!-- 用户界面组件 -->
    <mxCell id="user-interface-components" value="用户界面组件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc02;strokeColor=#f57c00;fontSize=13;fontStyle=1;" vertex="1" parent="frontend-web-layer">
      <mxGeometry x="55" y="35" width="190" height="40" as="geometry"/>
    </mxCell>
    
    <!-- Web页面 -->
    <mxCell id="login-page" value="登录页面" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="frontend-web-layer">
      <mxGeometry x="20" y="90" width="80" height="40" as="geometry"/>
    </mxCell>
    <mxCell id="industry-select" value="行业选择" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="frontend-web-layer">
      <mxGeometry x="110" y="90" width="80" height="40" as="geometry"/>
    </mxCell>
    <mxCell id="config-wizard" value="配置向导" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="frontend-web-layer">
      <mxGeometry x="200" y="90" width="80" height="40" as="geometry"/>
    </mxCell>
    
    <mxCell id="main-dashboard" value="主仪表板" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="frontend-web-layer">
      <mxGeometry x="20" y="145" width="80" height="40" as="geometry"/>
    </mxCell>
    <mxCell id="analysis-page" value="分析页面" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="frontend-web-layer">
      <mxGeometry x="110" y="145" width="80" height="40" as="geometry"/>
    </mxCell>
    <mxCell id="deep-insight-page" value="深度洞察" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="frontend-web-layer">
      <mxGeometry x="200" y="145" width="80" height="40" as="geometry"/>
    </mxCell>
    
    <!-- 响应式设计 -->
    <mxCell id="responsive-design" value="响应式设计支持" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ff9800;strokeColor=#e65100;fontSize=13;fontStyle=1;" vertex="1" parent="frontend-web-layer">
      <mxGeometry x="55" y="210" width="190" height="40" as="geometry"/>
    </mxCell>
    
    <mxCell id="desktop-ui" value="桌面端UI" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="frontend-web-layer">
      <mxGeometry x="20" y="270" width="80" height="40" as="geometry"/>
    </mxCell>
    <mxCell id="tablet-ui" value="平板端UI" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="frontend-web-layer">
      <mxGeometry x="110" y="270" width="80" height="40" as="geometry"/>
    </mxCell>
    <mxCell id="mobile-ui" value="移动端UI" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="frontend-web-layer">
      <mxGeometry x="200" y="270" width="80" height="40" as="geometry"/>
    </mxCell>
    
    <!-- PWA支持 -->
    <mxCell id="pwa-support" value="PWA支持" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ff7043;strokeColor=#d84315;fontSize=13;fontStyle=1;" vertex="1" parent="frontend-web-layer">
      <mxGeometry x="55" y="330" width="190" height="40" as="geometry"/>
    </mxCell>
    
    <mxCell id="offline-support" value="离线支持" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="frontend-web-layer">
      <mxGeometry x="75" y="380" width="150" height="30" as="geometry"/>
    </mxCell>
    
    <!-- 监控告警层 -->
    <mxCell id="monitor-layer" value="监控告警层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;fontSize=14;fontStyle=1;" vertex="1" parent="1">
      <mxGeometry x="1370" y="100" width="300" height="480" as="geometry"/>
    </mxCell>
    
    <!-- 监控组件 -->
    <mxCell id="performance-monitor" value="性能监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="monitor-layer">
      <mxGeometry x="20" y="40" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="data-quality-monitor" value="数据质量监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="monitor-layer">
      <mxGeometry x="110" y="40" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="alert-service" value="告警服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="monitor-layer">
      <mxGeometry x="200" y="40" width="80" height="35" as="geometry"/>
    </mxCell>
    
    <mxCell id="log-analysis" value="日志分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="monitor-layer">
      <mxGeometry x="20" y="90" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="health-check" value="健康检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="monitor-layer">
      <mxGeometry x="110" y="90" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="capacity-monitor" value="容量监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="monitor-layer">
      <mxGeometry x="200" y="90" width="80" height="35" as="geometry"/>
    </mxCell>
    
    <mxCell id="business-monitor" value="业务监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="monitor-layer">
      <mxGeometry x="20" y="140" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="config-monitor" value="配置监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="monitor-layer">
      <mxGeometry x="110" y="140" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="user-behavior" value="用户行为监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="monitor-layer">
      <mxGeometry x="200" y="140" width="80" height="35" as="geometry"/>
    </mxCell>
    
    <mxCell id="api-monitor" value="API监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="monitor-layer">
      <mxGeometry x="20" y="190" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="frontend-monitor" value="前端监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="monitor-layer">
      <mxGeometry x="110" y="190" width="80" height="35" as="geometry"/>
    </mxCell>
    <mxCell id="interface-monitor" value="接口调用监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="monitor-layer">
      <mxGeometry x="200" y="190" width="80" height="35" as="geometry"/>
    </mxCell>
    
    <!-- 智能运维中心 -->
    <mxCell id="intelligent-ops" value="智能运维管理中心" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ff9800;strokeColor=#e65100;fontSize=12;fontStyle=1;" vertex="1" parent="monitor-layer">
      <mxGeometry x="75" y="250" width="150" height="35" as="geometry"/>
    </mxCell>
    
    <mxCell id="auto-scaling" value="自动扩缩容" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="monitor-layer">
      <mxGeometry x="75" y="300" width="150" height="35" as="geometry"/>
    </mxCell>
    
    <mxCell id="error-tracking" value="错误追踪与恢复" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="monitor-layer">
      <mxGeometry x="75" y="350" width="150" height="35" as="geometry"/>
    </mxCell>
    
    <mxCell id="performance-optimization" value="性能自动优化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;" vertex="1" parent="monitor-layer">
      <mxGeometry x="75" y="400" width="150" height="35" as="geometry"/>
    </mxCell>
    
    <!-- 主要数据流向箭头和接口调用 -->
    
    <!-- 数据源到配置层 -->
    <mxCell id="flow-1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#1976d2;" edge="1" parent="1" source="message-queue" target="ai-config-engine">
      <mxGeometry relative="1" as="geometry"/>
    </mxCell>
    <mxCell id="flow-1-label" value="数据特征识别" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=11;fontColor=#1976d2;fontStyle=1;" vertex="1" connectable="0" parent="flow-1">
      <mxGeometry x="-0.1" relative="1" as="geometry">
        <mxPoint as="offset"/>
      </mxGeometry>
    </mxCell>
    
    <!-- 配置层到AI分析层 -->
    <mxCell id="flow-2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#388e3c;" edge="1" parent="1" source="hot-update" target="llm-engine">
      <mxGeometry relative="1" as="geometry"/>
    </mxCell>
    <mxCell id="flow-2-label" value="配置驱动分析" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=11;fontColor=#388e3c;fontStyle=1;" vertex="1" connectable="0" parent="flow-2">
      <mxGeometry x="-0.1" relative="1" as="geometry">
        <mxPoint as="offset"/>
      </mxGeometry>
    </mxCell>
    
    <!-- AI分析层到存储层 -->
    <mxCell id="flow-3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#f57c00;" edge="1" parent="1" source="batch-engine" target="data-warehouse">
      <mxGeometry relative="1" as="geometry"/>
    </mxCell>
    <mxCell id="flow-3-label" value="分析结果存储" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=11;fontColor=#f57c00;fontStyle=1;" vertex="1" connectable="0" parent="flow-3">
      <mxGeometry x="-0.1" relative="1" as="geometry">
        <mxPoint as="offset"/>
      </mxGeometry>
    </mxCell>
    
    <!-- 存储层到服务层 -->
    <mxCell id="flow-4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#7b1fa2;" edge="1" parent="1" source="data-warehouse" target="data-service-apis">
      <mxGeometry relative="1" as="geometry">
        <Array as="points">
          <mxPoint x="1260" y="600"/>
          <mxPoint x="315" y="600"/>
        </Array>
      </mxGeometry>
    </mxCell>
    <mxCell id="flow-4-label" value="统一数据服务化" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=11;fontColor=#7b1fa2;fontStyle=1;" vertex="1" connectable="0" parent="flow-4">
      <mxGeometry x="-0.1" relative="1" as="geometry">
        <mxPoint as="offset"/>
      </mxGeometry>
    </mxCell>
    
    <!-- 服务层到报表展示层的API调用 -->
    <mxCell id="flow-5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#00695c;" edge="1" parent="1" source="unified-api-gateway" target="unified-frontend-router">
      <mxGeometry relative="1" as="geometry"/>
    </mxCell>
    <mxCell id="flow-5-label" value="统一API调用" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=11;fontColor=#00695c;fontStyle=1;" vertex="1" connectable="0" parent="flow-5">
      <mxGeometry x="-0.1" relative="1" as="geometry">
        <mxPoint as="offset"/>
      </mxGeometry>
    </mxCell>
    
    <!-- 报表展示层到前端Web层 -->
    <mxCell id="flow-6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#f57c00;" edge="1" parent="1" source="frontend-state-management" target="user-interface-components">
      <mxGeometry relative="1" as="geometry"/>
    </mxCell>
    <mxCell id="flow-6-label" value="组件渲染" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=11;fontColor=#f57c00;fontStyle=1;" vertex="1" connectable="0" parent="flow-6">
      <mxGeometry x="-0.1" relative="1" as="geometry">
        <mxPoint as="offset"/>
      </mxGeometry>
    </mxCell>
    
    <!-- 配置驱动API调用流程 -->
    <mxCell id="flow-7" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#9c27b0;" edge="1" parent="1" source="industry-config-api" target="industry-dashboard">
      <mxGeometry relative="1" as="geometry">
        <Array as="points">
          <mxPoint x="135" y="1120"/>
          <mxPoint x="795" y="1120"/>
        </Array>
      </mxGeometry>
    </mxCell>
    <mxCell id="flow-7-label" value="配置驱动渲染" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=10;fontColor=#9c27b0;fontStyle=1;" vertex="1" connectable="0" parent="flow-7">
      <mxGeometry x="-0.1" relative="1" as="geometry">
        <mxPoint as="offset"/>
      </mxGeometry>
    </mxCell>
    
    <!-- 监控反馈流程 -->
    <mxCell id="flow-8" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#e65100;dashed=1;" edge="1" parent="1" source="intelligent-ops" target="unified-api-gateway">
      <mxGeometry relative="1" as="geometry">
        <Array as="points">
          <mxPoint x="1595" y="590"/>
          <mxPoint x="315" y="590"/>
        </Array>
      </mxGeometry>
    </mxCell>
    <mxCell id="flow-8-label" value="智能运维反馈" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=10;fontColor=#e65100;fontStyle=1;" vertex="1" connectable="0" parent="flow-8">
      <mxGeometry x="-0.1" relative="1" as="geometry">
        <mxPoint as="offset"/>
      </mxGeometry>
    </mxCell>
    
    <!-- 前端到API的双向调用 -->
    <mxCell id="flow-9" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#795548;dashed=1;" edge="1" parent="1" source="api-service" target="unified-api-gateway">
      <mxGeometry relative="1" as="geometry">
        <Array as="points">
          <mxPoint x="1305" y="1080"/>
          <mxPoint x="680" y="1080"/>
          <mxPoint x="680" y="655"/>
        </Array>
      </mxGeometry>
    </mxCell>
    <mxCell id="flow-9-label" value="前端API调用" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=10;fontColor=#795548;fontStyle=1;" vertex="1" connectable="0" parent="flow-9">
      <mxGeometry x="-0.1" relative="1" as="geometry">
        <mxPoint as="offset"/>
      </mxGeometry>
    </mxCell>
    
    <!-- 底部流程说明 -->
    <mxCell id="process-description" value="接口调用与配置驱动流程说明" style="text;strokeColor=none;fillColor=none;html=1;fontSize=16;fontStyle=1;verticalAlign=middle;align=center;fontColor=#2c3e50;" vertex="1" parent="1">
      <mxGeometry x="750" y="1100" width="497" height="30" as="geometry"/>
    </mxCell>
    
    <mxCell id="process-steps" value="1. 多行业数据源自动接入，智能格式识别和质量检查&#xa;2. AI配置识别引擎自动分析数据特征，推荐最优配置&#xa;3. 零代码配置平台支持可视化配置，实时预览效果&#xa;4. 动态规则引擎和配置热更新，无需重启系统&#xa;5. 火山大模型多维度并行分析，智能优化和成本控制&#xa;6. StarRocks数据仓库分层存储，支持大规模OLAP查询&#xa;7. 跨行业统一API接口层，标准化RESTful API设计，支持配置驱动&#xa;8. 前端组件库复用，配置驱动渲染，统一用户体验，提高开发效率&#xa;9. 全链路接口调用监控，智能运维反馈，保障系统稳定运行" style="text;strokeColor=#cccccc;fillColor=#f8f9fa;html=1;fontSize=12;verticalAlign=top;align=left;whiteSpace=wrap;rounded=1;strokeWidth=1;spacing=10;" vertex="1" parent="1">
      <mxGeometry x="250" y="1150" width="1100" height="150" as="geometry"/>
    </mxCell>
    
    <!-- 技术特色标注 -->
    <mxCell id="innovation-1" value="AI驱动" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4caf50;strokeColor=#2e7d32;fontColor=white;fontSize=10;fontStyle=1;" vertex="1" parent="1">
      <mxGeometry x="490" y="80" width="60" height="20" as="geometry"/>
    </mxCell>
    
    <mxCell id="innovation-2" value="零代码" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ff9800;strokeColor=#e65100;fontColor=white;fontSize=10;fontStyle=1;" vertex="1" parent="1">
      <mxGeometry x="560" y="80" width="60" height="20" as="geometry"/>
    </mxCell>
    
    <mxCell id="innovation-3" value="配置驱动" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2196f3;strokeColor=#1976d2;fontColor=white;fontSize=10;fontStyle=1;" vertex="1" parent="1">
      <mxGeometry x="630" y="80" width="70" height="20" as="geometry"/>
    </mxCell>
    
    <mxCell id="innovation-4" value="热更新" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#9c27b0;strokeColor=#7b1fa2;fontColor=white;fontSize=10;fontStyle=1;" vertex="1" parent="1">
      <mxGeometry x="710" y="80" width="60" height="20" as="geometry"/>
    </mxCell>
    
    <mxCell id="innovation-5" value="大模型" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f44336;strokeColor=#d32f2f;fontColor=white;fontSize=10;fontStyle=1;" vertex="1" parent="1">
      <mxGeometry x="780" y="80" width="60" height="20" as="geometry"/>
    </mxCell>
    
    <mxCell id="innovation-6" value="跨行业统一" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#607d8b;strokeColor=#455a64;fontColor=white;fontSize=10;fontStyle=1;" vertex="1" parent="1">
      <mxGeometry x="850" y="80" width="80" height="20" as="geometry"/>
    </mxCell>
    
    <mxCell id="innovation-7" value="避免重复开发" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#795548;strokeColor=#5d4037;fontColor=white;fontSize=10;fontStyle=1;" vertex="1" parent="1">
      <mxGeometry x="940" y="80" width="90" height="20" as="geometry"/>
    </mxCell>
    
    <mxCell id="innovation-8" value="统一API接口" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#3f51b5;strokeColor=#303f9f;fontColor=white;fontSize=10;fontStyle=1;" vertex="1" parent="1">
      <mxGeometry x="1040" y="80" width="80" height="20" as="geometry"/>
    </mxCell>
    
    <mxCell id="innovation-9" value="组件复用" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#009688;strokeColor=#00695c;fontColor=white;fontSize=10;fontStyle=1;" vertex="1" parent="1">
      <mxGeometry x="1130" y="80" width="70" height="20" as="geometry"/>
    </mxCell>
    
  </root>
</mxGraphModel>
