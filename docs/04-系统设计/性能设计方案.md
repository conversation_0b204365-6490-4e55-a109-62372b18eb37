# 通用VOC报表系统性能设计方案

**文档版本**: v1.0.0  
**创建日期**: 2024年1月  
**最后更新**: 2024年1月  
**文档状态**: 设计阶段

---

## 📋 文档概述

本文档详细描述了通用VOC报表系统的性能设计方案，包括性能目标、优化策略、可扩展性设计、性能监控体系以及容量规划等关键内容。

### 性能设计原则
- **响应优先**：确保用户界面响应速度和API接口性能
- **水平扩展**：支持通过增加节点实现线性扩展
- **弹性伸缩**：根据负载自动调整资源配置
- **资源优化**：最大化硬件资源利用率
- **监控驱动**：基于监控数据持续优化性能

---

## 🎯 性能目标与指标

### 1.1 核心性能指标

**响应时间指标**：
```json
{
  "responseTimeTargets": {
    "apiEndpoints": {
      "dataIngestion": {
        "p95": "< 500ms",
        "p99": "< 1000ms",
        "average": "< 200ms",
        "timeout": "30s"
      },
      "dataQuery": {
        "p95": "< 300ms", 
        "p99": "< 800ms",
        "average": "< 150ms",
        "timeout": "10s"
      },
      "reportGeneration": {
        "p95": "< 5000ms",
        "p99": "< 10000ms",
        "average": "< 3000ms",
        "timeout": "60s"
      },
      "configuration": {
        "p95": "< 200ms",
        "p99": "< 500ms", 
        "average": "< 100ms",
        "timeout": "5s"
      }
    },
    "userInterface": {
      "pageLoad": "< 2s",
      "chartRender": "< 1s",
      "dataExport": "< 10s",
      "searchResponse": "< 500ms"
    }
  }
}
```

**吞吐量指标**：
```json
{
  "throughputTargets": {
    "dataProcessing": {
      "ingestionRate": "10,000 records/minute",
      "analysisRate": "5,000 records/minute",
      "batchProcessing": "100,000 records/hour"
    },
    "apiRequests": {
      "concurrent": "1,000 requests",
      "peakRps": "500 RPS",
      "sustainedRps": "200 RPS"
    },
    "reporting": {
      "concurrentReports": "50 reports",
      "reportGeneration": "10 reports/minute",
      "dataExport": "5 exports/minute"
    }
  }
}
```

**可用性指标**：
```json
{
  "availabilityTargets": {
    "systemUptime": "99.9%",
    "serviceAvailability": "99.95%",
    "dataConsistency": "99.99%",
    "maxDowntime": "8.76 hours/year",
    "mttr": "< 30 minutes",
    "mtbf": "> 720 hours"
  }
}
```

### 1.2 性能基准测试

**基准测试配置**：
```yaml
# JMeter性能测试计划
performanceTestPlan:
  name: "VOC System Performance Benchmark"
  
  testScenarios:
    - name: "数据接入性能测试"
      users: 100
      rampUp: 60
      duration: 600
      requests:
        - endpoint: "/api/v1/data-ingestion/tasks"
          method: POST
          dataSize: "1MB"
          frequency: "10/second"
      
    - name: "查询性能测试"  
      users: 200
      rampUp: 30
      duration: 300
      requests:
        - endpoint: "/api/v1/voc-records"
          method: GET
          parameters: "page=1&size=100&sentiment=negative"
          frequency: "20/second"
          
    - name: "报表生成测试"
      users: 50
      rampUp: 15
      duration: 900
      requests:
        - endpoint: "/api/v1/reports"
          method: POST
          reportType: "sentiment_analysis"
          frequency: "2/second"

  performanceThresholds:
    responseTime:
      p95: 1000
      p99: 2000
    throughput:
      minRps: 100
    errorRate:
      maxPercent: 1.0
    resourceUsage:
      maxCpu: 80
      maxMemory: 85
```

---

## ⚡ 系统性能优化策略

### 2.1 数据处理性能优化

**流式处理优化**：
```java
@Configuration
public class StreamProcessingOptimization {
    
    @Bean
    public StreamsConfig kafkaStreamsConfig() {
        Properties props = new Properties();
        
        // 性能调优参数
        props.put(StreamsConfig.NUM_STREAM_THREADS_CONFIG, 4);
        props.put(StreamsConfig.BUFFERED_RECORDS_PER_PARTITION_CONFIG, 1000);
        props.put(StreamsConfig.COMMIT_INTERVAL_MS_CONFIG, 1000);
        props.put(StreamsConfig.CACHE_MAX_BYTES_BUFFERING_CONFIG, 10 * 1024 * 1024);
        
        // 序列化优化
        props.put(StreamsConfig.DEFAULT_KEY_SERDE_CLASS_CONFIG, Serdes.String().getClass());
        props.put(StreamsConfig.DEFAULT_VALUE_SERDE_CLASS_CONFIG, JsonSerde.class);
        
        // 状态存储优化
        props.put(StreamsConfig.STATE_DIR_CONFIG, "/tmp/kafka-streams");
        props.put(StreamsConfig.ROCKSDB_CONFIG_SETTER_CLASS_CONFIG, 
                 RocksDBConfigSetter.class);
        
        return new StreamsConfig(props);
    }
    
    public static class RocksDBConfigSetter implements RocksDBConfigSetter {
        @Override
        public void setConfig(String storeName, Options options, Map<String, Object> configs) {
            // RocksDB性能优化
            options.setIncreaseParallelism(4);
            options.setAllowMmapReads(true);
            options.setAllowMmapWrites(true);
            options.setMaxWriteBufferNumber(3);
            options.setWriteBufferSize(64 * 1024 * 1024);
            
            // 压缩优化
            options.setCompressionType(CompressionType.LZ4_COMPRESSION);
            options.setBottommostCompressionType(CompressionType.ZSTD_COMPRESSION);
        }
    }
}
```

**批处理优化**：
```java
@Service
public class OptimizedBatchProcessor {
    
    @Value("${batch.processing.parallelism:8}")
    private int parallelism;
    
    @Value("${batch.processing.chunk-size:1000}")
    private int chunkSize;
    
    @Autowired
    private ThreadPoolTaskExecutor batchExecutor;
    
    public CompletableFuture<BatchProcessingResult> processBatch(List<DataRecord> records) {
        // 分片处理
        List<List<DataRecord>> chunks = Lists.partition(records, chunkSize);
        
        // 并行处理每个分片
        List<CompletableFuture<ChunkResult>> chunkFutures = chunks.stream()
            .map(chunk -> CompletableFuture.supplyAsync(() -> processChunk(chunk), batchExecutor))
            .collect(Collectors.toList());
        
        // 合并结果
        return CompletableFuture.allOf(chunkFutures.toArray(new CompletableFuture[0]))
            .thenApply(v -> {
                List<ChunkResult> chunkResults = chunkFutures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList());
                
                return mergeBatchResults(chunkResults);
            });
    }
    
    private ChunkResult processChunk(List<DataRecord> chunk) {
        try {
            List<ProcessedRecord> processedRecords = new ArrayList<>();
            
            // 批量预处理
            chunk = preprocessBatch(chunk);
            
            // 批量AI分析
            List<AnalysisResult> analysisResults = aiAnalysisService.batchAnalyze(chunk);
            
            // 批量后处理
            for (int i = 0; i < chunk.size(); i++) {
                ProcessedRecord processed = new ProcessedRecord(chunk.get(i), analysisResults.get(i));
                processedRecords.add(processed);
            }
            
            return ChunkResult.success(processedRecords);
            
        } catch (Exception e) {
            log.error("分片处理失败", e);
            return ChunkResult.failure(e);
        }
    }
    
    @Bean
    public ThreadPoolTaskExecutor batchExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(parallelism);
        executor.setMaxPoolSize(parallelism * 2);
        executor.setQueueCapacity(parallelism * 10);
        executor.setThreadNamePrefix("batch-processor-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }
}
```

### 2.2 数据库性能优化

**连接池优化**：
```yaml
# HikariCP配置优化
spring:
  datasource:
    hikari:
      # 连接池大小 = CPU核心数 * 2
      maximum-pool-size: 16
      minimum-idle: 8
      # 连接超时
      connection-timeout: 20000
      # 空闲超时
      idle-timeout: 300000
      # 最大生命周期
      max-lifetime: 1200000
      # 连接测试查询
      connection-test-query: SELECT 1
      # 性能优化
      leak-detection-threshold: 60000
      pool-name: "VOC-HikariCP"
      
  jpa:
    hibernate:
      # 批量操作优化
      jdbc:
        batch_size: 50
        order_inserts: true
        order_updates: true
        batch_versioned_data: true
    properties:
      hibernate:
        # 二级缓存
        cache:
          use_second_level_cache: true
          region:
            factory_class: org.hibernate.cache.jcache.JCacheRegionFactory
        # SQL优化
        format_sql: false
        use_sql_comments: false
        jdbc:
          fetch_size: 50
```

**查询优化策略**：
```java
@Repository
public class OptimizedVOCRepository {
    
    @PersistenceContext
    private EntityManager entityManager;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    // 分页查询优化
    @Query(value = """
        SELECT v.* FROM (
            SELECT id, original_text, sentiment, intent, topic, feedback_time,
                   ROW_NUMBER() OVER (ORDER BY feedback_time DESC) as rn
            FROM dwd_voc_detail 
            WHERE tenant_id = :tenantId 
            AND industry_code = :industryCode
            AND feedback_time >= :startDate
            AND (:sentiment IS NULL OR sentiment = :sentiment)
        ) v WHERE v.rn BETWEEN :offset AND :limit
        """, nativeQuery = true)
    List<VOCDetail> findWithOptimizedPaging(
        @Param("tenantId") String tenantId,
        @Param("industryCode") String industryCode, 
        @Param("startDate") LocalDateTime startDate,
        @Param("sentiment") String sentiment,
        @Param("offset") int offset,
        @Param("limit") int limit
    );
    
    // 聚合查询优化
    @Query("""
        SELECT new com.voc.dto.SentimentStats(
            v.sentiment, COUNT(v), AVG(v.sentimentConfidence)
        )
        FROM VOCDetail v 
        WHERE v.tenantId = :tenantId 
        AND v.industryCode = :industryCode
        AND v.feedbackTime >= :startDate
        GROUP BY v.sentiment
        """)
    @Cacheable(value = "sentimentStats", key = "#tenantId + '_' + #industryCode + '_' + #startDate")
    List<SentimentStats> getSentimentStatistics(
        @Param("tenantId") String tenantId,
        @Param("industryCode") String industryCode,
        @Param("startDate") LocalDateTime startDate
    );
    
    // 批量插入优化
    @Transactional
    public void batchInsert(List<VOCDetail> records) {
        int batchSize = 50;
        for (int i = 0; i < records.size(); i++) {
            entityManager.persist(records.get(i));
            
            if (i % batchSize == 0 && i > 0) {
                entityManager.flush();
                entityManager.clear();
            }
        }
        entityManager.flush();
        entityManager.clear();
    }
}
```

### 2.3 缓存性能优化

**多级缓存架构**：
```java
@Configuration
@EnableCaching
public class CacheOptimizationConfig {
    
    @Bean
    public CacheManager cacheManager() {
        // L1缓存：本地缓存（Caffeine）
        CaffeineCacheManager caffeineCacheManager = new CaffeineCacheManager();
        caffeineCacheManager.setCaffeine(Caffeine.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(Duration.ofMinutes(10))
            .recordStats());
        
        // L2缓存：分布式缓存（Redis）
        RedisCacheManager redisCacheManager = RedisCacheManager.builder(redisConnectionFactory())
            .cacheDefaults(redisCacheConfiguration())
            .build();
        
        // 多级缓存管理器
        return new MultiLevelCacheManager(caffeineCacheManager, redisCacheManager);
    }
    
    private RedisCacheConfiguration redisCacheConfiguration() {
        return RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofHours(1))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()))
            .compressWith(RedisCacheConfiguration.CompressionType.GZIP);
    }
}

@Service
public class IntelligentCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private CacheMetricsCollector metricsCollector;
    
    // 智能缓存预热
    @EventListener
    public void handleDataUpdateEvent(DataUpdateEvent event) {
        // 异步预热相关缓存
        CompletableFuture.runAsync(() -> {
            preWarmRelatedCaches(event.getIndustryCode(), event.getDataType());
        });
    }
    
    // 缓存命中率优化
    @Scheduled(fixedRate = 300000) // 每5分钟执行
    public void optimizeCacheStrategy() {
        Map<String, CacheStats> cacheStats = metricsCollector.collectCacheStats();
        
        for (Map.Entry<String, CacheStats> entry : cacheStats.entrySet()) {
            String cacheName = entry.getKey();
            CacheStats stats = entry.getValue();
            
            // 调整TTL
            if (stats.getHitRate() > 0.8) {
                // 高命中率，延长TTL
                adjustCacheTTL(cacheName, Duration.ofHours(2));
            } else if (stats.getHitRate() < 0.3) {
                // 低命中率，缩短TTL
                adjustCacheTTL(cacheName, Duration.ofMinutes(15));
            }
            
            // 调整缓存大小
            if (stats.getEvictionCount() > stats.getRequestCount() * 0.1) {
                // 淘汰率过高，增加缓存容量
                adjustCacheSize(cacheName, (int) (stats.getSize() * 1.5));
            }
        }
    }
}
```

---

## 🚀 可扩展性设计

### 3.1 水平扩展架构

**微服务扩展策略**：
```yaml
# Kubernetes扩展配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: voc-analysis-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: voc-analysis-service
  template:
    metadata:
      labels:
        app: voc-analysis-service
    spec:
      containers:
      - name: analysis-service
        image: voc/analysis-service:v1.0.0
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        env:
        - name: JAVA_OPTS
          value: "-Xmx1536m -XX:+UseG1GC -XX:+UseStringDeduplication"

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: voc-analysis-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: voc-analysis-service
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: active_requests_per_pod
      target:
        type: AverageValue
        averageValue: 100
```

**负载均衡优化**：
```java
@Configuration
public class LoadBalancingConfig {
    
    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        
        // 连接池配置
        PoolingHttpClientConnectionManager connectionManager = 
            new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(200);
        connectionManager.setDefaultMaxPerRoute(50);
        
        // HTTP客户端配置
        CloseableHttpClient httpClient = HttpClients.custom()
            .setConnectionManager(connectionManager)
            .setRetryHandler(new DefaultHttpRequestRetryHandler(3, true))
            .build();
        
        HttpComponentsClientHttpRequestFactory factory = 
            new HttpComponentsClientHttpRequestFactory(httpClient);
        factory.setConnectTimeout(5000);
        factory.setReadTimeout(10000);
        
        restTemplate.setRequestFactory(factory);
        return restTemplate;
    }
    
    @Bean
    public IRule ribbonRule() {
        // 使用加权响应时间规则
        return new WeightedResponseTimeRule();
    }
}

@Component
public class DynamicLoadBalancer {
    
    @Autowired
    private DiscoveryClient discoveryClient;
    
    @Autowired
    private MeterRegistry meterRegistry;
    
    public ServiceInstance selectOptimalInstance(String serviceName) {
        List<ServiceInstance> instances = discoveryClient.getInstances(serviceName);
        
        if (instances.isEmpty()) {
            throw new ServiceUnavailableException("No available instances for " + serviceName);
        }
        
        // 基于实时指标选择最优实例
        return instances.stream()
            .min((i1, i2) -> {
                double score1 = calculateInstanceScore(i1);
                double score2 = calculateInstanceScore(i2);
                return Double.compare(score1, score2);
            })
            .orElse(instances.get(0));
    }
    
    private double calculateInstanceScore(ServiceInstance instance) {
        String instanceId = instance.getInstanceId();
        
        // 获取实例的CPU使用率
        double cpuUsage = meterRegistry.get("system.cpu.usage")
            .tag("instance", instanceId)
            .gauge().value();
            
        // 获取实例的内存使用率
        double memoryUsage = meterRegistry.get("jvm.memory.used")
            .tag("instance", instanceId)
            .gauge().value() / 
            meterRegistry.get("jvm.memory.max")
            .tag("instance", instanceId)
            .gauge().value();
            
        // 获取实例的响应时间
        double responseTime = meterRegistry.get("http.server.requests")
            .tag("instance", instanceId)
            .timer().mean(TimeUnit.MILLISECONDS);
        
        // 综合评分（越低越好）
        return cpuUsage * 0.3 + memoryUsage * 0.3 + responseTime * 0.4;
    }
}
```

### 3.2 数据分片策略

**数据库分片设计**：
```java
@Configuration
public class ShardingConfiguration {
    
    @Bean
    public DataSource dataSource() throws SQLException {
        // 分片规则配置
        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
        
        // 表分片规则
        TableRuleConfiguration vocDetailTableRule = new TableRuleConfiguration(
            "dwd_voc_detail", 
            "ds${0..2}.dwd_voc_detail_${0..11}"
        );
        
        // 数据库分片策略（按租户ID）
        vocDetailTableRule.setDatabaseShardingStrategyConfig(
            new StandardShardingStrategyConfiguration("tenant_id", new TenantShardingAlgorithm())
        );
        
        // 表分片策略（按时间）
        vocDetailTableRule.setTableShardingStrategyConfig(
            new StandardShardingStrategyConfiguration("feedback_time", new TimeBasedShardingAlgorithm())
        );
        
        shardingRuleConfig.getTableRuleConfigs().add(vocDetailTableRule);
        
        // 数据源配置
        Map<String, DataSource> dataSourceMap = createDataSourceMap();
        
        return ShardingSphereDataSourceFactory.createDataSource(
            dataSourceMap, 
            Collections.singleton(shardingRuleConfig), 
            new Properties()
        );
    }
    
    private Map<String, DataSource> createDataSourceMap() {
        Map<String, DataSource> dataSourceMap = new HashMap<>();
        
        // 创建3个数据源
        for (int i = 0; i < 3; i++) {
            HikariDataSource dataSource = new HikariDataSource();
            dataSource.setJdbcUrl("jdbc:postgresql://db" + i + ":5432/voc_db");
            dataSource.setUsername("voc_user");
            dataSource.setPassword("voc_password");
            dataSourceMap.put("ds" + i, dataSource);
        }
        
        return dataSourceMap;
    }
}

public class TimeBasedShardingAlgorithm implements PreciseShardingAlgorithm<LocalDateTime> {
    
    @Override
    public String doSharding(Collection<String> tableNames, 
                           PreciseShardingValue<LocalDateTime> shardingValue) {
        LocalDateTime feedbackTime = shardingValue.getValue();
        int month = feedbackTime.getMonthValue() - 1; // 0-11
        
        String logicTableName = shardingValue.getLogicTableName();
        return logicTableName + "_" + month;
    }
}
```

### 3.3 弹性伸缩机制

**自动扩缩容策略**：
```java
@Service
public class AutoScalingService {
    
    @Autowired
    private KubernetesClient kubernetesClient;
    
    @Autowired
    private MetricsService metricsService;
    
    @Scheduled(fixedRate = 60000) // 每分钟检查一次
    public void checkAndScale() {
        List<ServiceMetrics> serviceMetrics = metricsService.getAllServiceMetrics();
        
        for (ServiceMetrics metrics : serviceMetrics) {
            ScalingDecision decision = makeScalingDecision(metrics);
            
            if (decision.shouldScale()) {
                executeScaling(metrics.getServiceName(), decision);
            }
        }
    }
    
    private ScalingDecision makeScalingDecision(ServiceMetrics metrics) {
        ScalingDecision decision = new ScalingDecision();
        
        // CPU使用率检查
        if (metrics.getCpuUsage() > 0.8) {
            decision.setScaleUp(true);
            decision.setReason("CPU使用率过高: " + metrics.getCpuUsage());
        } else if (metrics.getCpuUsage() < 0.3 && metrics.getCurrentReplicas() > metrics.getMinReplicas()) {
            decision.setScaleDown(true);
            decision.setReason("CPU使用率过低: " + metrics.getCpuUsage());
        }
        
        // 内存使用率检查
        if (metrics.getMemoryUsage() > 0.85) {
            decision.setScaleUp(true);
            decision.setReason("内存使用率过高: " + metrics.getMemoryUsage());
        }
        
        // 队列长度检查
        if (metrics.getQueueLength() > 1000) {
            decision.setScaleUp(true);
            decision.setReason("队列堆积严重: " + metrics.getQueueLength());
        }
        
        // 响应时间检查
        if (metrics.getAvgResponseTime() > 2000) {
            decision.setScaleUp(true);
            decision.setReason("响应时间过长: " + metrics.getAvgResponseTime() + "ms");
        }
        
        return decision;
    }
    
    private void executeScaling(String serviceName, ScalingDecision decision) {
        try {
            if (decision.isScaleUp()) {
                scaleUp(serviceName, decision.getTargetReplicas());
            } else if (decision.isScaleDown()) {
                scaleDown(serviceName, decision.getTargetReplicas());
            }
            
            // 记录扩缩容事件
            auditService.logScalingEvent(serviceName, decision);
            
        } catch (Exception e) {
            log.error("执行扩缩容失败: service={}, decision={}", serviceName, decision, e);
            alertService.sendScalingFailureAlert(serviceName, e);
        }
    }
}
```

---

## 📊 性能监控体系

### 4.1 监控指标体系

**应用性能监控**：
```java
@Component
public class ApplicationPerformanceMonitor {
    
    private final MeterRegistry meterRegistry;
    private final Timer requestTimer;
    private final Counter errorCounter;
    private final Gauge activeConnectionsGauge;
    
    public ApplicationPerformanceMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.requestTimer = Timer.builder("http.server.requests")
            .description("HTTP请求处理时间")
            .register(meterRegistry);
        this.errorCounter = Counter.builder("application.errors")
            .description("应用错误计数")
            .register(meterRegistry);
        this.activeConnectionsGauge = Gauge.builder("application.connections.active")
            .description("活跃连接数")
            .register(meterRegistry, this, ApplicationPerformanceMonitor::getActiveConnections);
    }
    
    @EventListener
    public void handleRequestCompletedEvent(RequestCompletedEvent event) {
        // 记录请求耗时
        requestTimer.record(event.getDuration(), TimeUnit.MILLISECONDS);
        
        // 记录错误
        if (event.isError()) {
            errorCounter.increment(
                Tags.of(
                    "error.type", event.getErrorType(),
                    "endpoint", event.getEndpoint()
                )
            );
        }
        
        // 记录业务指标
        recordBusinessMetrics(event);
    }
    
    private void recordBusinessMetrics(RequestCompletedEvent event) {
        if (event.getEndpoint().contains("/data-ingestion/")) {
            meterRegistry.counter("business.data.ingested")
                .increment(event.getRecordCount());
        } else if (event.getEndpoint().contains("/reports/")) {
            meterRegistry.counter("business.reports.generated")
                .increment();
        }
    }
    
    @Scheduled(fixedRate = 30000) // 每30秒更新一次
    public void updateSystemMetrics() {
        // JVM指标
        Runtime runtime = Runtime.getRuntime();
        meterRegistry.gauge("jvm.memory.used", runtime.totalMemory() - runtime.freeMemory());
        meterRegistry.gauge("jvm.memory.free", runtime.freeMemory());
        meterRegistry.gauge("jvm.memory.total", runtime.totalMemory());
        meterRegistry.gauge("jvm.memory.max", runtime.maxMemory());
        
        // 系统指标
        OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
        meterRegistry.gauge("system.cpu.usage", osBean.getProcessCpuLoad());
        meterRegistry.gauge("system.load.average", osBean.getSystemLoadAverage());
        
        // 线程指标
        ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
        meterRegistry.gauge("jvm.threads.live", threadBean.getThreadCount());
        meterRegistry.gauge("jvm.threads.daemon", threadBean.getDaemonThreadCount());
    }
}
```

**数据库性能监控**：
```java
@Component
public class DatabasePerformanceMonitor {
    
    @Autowired
    private DataSource dataSource;
    
    @Autowired
    private MeterRegistry meterRegistry;
    
    @EventListener
    public void handleQueryExecutedEvent(QueryExecutedEvent event) {
        // 记录查询执行时间
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("database.query.duration")
            .tag("query.type", event.getQueryType())
            .tag("table", event.getTableName())
            .register(meterRegistry));
        
        // 记录慢查询
        if (event.getDuration() > 1000) {
            meterRegistry.counter("database.slow.queries")
                .increment();
            
            log.warn("慢查询检测: SQL={}, Duration={}ms", 
                event.getSql(), event.getDuration());
        }
    }
    
    @Scheduled(fixedRate = 60000) // 每分钟更新一次
    public void updateConnectionPoolMetrics() {
        if (dataSource instanceof HikariDataSource) {
            HikariDataSource hikariDS = (HikariDataSource) dataSource;
            HikariPoolMXBean poolBean = hikariDS.getHikariPoolMXBean();
            
            meterRegistry.gauge("hikari.connections.active", poolBean.getActiveConnections());
            meterRegistry.gauge("hikari.connections.idle", poolBean.getIdleConnections());
            meterRegistry.gauge("hikari.connections.total", poolBean.getTotalConnections());
            meterRegistry.gauge("hikari.connections.waiting", poolBean.getThreadsAwaitingConnection());
        }
    }
}
```

### 4.2 性能告警机制

**告警规则配置**：
```yaml
# Prometheus告警规则
groups:
  - name: voc.performance
    rules:
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, http_server_requests_seconds_bucket{job="voc-api"}) > 0.5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "API响应时间过长"
          description: "95%的请求响应时间超过500ms，当前值: {{ $value }}s"
      
      - alert: HighErrorRate
        expr: rate(http_server_requests_total{status=~"5.."}[5m]) / rate(http_server_requests_total[5m]) > 0.01
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "错误率过高"
          description: "错误率超过1%，当前值: {{ $value | humanizePercentage }}"
      
      - alert: HighCPUUsage
        expr: rate(process_cpu_seconds_total[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "CPU使用率过高"
          description: "CPU使用率超过80%，当前值: {{ $value }}%"
      
      - alert: HighMemoryUsage
        expr: (jvm_memory_used_bytes / jvm_memory_max_bytes) * 100 > 85
        for: 3m
        labels:
          severity: critical
        annotations:
          summary: "内存使用率过高"
          description: "内存使用率超过85%，当前值: {{ $value }}%"
```

**智能告警优化**：
```java
@Service
public class IntelligentAlertingService {
    
    @Autowired
    private AlertRuleEngine alertRuleEngine;
    
    @Autowired
    private MachineLearningService mlService;
    
    public void evaluateAlert(MetricData metricData) {
        // 基于历史数据的动态阈值
        Double dynamicThreshold = mlService.predictThreshold(
            metricData.getMetricName(), 
            metricData.getHistoricalData()
        );
        
        // 考虑业务上下文的告警
        AlertContext context = AlertContext.builder()
            .currentTime(LocalDateTime.now())
            .businessHours(isBusinessHours())
            .seasonality(getSeason())
            .workload(getCurrentWorkload())
            .build();
        
        // 智能告警评估
        AlertEvaluation evaluation = alertRuleEngine.evaluate(
            metricData, 
            dynamicThreshold, 
            context
        );
        
        if (evaluation.shouldAlert()) {
            // 告警降噪处理
            if (!isDuplicateAlert(evaluation)) {
                sendAlert(evaluation);
            }
        }
    }
    
    private boolean isDuplicateAlert(AlertEvaluation evaluation) {
        // 检查是否在时间窗口内有相同告警
        Duration timeWindow = Duration.ofMinutes(10);
        return alertHistoryService.hasAlertInTimeWindow(
            evaluation.getAlertType(),
            evaluation.getMetricName(),
            timeWindow
        );
    }
}
```

---

## 💡 性能优化最佳实践

### 5.1 代码层面优化

**JVM调优配置**：
```bash
# 生产环境JVM参数
JAVA_OPTS="
-Xms4g -Xmx4g
-XX:+UseG1GC
-XX:G1HeapRegionSize=16m
-XX:G1ReservePercent=25
-XX:InitiatingHeapOccupancyPercent=30
-XX:+UseStringDeduplication
-XX:+UseCompressedOops
-XX:+UseCompressedClassPointers
-XX:+DisableExplicitGC
-XX:+ExitOnOutOfMemoryError
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/var/log/heapdump.hprof
-Djava.security.egd=file:/dev/./urandom
-Dfile.encoding=UTF-8
-Duser.timezone=Asia/Shanghai
"
```

**异步处理优化**：
```java
@Service
public class AsyncProcessingOptimization {
    
    @Async("dataProcessingExecutor")
    @Retryable(value = {TransientDataException.class}, maxAttempts = 3)
    public CompletableFuture<ProcessingResult> processDataAsync(DataBatch batch) {
        try {
            // 异步数据处理逻辑
            ProcessingResult result = performProcessing(batch);
            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            log.error("异步处理失败: batchId={}", batch.getId(), e);
            return CompletableFuture.failedFuture(e);
        }
    }
    
    @Bean("dataProcessingExecutor")
    public ThreadPoolTaskExecutor dataProcessingExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数 = CPU核心数
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors());
        
        // 最大线程数 = CPU核心数 * 2
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2);
        
        // 队列容量
        executor.setQueueCapacity(1000);
        
        // 线程名前缀
        executor.setThreadNamePrefix("data-processing-");
        
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 优雅关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        return executor;
    }
}
```

### 5.2 架构层面优化

**读写分离优化**：
```java
@Configuration
public class ReadWriteSplitConfiguration {
    
    @Bean
    @Primary
    public DataSource dataSource() {
        return DataSourceBuilder.create()
            .type(HikariDataSource.class)
            .url("***************************************")
            .username("voc_user")
            .password("voc_password")
            .build();
    }
    
    @Bean("readOnlyDataSource")
    public DataSource readOnlyDataSource() {
        return DataSourceBuilder.create()
            .type(HikariDataSource.class)
            .url("**************************************")
            .username("voc_readonly")
            .password("voc_password")
            .build();
    }
    
    @Bean
    public PlatformTransactionManager transactionManager() {
        return new DataSourceTransactionManager(dataSource());
    }
}

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Transactional(readOnly = true)
public @interface ReadOnly {
}

@Aspect
@Component
public class ReadOnlyAspect {
    
    @Around("@annotation(readOnly)")
    public Object routeToReadOnlyDataSource(ProceedingJoinPoint joinPoint, ReadOnly readOnly) throws Throwable {
        try {
            // 设置数据源路由键
            DataSourceContextHolder.setDataSourceKey("readOnly");
            return joinPoint.proceed();
        } finally {
            // 清除数据源路由键
            DataSourceContextHolder.clearDataSourceKey();
        }
    }
}
```

---

## 📈 容量规划

### 6.1 容量评估模型

**资源需求计算**：
```java
@Service
public class CapacityPlanningService {
    
    public CapacityPlan calculateCapacityRequirements(BusinessGrowthProjection projection) {
        // 数据量增长预测
        long currentDataVolume = getCurrentDataVolume();
        long projectedDataVolume = currentDataVolume * projection.getDataGrowthMultiplier();
        
        // 用户量增长预测
        int currentUsers = getCurrentActiveUsers();
        int projectedUsers = (int) (currentUsers * projection.getUserGrowthMultiplier());
        
        // 计算存储需求
        StorageRequirements storage = calculateStorageRequirements(projectedDataVolume);
        
        // 计算计算资源需求
        ComputeRequirements compute = calculateComputeRequirements(projectedUsers, projectedDataVolume);
        
        // 计算网络资源需求
        NetworkRequirements network = calculateNetworkRequirements(projectedUsers);
        
        return CapacityPlan.builder()
            .projectionPeriod(projection.getProjectionPeriod())
            .storageRequirements(storage)
            .computeRequirements(compute)
            .networkRequirements(network)
            .estimatedCost(calculateCost(storage, compute, network))
            .build();
    }
    
    private ComputeRequirements calculateComputeRequirements(int users, long dataVolume) {
        // 基于用户数和数据量计算CPU需求
        int cpuCores = Math.max(4, users / 100 + (int)(dataVolume / 1_000_000));
        
        // 基于数据处理需求计算内存
        long memoryGB = Math.max(8, dataVolume / 100_000_000 * 16);
        
        // 计算实例数量
        int instances = Math.max(2, users / 500);
        
        return ComputeRequirements.builder()
            .cpuCores(cpuCores)
            .memoryGB(memoryGB)
            .instances(instances)
            .build();
    }
}
```

### 6.2 性能容量矩阵

**负载测试规划**：
```yaml
# 负载测试矩阵
loadTestMatrix:
  scenarios:
    - name: "正常负载"
      users: 500
      duration: "30m"
      expectedThroughput: "200 RPS"
      expectedResponseTime: "< 200ms"
      
    - name: "高峰负载"
      users: 1000
      duration: "1h"
      expectedThroughput: "400 RPS"
      expectedResponseTime: "< 500ms"
      
    - name: "压力测试"
      users: 2000
      duration: "15m"
      expectedThroughput: "600 RPS"
      expectedResponseTime: "< 1000ms"
      
    - name: "容量测试"
      users: 5000
      duration: "2h"
      expectedThroughput: "800 RPS"
      maxAcceptableResponseTime: "< 2000ms"

  resourceLimits:
    cpu: "< 80%"
    memory: "< 85%"
    disk: "< 90%"
    network: "< 70%"
```

---

**文档维护**: 性能工程师、系统架构师  
**审核**: 技术负责人、运维团队  
**下次更新**: 性能要求变更时及时更新 