# 行为型设计模式应用

**文档版本**: v1.0.0  
**创建日期**: 2024年1月  
**最后更新**: 2024年1月  
**文档状态**: 设计阶段

---

## 📋 文档概述

本文档描述了通用VOC报表系统中应用的行为型设计模式，包括观察者模式、策略模式、责任链模式、命令模式、状态模式、模板方法模式、迭代器模式等，详细说明了每种模式的应用场景、实现方式和设计考虑。

---

## 👁️ 观察者模式（Observer Pattern）

### 1.1 数据处理事件监听器

**应用场景**：当数据处理状态发生变化时，通知相关的监听器进行后续处理

**实现代码**：
```java
// 数据处理事件接口
public interface DataProcessingEvent {
    String getEventType();
    Object getEventData();
    LocalDateTime getTimestamp();
    String getSourceId();
}

// 数据处理监听器接口
public interface DataProcessingListener {
    void onDataProcessingEvent(DataProcessingEvent event);
    boolean supports(String eventType);
}

// 数据处理事件发布器
@Component
public class DataProcessingEventPublisher {
    
    private final List<DataProcessingListener> listeners = new CopyOnWriteArrayList<>();
    private final ExecutorService eventExecutor = Executors.newCachedThreadPool();
    
    public void addListener(DataProcessingListener listener) {
        listeners.add(listener);
    }
    
    public void removeListener(DataProcessingListener listener) {
        listeners.remove(listener);
    }
    
    public void publishEvent(DataProcessingEvent event) {
        listeners.stream()
            .filter(listener -> listener.supports(event.getEventType()))
            .forEach(listener -> {
                eventExecutor.submit(() -> {
                    try {
                        listener.onDataProcessingEvent(event);
                    } catch (Exception e) {
                        log.error("事件处理失败", e);
                    }
                });
            });
    }
}

// 具体事件实现
public class DataAnalysisCompletedEvent implements DataProcessingEvent {
    private final String eventType = "DATA_ANALYSIS_COMPLETED";
    private final AnalysisResult analysisResult;
    private final LocalDateTime timestamp;
    private final String sourceId;
    
    // 构造函数、getter方法等
}

// 具体监听器实现
@Component
public class ReportGenerationListener implements DataProcessingListener {
    
    private final ReportService reportService;
    
    @Override
    public void onDataProcessingEvent(DataProcessingEvent event) {
        if (event instanceof DataAnalysisCompletedEvent) {
            DataAnalysisCompletedEvent analysisEvent = (DataAnalysisCompletedEvent) event;
            reportService.generateReportsAsync(analysisEvent.getAnalysisResult());
        }
    }
    
    @Override
    public boolean supports(String eventType) {
        return "DATA_ANALYSIS_COMPLETED".equals(eventType);
    }
}
```

**设计优势**：
- 解耦事件发送者和接收者
- 支持动态添加和移除监听器
- 异步事件处理提高系统性能
- 易于扩展新的事件类型

---

## 🎯 策略模式（Strategy Pattern）

### 2.1 行业分析策略

**应用场景**：根据不同行业特点选择不同的数据分析策略

**实现代码**：
```java
// 分析策略接口
public interface AnalysisStrategy {
    AnalysisResult analyze(RawDataRecord data, IndustryConfig config);
    boolean supports(String industryType);
    AnalysisMetadata getMetadata();
}

// 汽车行业分析策略
@Component
public class AutomotiveAnalysisStrategy implements AnalysisStrategy {
    
    private final SentimentAnalyzer sentimentAnalyzer;
    private final IntentClassifier intentClassifier;
    private final TopicExtractor topicExtractor;
    
    @Override
    public AnalysisResult analyze(RawDataRecord data, IndustryConfig config) {
        AnalysisResult result = new AnalysisResult();
        
        // 汽车行业特定的情感分析
        SentimentResult sentiment = sentimentAnalyzer.analyze(
            data.getContent(), 
            config.getAutomotiveKeywords()
        );
        result.setSentiment(sentiment);
        
        // 汽车行业特定的意图识别
        IntentResult intent = intentClassifier.classify(
            data.getContent(),
            config.getAutomotiveIntentDictionary()
        );
        result.setIntent(intent);
        
        // 汽车行业特定的主题提取
        TopicResult topic = topicExtractor.extract(
            data.getContent(),
            config.getAutomotiveTopicModel()
        );
        result.setTopic(topic);
        
        return result;
    }
    
    @Override
    public boolean supports(String industryType) {
        return "automotive".equals(industryType);
    }
}

// 美妆行业分析策略
@Component
public class CosmeticAnalysisStrategy implements AnalysisStrategy {
    
    @Override
    public AnalysisResult analyze(RawDataRecord data, IndustryConfig config) {
        AnalysisResult result = new AnalysisResult();
        
        // 美妆行业特定的分析逻辑
        // 重点关注效果描述、肌肤类型、使用感受等
        
        return result;
    }
    
    @Override
    public boolean supports(String industryType) {
        return "cosmetic".equals(industryType);
    }
}

// 策略上下文
@Service
public class AnalysisStrategyContext {
    
    private final Map<String, AnalysisStrategy> strategies;
    
    public AnalysisStrategyContext(List<AnalysisStrategy> strategyList) {
        this.strategies = strategyList.stream()
            .collect(Collectors.toMap(
                strategy -> {
                    // 从类名推断行业类型
                    String className = strategy.getClass().getSimpleName();
                    return className.replace("AnalysisStrategy", "").toLowerCase();
                },
                strategy -> strategy
            ));
    }
    
    public AnalysisResult executeAnalysis(RawDataRecord data, IndustryConfig config) {
        AnalysisStrategy strategy = strategies.get(config.getIndustryType());
        if (strategy == null) {
            throw new UnsupportedIndustryException("不支持的行业类型: " + config.getIndustryType());
        }
        
        return strategy.analyze(data, config);
    }
}
```

**设计优势**：
- 支持运行时切换分析算法
- 易于添加新的行业分析策略
- 将算法变化与使用算法的代码隔离
- 提高代码的可维护性和扩展性

---

## 🔗 责任链模式（Chain of Responsibility Pattern）

### 3.1 数据处理管道

**应用场景**：数据从接入到最终存储需要经过多个处理步骤，每个步骤负责特定的处理逻辑

**实现代码**：
```java
// 数据处理处理器接口
public abstract class DataProcessor {
    
    protected DataProcessor nextProcessor;
    
    public void setNext(DataProcessor processor) {
        this.nextProcessor = processor;
    }
    
    public final ProcessingResult process(DataContext context) {
        ProcessingResult result = doProcess(context);
        
        if (result.isSuccess() && nextProcessor != null) {
            return nextProcessor.process(context);
        }
        
        return result;
    }
    
    protected abstract ProcessingResult doProcess(DataContext context);
    protected abstract boolean canHandle(DataContext context);
}

// 数据验证处理器
@Component
public class DataValidationProcessor extends DataProcessor {
    
    private final DataValidator validator;
    
    @Override
    protected ProcessingResult doProcess(DataContext context) {
        ValidationResult validation = validator.validate(context.getRawData());
        
        if (!validation.isValid()) {
            return ProcessingResult.failure("数据验证失败: " + validation.getErrors());
        }
        
        context.setValidationResult(validation);
        return ProcessingResult.success("数据验证通过");
    }
    
    @Override
    protected boolean canHandle(DataContext context) {
        return context.getRawData() != null;
    }
}

// 数据清洗处理器
@Component
public class DataCleaningProcessor extends DataProcessor {
    
    private final DataCleaner cleaner;
    
    @Override
    protected ProcessingResult doProcess(DataContext context) {
        CleaningResult cleaning = cleaner.clean(context.getRawData());
        
        context.setCleanedData(cleaning.getCleanedData());
        context.setCleaningMetrics(cleaning.getMetrics());
        
        return ProcessingResult.success("数据清洗完成");
    }
    
    @Override
    protected boolean canHandle(DataContext context) {
        return context.getValidationResult() != null && 
               context.getValidationResult().isValid();
    }
}

// AI分析处理器
@Component
public class AIAnalysisProcessor extends DataProcessor {
    
    private final AIAnalysisService analysisService;
    
    @Override
    protected ProcessingResult doProcess(DataContext context) {
        AnalysisResult analysis = analysisService.analyze(
            context.getCleanedData(),
            context.getIndustryConfig()
        );
        
        context.setAnalysisResult(analysis);
        return ProcessingResult.success("AI分析完成");
    }
    
    @Override
    protected boolean canHandle(DataContext context) {
        return context.getCleanedData() != null;
    }
}

// 数据存储处理器
@Component
public class DataStorageProcessor extends DataProcessor {
    
    private final DataRepository repository;
    
    @Override
    protected ProcessingResult doProcess(DataContext context) {
        ProcessedDataRecord record = buildDataRecord(context);
        repository.save(record);
        
        context.setStoredRecord(record);
        return ProcessingResult.success("数据存储完成");
    }
    
    @Override
    protected boolean canHandle(DataContext context) {
        return context.getAnalysisResult() != null;
    }
}

// 处理链构建器
@Component
public class DataProcessingChainBuilder {
    
    private final DataValidationProcessor validationProcessor;
    private final DataCleaningProcessor cleaningProcessor;
    private final AIAnalysisProcessor analysisProcessor;
    private final DataStorageProcessor storageProcessor;
    
    public DataProcessor buildChain() {
        validationProcessor.setNext(cleaningProcessor);
        cleaningProcessor.setNext(analysisProcessor);
        analysisProcessor.setNext(storageProcessor);
        
        return validationProcessor;
    }
}
```

**设计优势**：
- 解耦请求发送者和接收者
- 动态组织和分配责任
- 易于扩展新的处理步骤
- 提高系统的灵活性

---

## 💾 命令模式（Command Pattern）

### 4.1 报表生成命令

**应用场景**：将报表生成请求封装成命令对象，支持排队、记录和撤销操作

**实现代码**：
```java
// 命令接口
public interface Command {
    CommandResult execute();
    CommandResult undo();
    boolean canUndo();
    String getCommandId();
    CommandMetadata getMetadata();
}

// 报表生成命令
public class ReportGenerationCommand implements Command {
    
    private final String commandId;
    private final ReportRequest request;
    private final ReportService reportService;
    private final CommandMetadata metadata;
    private ReportResult lastResult;
    
    public ReportGenerationCommand(String commandId, ReportRequest request, 
                                 ReportService reportService) {
        this.commandId = commandId;
        this.request = request;
        this.reportService = reportService;
        this.metadata = new CommandMetadata("REPORT_GENERATION", LocalDateTime.now());
    }
    
    @Override
    public CommandResult execute() {
        try {
            lastResult = reportService.generateReport(request);
            return CommandResult.success("报表生成成功", lastResult);
        } catch (Exception e) {
            return CommandResult.failure("报表生成失败: " + e.getMessage());
        }
    }
    
    @Override
    public CommandResult undo() {
        if (lastResult != null && lastResult.getReportId() != null) {
            reportService.deleteReport(lastResult.getReportId());
            return CommandResult.success("报表删除成功");
        }
        return CommandResult.failure("无法撤销：没有可撤销的报表");
    }
    
    @Override
    public boolean canUndo() {
        return lastResult != null && lastResult.getReportId() != null;
    }
}

// 数据分析命令
public class DataAnalysisCommand implements Command {
    
    private final String commandId;
    private final AnalysisRequest request;
    private final AnalysisService analysisService;
    private AnalysisResult lastResult;
    
    @Override
    public CommandResult execute() {
        try {
            lastResult = analysisService.analyzeData(request);
            return CommandResult.success("数据分析完成", lastResult);
        } catch (Exception e) {
            return CommandResult.failure("数据分析失败: " + e.getMessage());
        }
    }
    
    @Override
    public CommandResult undo() {
        // 数据分析通常不需要撤销，但可以清除缓存
        if (lastResult != null) {
            analysisService.clearAnalysisCache(request.getRequestId());
            return CommandResult.success("分析缓存已清除");
        }
        return CommandResult.failure("无法撤销：没有可撤销的分析结果");
    }
}

// 命令调用器
@Service
public class CommandInvoker {
    
    private final Queue<Command> commandQueue = new ConcurrentLinkedQueue<>();
    private final Stack<Command> commandHistory = new Stack<>();
    private final ExecutorService commandExecutor = Executors.newFixedThreadPool(10);
    
    public CompletableFuture<CommandResult> executeCommand(Command command) {
        commandQueue.offer(command);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                CommandResult result = command.execute();
                if (result.isSuccess()) {
                    commandHistory.push(command);
                }
                return result;
            } catch (Exception e) {
                return CommandResult.failure("命令执行异常: " + e.getMessage());
            }
        }, commandExecutor);
    }
    
    public CommandResult undoLastCommand() {
        if (commandHistory.isEmpty()) {
            return CommandResult.failure("没有可撤销的命令");
        }
        
        Command lastCommand = commandHistory.pop();
        if (lastCommand.canUndo()) {
            return lastCommand.undo();
        } else {
            return CommandResult.failure("该命令不支持撤销");
        }
    }
    
    public List<CommandMetadata> getCommandHistory() {
        return commandHistory.stream()
            .map(Command::getMetadata)
            .collect(Collectors.toList());
    }
}
```

**设计优势**：
- 将请求封装成对象
- 支持操作的排队和记录
- 支持撤销操作
- 解耦调用者和接收者

---

## 🔄 状态模式（State Pattern）

### 5.1 数据处理状态管理

**应用场景**：数据处理过程中的状态转换管理

**实现代码**：
```java
// 数据处理状态接口
public interface DataProcessingState {
    ProcessingResult process(DataProcessingContext context);
    boolean canTransitionTo(DataProcessingState targetState);
    String getStateName();
}

// 数据处理上下文
public class DataProcessingContext {
    
    private DataProcessingState currentState;
    private final ProcessingData data;
    private final Map<String, Object> contextAttributes = new HashMap<>();
    
    public DataProcessingContext(ProcessingData data) {
        this.data = data;
        this.currentState = new PendingState();
    }
    
    public void setState(DataProcessingState state) {
        if (currentState.canTransitionTo(state)) {
            this.currentState = state;
        } else {
            throw new IllegalStateTransitionException(
                "不能从 " + currentState.getStateName() + " 转换到 " + state.getStateName()
            );
        }
    }
    
    public ProcessingResult process() {
        return currentState.process(this);
    }
}

// 待处理状态
public class PendingState implements DataProcessingState {
    
    @Override
    public ProcessingResult process(DataProcessingContext context) {
        // 开始处理，转换到处理中状态
        context.setState(new ProcessingState());
        return ProcessingResult.success("开始处理数据");
    }
    
    @Override
    public boolean canTransitionTo(DataProcessingState targetState) {
        return targetState instanceof ProcessingState || 
               targetState instanceof CancelledState;
    }
    
    @Override
    public String getStateName() {
        return "PENDING";
    }
}

// 处理中状态
public class ProcessingState implements DataProcessingState {
    
    private final DataProcessor processor;
    
    @Override
    public ProcessingResult process(DataProcessingContext context) {
        try {
            ProcessingResult result = processor.process(context.getData());
            
            if (result.isSuccess()) {
                context.setState(new CompletedState());
                return ProcessingResult.success("数据处理完成");
            } else {
                context.setState(new FailedState());
                return ProcessingResult.failure("数据处理失败: " + result.getMessage());
            }
        } catch (Exception e) {
            context.setState(new FailedState());
            return ProcessingResult.failure("处理异常: " + e.getMessage());
        }
    }
    
    @Override
    public boolean canTransitionTo(DataProcessingState targetState) {
        return targetState instanceof CompletedState || 
               targetState instanceof FailedState ||
               targetState instanceof CancelledState;
    }
    
    @Override
    public String getStateName() {
        return "PROCESSING";
    }
}

// 完成状态
public class CompletedState implements DataProcessingState {
    
    @Override
    public ProcessingResult process(DataProcessingContext context) {
        return ProcessingResult.success("数据已处理完成，无需重复处理");
    }
    
    @Override
    public boolean canTransitionTo(DataProcessingState targetState) {
        return targetState instanceof PendingState; // 支持重新处理
    }
    
    @Override
    public String getStateName() {
        return "COMPLETED";
    }
}

// 失败状态
public class FailedState implements DataProcessingState {
    
    @Override
    public ProcessingResult process(DataProcessingContext context) {
        // 可以选择重试或转到待处理状态
        context.setState(new PendingState());
        return ProcessingResult.success("重置为待处理状态，准备重试");
    }
    
    @Override
    public boolean canTransitionTo(DataProcessingState targetState) {
        return targetState instanceof PendingState ||
               targetState instanceof CancelledState;
    }
    
    @Override
    public String getStateName() {
        return "FAILED";
    }
}
```

**设计优势**：
- 将状态转换逻辑封装在状态类中
- 避免大量的if-else条件判断
- 易于扩展新的状态
- 状态转换规则清晰

---

## 📄 模板方法模式（Template Method Pattern）

### 6.1 报表生成模板

**应用场景**：不同类型的报表有相同的生成流程，但具体实现细节不同

**实现代码**：
```java
// 抽象报表生成器
public abstract class AbstractReportGenerator {
    
    // 模板方法，定义报表生成流程
    public final ReportResult generateReport(ReportRequest request) {
        try {
            // 1. 数据收集
            ReportData data = collectData(request);
            
            // 2. 数据处理
            ProcessedData processedData = processData(data);
            
            // 3. 报表渲染
            RenderedReport report = renderReport(processedData, request);
            
            // 4. 格式化输出
            FormattedReport formattedReport = formatOutput(report, request.getOutputFormat());
            
            // 5. 保存报表
            String reportId = saveReport(formattedReport);
            
            return ReportResult.success(reportId, formattedReport);
            
        } catch (Exception e) {
            return ReportResult.failure("报表生成失败: " + e.getMessage());
        }
    }
    
    // 抽象方法，由子类实现
    protected abstract ReportData collectData(ReportRequest request);
    protected abstract ProcessedData processData(ReportData data);
    protected abstract RenderedReport renderReport(ProcessedData data, ReportRequest request);
    
    // 具体方法，提供默认实现，子类可以重写
    protected FormattedReport formatOutput(RenderedReport report, OutputFormat format) {
        switch (format) {
            case PDF:
                return new PdfFormatter().format(report);
            case EXCEL:
                return new ExcelFormatter().format(report);
            case HTML:
                return new HtmlFormatter().format(report);
            default:
                throw new UnsupportedFormatException("不支持的输出格式: " + format);
        }
    }
    
    protected String saveReport(FormattedReport report) {
        String reportId = UUID.randomUUID().toString();
        reportRepository.save(reportId, report);
        return reportId;
    }
}

// 情感分析报表生成器
@Component
public class SentimentAnalysisReportGenerator extends AbstractReportGenerator {
    
    private final SentimentDataService sentimentDataService;
    private final SentimentChartRenderer chartRenderer;
    
    @Override
    protected ReportData collectData(ReportRequest request) {
        SentimentAnalysisRequest sentimentRequest = (SentimentAnalysisRequest) request;
        
        List<SentimentRecord> records = sentimentDataService.getSentimentData(
            sentimentRequest.getStartDate(),
            sentimentRequest.getEndDate(),
            sentimentRequest.getFilters()
        );
        
        return new SentimentReportData(records);
    }
    
    @Override
    protected ProcessedData processData(ReportData data) {
        SentimentReportData sentimentData = (SentimentReportData) data;
        
        // 计算情感分布
        SentimentDistribution distribution = calculateSentimentDistribution(sentimentData.getRecords());
        
        // 计算趋势
        SentimentTrend trend = calculateSentimentTrend(sentimentData.getRecords());
        
        // 识别异常点
        List<SentimentAnomaly> anomalies = identifyAnomalies(sentimentData.getRecords());
        
        return new ProcessedSentimentData(distribution, trend, anomalies);
    }
    
    @Override
    protected RenderedReport renderReport(ProcessedData data, ReportRequest request) {
        ProcessedSentimentData sentimentData = (ProcessedSentimentData) data;
        
        // 生成图表
        Chart distributionChart = chartRenderer.renderDistributionChart(sentimentData.getDistribution());
        Chart trendChart = chartRenderer.renderTrendChart(sentimentData.getTrend());
        
        // 生成摘要
        ReportSummary summary = generateSummary(sentimentData);
        
        return new RenderedSentimentReport(distributionChart, trendChart, summary);
    }
}

// 主题分析报表生成器
@Component
public class TopicAnalysisReportGenerator extends AbstractReportGenerator {
    
    private final TopicDataService topicDataService;
    private final TopicVisualizationService visualizationService;
    
    @Override
    protected ReportData collectData(ReportRequest request) {
        TopicAnalysisRequest topicRequest = (TopicAnalysisRequest) request;
        
        List<TopicRecord> records = topicDataService.getTopicData(
            topicRequest.getStartDate(),
            topicRequest.getEndDate(),
            topicRequest.getIndustryType()
        );
        
        return new TopicReportData(records);
    }
    
    @Override
    protected ProcessedData processData(ReportData data) {
        TopicReportData topicData = (TopicReportData) data;
        
        // 计算主题分布
        TopicDistribution distribution = calculateTopicDistribution(topicData.getRecords());
        
        // 分析主题关联
        TopicCorrelation correlation = analyzeTopicCorrelation(topicData.getRecords());
        
        // 识别热门主题
        List<HotTopic> hotTopics = identifyHotTopics(topicData.getRecords());
        
        return new ProcessedTopicData(distribution, correlation, hotTopics);
    }
    
    @Override
    protected RenderedReport renderReport(ProcessedData data, ReportRequest request) {
        ProcessedTopicData topicData = (ProcessedTopicData) data;
        
        // 生成词云图
        WordCloudChart wordCloud = visualizationService.generateWordCloud(topicData.getDistribution());
        
        // 生成关联图
        CorrelationChart correlationChart = visualizationService.generateCorrelationChart(topicData.getCorrelation());
        
        // 生成趋势图
        TrendChart trendChart = visualizationService.generateTrendChart(topicData.getHotTopics());
        
        return new RenderedTopicReport(wordCloud, correlationChart, trendChart);
    }
}
```

**设计优势**：
- 代码复用，避免重复实现
- 框架控制整体流程
- 子类专注于特定实现
- 易于扩展新的报表类型

---

## 🔄 迭代器模式（Iterator Pattern）

### 7.1 大数据集遍历

**应用场景**：对大数据集进行分批处理，避免内存溢出

**实现代码**：
```java
// 数据迭代器接口
public interface DataIterator<T> {
    boolean hasNext();
    T next();
    void remove();
    void reset();
    long getCurrentPosition();
    long getTotalCount();
}

// 分页数据迭代器
public class PagedDataIterator<T> implements DataIterator<T> {
    
    private final DataRepository<T> repository;
    private final QueryCriteria criteria;
    private final int pageSize;
    private int currentPage = 0;
    private List<T> currentPageData = new ArrayList<>();
    private int currentIndex = 0;
    private long totalCount = -1;
    
    public PagedDataIterator(DataRepository<T> repository, QueryCriteria criteria, int pageSize) {
        this.repository = repository;
        this.criteria = criteria;
        this.pageSize = pageSize;
        loadNextPage();
    }
    
    @Override
    public boolean hasNext() {
        if (currentIndex < currentPageData.size()) {
            return true;
        }
        
        // 当前页数据已遍历完，尝试加载下一页
        if (currentPageData.size() == pageSize) {
            loadNextPage();
            return currentIndex < currentPageData.size();
        }
        
        return false;
    }
    
    @Override
    public T next() {
        if (!hasNext()) {
            throw new NoSuchElementException("没有更多数据");
        }
        
        return currentPageData.get(currentIndex++);
    }
    
    @Override
    public void remove() {
        throw new UnsupportedOperationException("不支持删除操作");
    }
    
    @Override
    public void reset() {
        currentPage = 0;
        currentIndex = 0;
        currentPageData.clear();
        loadNextPage();
    }
    
    private void loadNextPage() {
        PageRequest pageRequest = PageRequest.of(currentPage, pageSize);
        Page<T> page = repository.findByCriteria(criteria, pageRequest);
        
        currentPageData = page.getContent();
        currentIndex = 0;
        currentPage++;
        
        if (totalCount == -1) {
            totalCount = page.getTotalElements();
        }
    }
    
    @Override
    public long getCurrentPosition() {
        return (long) (currentPage - 1) * pageSize + currentIndex;
    }
    
    @Override
    public long getTotalCount() {
        return totalCount;
    }
}

// 流式数据迭代器
public class StreamDataIterator implements DataIterator<DataRecord> {
    
    private final KafkaConsumer<String, String> consumer;
    private final String topic;
    private final DataDeserializer deserializer;
    private final Queue<DataRecord> buffer = new ConcurrentLinkedQueue<>();
    private final AtomicLong processedCount = new AtomicLong(0);
    private volatile boolean running = true;
    
    public StreamDataIterator(KafkaConsumer<String, String> consumer, String topic) {
        this.consumer = consumer;
        this.topic = topic;
        this.deserializer = new DataDeserializer();
        
        consumer.subscribe(Collections.singletonList(topic));
        startPolling();
    }
    
    @Override
    public boolean hasNext() {
        return running || !buffer.isEmpty();
    }
    
    @Override
    public DataRecord next() {
        while (buffer.isEmpty() && running) {
            try {
                Thread.sleep(100); // 等待新数据
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("线程被中断", e);
            }
        }
        
        DataRecord record = buffer.poll();
        if (record != null) {
            processedCount.incrementAndGet();
        }
        return record;
    }
    
    private void startPolling() {
        Thread pollingThread = new Thread(() -> {
            while (running) {
                try {
                    ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(1000));
                    for (ConsumerRecord<String, String> record : records) {
                        DataRecord dataRecord = deserializer.deserialize(record.value());
                        buffer.offer(dataRecord);
                    }
                } catch (Exception e) {
                    log.error("数据轮询异常", e);
                }
            }
        });
        pollingThread.setDaemon(true);
        pollingThread.start();
    }
    
    public void stop() {
        running = false;
        consumer.close();
    }
    
    @Override
    public long getCurrentPosition() {
        return processedCount.get();
    }
}

// 可迭代数据集合
public class IterableDataCollection<T> implements Iterable<T> {
    
    private final DataIteratorFactory<T> iteratorFactory;
    private final QueryCriteria criteria;
    
    public IterableDataCollection(DataIteratorFactory<T> iteratorFactory, QueryCriteria criteria) {
        this.iteratorFactory = iteratorFactory;
        this.criteria = criteria;
    }
    
    @Override
    public DataIterator<T> iterator() {
        return iteratorFactory.createIterator(criteria);
    }
    
    // 支持并行处理
    public void processInParallel(Consumer<T> processor, int parallelism) {
        ForkJoinPool customThreadPool = new ForkJoinPool(parallelism);
        try {
            customThreadPool.submit(() -> 
                StreamSupport.stream(spliterator(), true)
                    .forEach(processor)
            ).get();
        } catch (Exception e) {
            throw new RuntimeException("并行处理失败", e);
        } finally {
            customThreadPool.shutdown();
        }
    }
    
    // 支持批量处理
    public void processBatch(int batchSize, Consumer<List<T>> batchProcessor) {
        DataIterator<T> iterator = iterator();
        List<T> batch = new ArrayList<>(batchSize);
        
        while (iterator.hasNext()) {
            batch.add(iterator.next());
            
            if (batch.size() == batchSize) {
                batchProcessor.accept(new ArrayList<>(batch));
                batch.clear();
            }
        }
        
        // 处理最后一批数据
        if (!batch.isEmpty()) {
            batchProcessor.accept(batch);
        }
    }
}
```

**设计优势**：
- 统一的遍历接口
- 支持大数据集的内存优化遍历
- 支持不同数据源的统一访问
- 易于扩展新的迭代策略

---

## 🤝 中介者模式（Mediator Pattern）

### 8.1 组件间通信中介

**应用场景**：系统中多个组件需要相互通信，通过中介者统一管理通信逻辑

**实现代码**：
```java
// 中介者接口
public interface SystemMediator {
    void notify(Component sender, String event, Object data);
    void registerComponent(Component component);
    void unregisterComponent(Component component);
}

// 系统组件基类
public abstract class Component {
    
    protected SystemMediator mediator;
    protected String componentId;
    
    public Component(SystemMediator mediator, String componentId) {
        this.mediator = mediator;
        this.componentId = componentId;
        mediator.registerComponent(this);
    }
    
    public abstract void handleEvent(String event, Object data);
    public abstract String getComponentType();
    
    protected void sendEvent(String event, Object data) {
        mediator.notify(this, event, data);
    }
}

// 系统中介者实现
@Component
public class VOCSystemMediator implements SystemMediator {
    
    private final Map<String, Component> components = new ConcurrentHashMap<>();
    private final EventPublisher eventPublisher;
    private final ComponentRegistry registry;
    
    @Override
    public void notify(Component sender, String event, Object data) {
        MediatorEvent mediatorEvent = new MediatorEvent(
            sender.getComponentType(),
            sender.componentId,
            event,
            data,
            LocalDateTime.now()
        );
        
        // 记录事件
        eventPublisher.publish(mediatorEvent);
        
        // 路由事件到相关组件
        routeEvent(mediatorEvent);
    }
    
    private void routeEvent(MediatorEvent event) {
        switch (event.getEvent()) {
            case "DATA_RECEIVED":
                notifyDataProcessors(event);
                break;
            case "ANALYSIS_COMPLETED":
                notifyReportGenerators(event);
                break;
            case "CONFIG_CHANGED":
                notifyAllComponents(event);
                break;
            case "ALERT_TRIGGERED":
                notifyAlertHandlers(event);
                break;
            default:
                log.debug("未知事件类型: {}", event.getEvent());
        }
    }
    
    private void notifyDataProcessors(MediatorEvent event) {
        components.values().stream()
            .filter(component -> "DATA_PROCESSOR".equals(component.getComponentType()))
            .forEach(component -> component.handleEvent(event.getEvent(), event.getData()));
    }
    
    private void notifyReportGenerators(MediatorEvent event) {
        components.values().stream()
            .filter(component -> "REPORT_GENERATOR".equals(component.getComponentType()))
            .forEach(component -> component.handleEvent(event.getEvent(), event.getData()));
    }
    
    private void notifyAllComponents(MediatorEvent event) {
        components.values().forEach(component -> 
            component.handleEvent(event.getEvent(), event.getData())
        );
    }
    
    @Override
    public void registerComponent(Component component) {
        components.put(component.componentId, component);
        log.info("组件已注册: {} ({})", component.componentId, component.getComponentType());
    }
    
    @Override
    public void unregisterComponent(Component component) {
        components.remove(component.componentId);
        log.info("组件已注销: {} ({})", component.componentId, component.getComponentType());
    }
}

// 数据接收组件
@org.springframework.stereotype.Component
public class DataIngestionComponent extends Component {
    
    private final DataReceiver dataReceiver;
    
    public DataIngestionComponent(SystemMediator mediator, DataReceiver dataReceiver) {
        super(mediator, "DATA_INGESTION_" + UUID.randomUUID().toString());
        this.dataReceiver = dataReceiver;
    }
    
    public void receiveData(RawDataBatch dataBatch) {
        try {
            // 接收数据
            ReceivedData data = dataReceiver.receive(dataBatch);
            
            // 通知其他组件
            sendEvent("DATA_RECEIVED", data);
            
        } catch (Exception e) {
            sendEvent("DATA_RECEPTION_FAILED", e.getMessage());
        }
    }
    
    @Override
    public void handleEvent(String event, Object data) {
        switch (event) {
            case "CONFIG_CHANGED":
                // 重新配置数据接收器
                ConfigChangeEvent configEvent = (ConfigChangeEvent) data;
                dataReceiver.updateConfig(configEvent.getNewConfig());
                break;
            default:
                log.debug("数据接收组件忽略事件: {}", event);
        }
    }
    
    @Override
    public String getComponentType() {
        return "DATA_INGESTION";
    }
}

// 数据处理组件
@org.springframework.stereotype.Component
public class DataProcessingComponent extends Component {
    
    private final DataProcessor processor;
    
    public DataProcessingComponent(SystemMediator mediator, DataProcessor processor) {
        super(mediator, "DATA_PROCESSING_" + UUID.randomUUID().toString());
        this.processor = processor;
    }
    
    @Override
    public void handleEvent(String event, Object data) {
        switch (event) {
            case "DATA_RECEIVED":
                processReceivedData((ReceivedData) data);
                break;
            case "CONFIG_CHANGED":
                updateProcessingConfig((ConfigChangeEvent) data);
                break;
            default:
                log.debug("数据处理组件忽略事件: {}", event);
        }
    }
    
    private void processReceivedData(ReceivedData data) {
        try {
            ProcessingResult result = processor.process(data);
            
            if (result.isSuccess()) {
                sendEvent("PROCESSING_COMPLETED", result);
            } else {
                sendEvent("PROCESSING_FAILED", result.getError());
            }
            
        } catch (Exception e) {
            sendEvent("PROCESSING_FAILED", e.getMessage());
        }
    }
    
    @Override
    public String getComponentType() {
        return "DATA_PROCESSOR";
    }
}

// 报表生成组件
@org.springframework.stereotype.Component
public class ReportGenerationComponent extends Component {
    
    private final ReportGenerator reportGenerator;
    
    public ReportGenerationComponent(SystemMediator mediator, ReportGenerator reportGenerator) {
        super(mediator, "REPORT_GENERATION_" + UUID.randomUUID().toString());
        this.reportGenerator = reportGenerator;
    }
    
    @Override
    public void handleEvent(String event, Object data) {
        switch (event) {
            case "ANALYSIS_COMPLETED":
                generateReportsFromAnalysis((AnalysisResult) data);
                break;
            case "REPORT_REQUEST":
                handleReportRequest((ReportRequest) data);
                break;
            default:
                log.debug("报表生成组件忽略事件: {}", event);
        }
    }
    
    private void generateReportsFromAnalysis(AnalysisResult analysis) {
        try {
            ReportResult result = reportGenerator.generateFromAnalysis(analysis);
            sendEvent("REPORT_GENERATED", result);
        } catch (Exception e) {
            sendEvent("REPORT_GENERATION_FAILED", e.getMessage());
        }
    }
    
    @Override
    public String getComponentType() {
        return "REPORT_GENERATOR";
    }
}
```

**设计优势**：
- 减少组件间的直接依赖
- 集中管理组件间的通信逻辑
- 易于扩展新的组件
- 提高系统的可维护性

---

## 📚 总结

本文档详细介绍了通用VOC报表系统中应用的各种行为型设计模式：

1. **观察者模式**：用于事件驱动的数据处理通知
2. **策略模式**：支持多行业的不同分析策略
3. **责任链模式**：构建灵活的数据处理管道
4. **命令模式**：封装操作请求，支持撤销和记录
5. **状态模式**：管理数据处理过程中的状态转换
6. **模板方法模式**：标准化报表生成流程
7. **迭代器模式**：优化大数据集的遍历处理
8. **中介者模式**：统一管理组件间的通信

这些设计模式的应用使系统具备了：
- **高扩展性**：易于添加新的功能和组件
- **低耦合性**：组件间依赖关系清晰
- **高复用性**：通用逻辑可重复使用
- **易维护性**：代码结构清晰，职责分明

通过合理应用这些行为型设计模式，通用VOC系统能够有效地管理复杂的业务逻辑，提供稳定可靠的服务。

---

## 📝 维护信息

- **文档作者**：系统架构师
- **审核人员**：技术负责人、高级工程师
- **更新周期**：设计变更时及时更新
- **相关文档**：[创建型模式应用.md](./创建型模式应用.md)、[结构型模式应用.md](./结构型模式应用.md) 