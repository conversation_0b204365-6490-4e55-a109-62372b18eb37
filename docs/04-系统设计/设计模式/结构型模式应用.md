# 结构型设计模式应用

**文档版本**: v1.0.0  
**创建日期**: 2024年1月  
**最后更新**: 2024年1月  
**文档状态**: 设计阶段

---

## 📋 文档概述

本文档描述了通用VOC报表系统中应用的结构型设计模式，包括适配器模式、装饰器模式、代理模式、组合模式、外观模式等，详细说明了每种模式的应用场景、实现方式和设计考虑。

---

## 🔌 适配器模式（Adapter Pattern）

### 1.1 数据源适配器

**应用场景**：将不同格式的数据源统一适配为标准接口

**实现代码**：
```java
// 标准数据接口
public interface DataSource {
    List<DataRecord> readData(ReadConfig config);
    void validateData(List<DataRecord> data);
    DataMetadata getMetadata();
}

// CSV文件适配器
@Component
public class CsvDataSourceAdapter implements DataSource {
    
    private final CsvReader csvReader;
    
    @Override
    public List<DataRecord> readData(ReadConfig config) {
        CsvConfiguration csvConfig = adaptToCsvConfig(config);
        List<String[]> rawData = csvReader.readAll(csvConfig);
        return rawData.stream()
            .map(this::convertToDataRecord)
            .collect(Collectors.toList());
    }
    
    @Override
    public void validateData(List<DataRecord> data) {
        CsvDataValidator validator = new CsvDataValidator();
        validator.validateStructure(data);
        validator.validateContent(data);
    }
    
    private DataRecord convertToDataRecord(String[] csvRow) {
        return DataRecord.builder()
            .id(csvRow[0])
            .originalText(csvRow[1])
            .feedbackTime(parseDateTime(csvRow[2]))
            .customerInfo(parseCustomerInfo(csvRow))
            .build();
    }
}

// API数据源适配器
@Component
public class ApiDataSourceAdapter implements DataSource {
    
    private final RestTemplate restTemplate;
    
    @Override
    public List<DataRecord> readData(ReadConfig config) {
        ApiConfiguration apiConfig = adaptToApiConfig(config);
        ResponseEntity<ApiResponse> response = restTemplate.exchange(
            apiConfig.getUrl(),
            HttpMethod.GET,
            createHttpEntity(apiConfig),
            ApiResponse.class
        );
        
        return response.getBody().getData().stream()
            .map(this::convertToDataRecord)
            .collect(Collectors.toList());
    }
    
    private DataRecord convertToDataRecord(ApiDataItem item) {
        return DataRecord.builder()
            .id(item.getId())
            .originalText(item.getContent())
            .feedbackTime(item.getTimestamp())
            .channel(item.getChannel())
            .build();
    }
}
```

### 1.2 AI模型适配器

**应用场景**：适配不同的AI服务提供商接口

**实现代码**：
```java
// AI模型统一接口
public interface AIModelAdapter {
    AnalysisResult analyze(String text, AnalysisType type);
    boolean isAvailable();
    ModelCapabilities getCapabilities();
}

// OpenAI适配器
@Component
public class OpenAIAdapter implements AIModelAdapter {
    
    @Override
    public AnalysisResult analyze(String text, AnalysisType type) {
        OpenAIRequest request = buildOpenAIRequest(text, type);
        OpenAIResponse response = openAIClient.complete(request);
        return convertToAnalysisResult(response, type);
    }
    
    private OpenAIRequest buildOpenAIRequest(String text, AnalysisType type) {
        return OpenAIRequest.builder()
            .model("gpt-3.5-turbo")
            .messages(Arrays.asList(
                Message.system(getSystemPrompt(type)),
                Message.user(text)
            ))
            .temperature(0.1)
            .maxTokens(1000)
            .build();
    }
}

// Claude适配器
@Component
public class ClaudeAdapter implements AIModelAdapter {
    
    @Override
    public AnalysisResult analyze(String text, AnalysisType type) {
        ClaudeRequest request = buildClaudeRequest(text, type);
        ClaudeResponse response = claudeClient.complete(request);
        return convertToAnalysisResult(response, type);
    }
    
    private ClaudeRequest buildClaudeRequest(String text, AnalysisType type) {
        return ClaudeRequest.builder()
            .model("claude-3-sonnet")
            .maxTokens(1000)
            .messages(Arrays.asList(
                ClaudeMessage.builder()
                    .role("user")
                    .content(buildPrompt(text, type))
                    .build()
            ))
            .build();
    }
}
```

---

## 🎨 装饰器模式（Decorator Pattern）

### 2.1 数据处理装饰器

**应用场景**：为数据处理链添加各种增强功能

**实现代码**：
```java
// 数据处理器接口
public interface DataProcessor {
    ProcessedData process(RawData data);
}

// 基础数据处理器
public class BaseDataProcessor implements DataProcessor {
    
    @Override
    public ProcessedData process(RawData data) {
        return ProcessedData.builder()
            .id(data.getId())
            .cleanedText(basicClean(data.getOriginalText()))
            .processedAt(Instant.now())
            .build();
    }
    
    private String basicClean(String text) {
        return text.trim().replaceAll("\\s+", " ");
    }
}

// 抽象装饰器
public abstract class DataProcessorDecorator implements DataProcessor {
    
    protected final DataProcessor wrapped;
    
    public DataProcessorDecorator(DataProcessor processor) {
        this.wrapped = processor;
    }
    
    @Override
    public ProcessedData process(RawData data) {
        return wrapped.process(data);
    }
}

// 文本规范化装饰器
@Component
public class TextNormalizationDecorator extends DataProcessorDecorator {
    
    public TextNormalizationDecorator(DataProcessor processor) {
        super(processor);
    }
    
    @Override
    public ProcessedData process(RawData data) {
        ProcessedData baseResult = super.process(data);
        
        String normalizedText = normalizeText(baseResult.getCleanedText());
        
        return baseResult.toBuilder()
            .cleanedText(normalizedText)
            .processingSteps(addStep(baseResult.getProcessingSteps(), "text_normalization"))
            .build();
    }
    
    private String normalizeText(String text) {
        return text.toLowerCase()
            .replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9\\s]", "")
            .replaceAll("\\s+", " ")
            .trim();
    }
}

// 敏感信息脱敏装饰器
@Component
public class SensitiveDataMaskingDecorator extends DataProcessorDecorator {
    
    private final Set<Pattern> sensitivePatterns;
    
    public SensitiveDataMaskingDecorator(DataProcessor processor) {
        super(processor);
        this.sensitivePatterns = initializeSensitivePatterns();
    }
    
    @Override
    public ProcessedData process(RawData data) {
        ProcessedData baseResult = super.process(data);
        
        String maskedText = maskSensitiveData(baseResult.getCleanedText());
        
        return baseResult.toBuilder()
            .cleanedText(maskedText)
            .processingSteps(addStep(baseResult.getProcessingSteps(), "sensitive_masking"))
            .hasSensitiveData(containsSensitiveData(baseResult.getCleanedText()))
            .build();
    }
    
    private String maskSensitiveData(String text) {
        String result = text;
        for (Pattern pattern : sensitivePatterns) {
            result = pattern.matcher(result).replaceAll("***");
        }
        return result;
    }
    
    private Set<Pattern> initializeSensitivePatterns() {
        return Set.of(
            Pattern.compile("\\d{11}"), // 手机号
            Pattern.compile("\\d{15,18}"), // 身份证号
            Pattern.compile("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}") // 邮箱
        );
    }
}

// 数据质量检查装饰器
@Component
public class DataQualityCheckDecorator extends DataProcessorDecorator {
    
    public DataQualityCheckDecorator(DataProcessor processor) {
        super(processor);
    }
    
    @Override
    public ProcessedData process(RawData data) {
        ProcessedData baseResult = super.process(data);
        
        QualityScore qualityScore = calculateQualityScore(baseResult);
        
        return baseResult.toBuilder()
            .qualityScore(qualityScore)
            .processingSteps(addStep(baseResult.getProcessingSteps(), "quality_check"))
            .build();
    }
    
    private QualityScore calculateQualityScore(ProcessedData data) {
        double completeness = calculateCompleteness(data);
        double accuracy = calculateAccuracy(data);
        double consistency = calculateConsistency(data);
        
        return QualityScore.builder()
            .overall((completeness + accuracy + consistency) / 3.0)
            .completeness(completeness)
            .accuracy(accuracy)
            .consistency(consistency)
            .build();
    }
}
```

### 2.2 缓存装饰器

**应用场景**：为各种服务添加缓存功能

**实现代码**：
```java
// 配置服务缓存装饰器
@Component
public class CachedConfigurationService extends DataProcessorDecorator {
    
    private final ConfigurationService configService;
    private final RedisTemplate<String, String> redisTemplate;
    private final Duration cacheTTL = Duration.ofHours(1);
    
    public CachedConfigurationService(ConfigurationService configService,
                                    RedisTemplate<String, String> redisTemplate) {
        this.configService = configService;
        this.redisTemplate = redisTemplate;
    }
    
    public String getConfiguration(String key, ConfigurationContext context) {
        String cacheKey = buildCacheKey(key, context);
        
        // 尝试从缓存获取
        String cachedValue = redisTemplate.opsForValue().get(cacheKey);
        if (cachedValue != null) {
            return cachedValue;
        }
        
        // 从原始服务获取
        String value = configService.getConfiguration(key, context);
        
        // 缓存结果
        if (value != null) {
            redisTemplate.opsForValue().set(cacheKey, value, cacheTTL);
        }
        
        return value;
    }
    
    private String buildCacheKey(String key, ConfigurationContext context) {
        return String.format("config:%s:%s:%s:%s", 
            key, 
            context.getEnvironment(),
            context.getIndustryCode(),
            context.getTenantId()
        );
    }
}
```

---

## 🔍 代理模式（Proxy Pattern）

### 3.1 AI服务代理

**应用场景**：控制对AI服务的访问，实现限流、监控、重试等功能

**实现代码**：
```java
// AI分析服务代理
@Component
public class AIAnalysisServiceProxy implements AnalysisService {
    
    private final AnalysisService targetService;
    private final RateLimiter rateLimiter;
    private final CircuitBreaker circuitBreaker;
    private final MeterRegistry meterRegistry;
    
    public AIAnalysisServiceProxy(AnalysisService targetService,
                                 RateLimiterRegistry rateLimiterRegistry,
                                 CircuitBreakerRegistry circuitBreakerRegistry,
                                 MeterRegistry meterRegistry) {
        this.targetService = targetService;
        this.rateLimiter = rateLimiterRegistry.rateLimiter("ai-analysis");
        this.circuitBreaker = circuitBreakerRegistry.circuitBreaker("ai-analysis");
        this.meterRegistry = meterRegistry;
    }
    
    @Override
    public AnalysisResult analyze(String text, AnalysisType type) {
        return Timer.Sample.start(meterRegistry)
            .stop(meterRegistry.timer("ai.analysis.duration"))
            .recordCallable(() -> {
                // 限流检查
                RateLimiter.waitForPermission(rateLimiter);
                
                // 熔断保护
                return circuitBreaker.executeSupplier(() -> {
                    try {
                        AnalysisResult result = targetService.analyze(text, type);
                        recordSuccess(type);
                        return result;
                    } catch (Exception e) {
                        recordFailure(type, e);
                        throw e;
                    }
                });
            });
    }
    
    private void recordSuccess(AnalysisType type) {
        meterRegistry.counter("ai.analysis.success", "type", type.name()).increment();
    }
    
    private void recordFailure(AnalysisType type, Exception e) {
        meterRegistry.counter("ai.analysis.failure", 
            "type", type.name(),
            "error", e.getClass().getSimpleName()).increment();
    }
}
```

### 3.2 数据库访问代理

**应用场景**：实现数据库访问的权限控制、审计和读写分离

**实现代码**：
```java
// 数据访问代理
@Component
public class SecureDataAccessProxy implements DataAccessService {
    
    private final DataAccessService readOnlyService;
    private final DataAccessService readWriteService;
    private final PermissionService permissionService;
    private final AuditService auditService;
    
    @Override
    public List<VOCRecord> findRecords(QueryCondition condition) {
        // 权限检查
        String currentUser = getCurrentUser();
        if (!permissionService.hasReadPermission(currentUser, condition)) {
            throw new AccessDeniedException("用户无查询权限: " + currentUser);
        }
        
        // 数据脱敏
        List<VOCRecord> records = readOnlyService.findRecords(condition);
        List<VOCRecord> maskedRecords = applyDataMasking(records, currentUser);
        
        // 审计日志
        auditService.logDataAccess(currentUser, "READ", condition, records.size());
        
        return maskedRecords;
    }
    
    @Override
    public void saveRecord(VOCRecord record) {
        String currentUser = getCurrentUser();
        
        // 权限检查
        if (!permissionService.hasWritePermission(currentUser, record)) {
            throw new AccessDeniedException("用户无写入权限: " + currentUser);
        }
        
        // 数据验证
        validateRecord(record);
        
        // 保存记录
        readWriteService.saveRecord(record);
        
        // 审计日志
        auditService.logDataAccess(currentUser, "WRITE", record);
    }
    
    private List<VOCRecord> applyDataMasking(List<VOCRecord> records, String user) {
        if (permissionService.hasFullAccess(user)) {
            return records;
        }
        
        return records.stream()
            .map(record -> record.toBuilder()
                .customerPhone(maskPhone(record.getCustomerPhone()))
                .customerEmail(maskEmail(record.getCustomerEmail()))
                .build())
            .collect(Collectors.toList());
    }
}
```

---

## 📦 组合模式（Composite Pattern）

### 4.1 报表组件组合

**应用场景**：构建复杂的报表结构，支持报表组件的嵌套和组合

**实现代码**：
```java
// 报表组件抽象类
public abstract class ReportComponent {
    
    protected String id;
    protected String name;
    protected Map<String, Object> properties;
    
    public abstract void render(RenderContext context);
    public abstract void addChild(ReportComponent child);
    public abstract void removeChild(ReportComponent child);
    public abstract List<ReportComponent> getChildren();
    public abstract boolean isComposite();
}

// 叶子组件 - 图表
public class ChartComponent extends ReportComponent {
    
    private final ChartType chartType;
    private final DataQuery dataQuery;
    
    public ChartComponent(String id, String name, ChartType chartType, DataQuery dataQuery) {
        this.id = id;
        this.name = name;
        this.chartType = chartType;
        this.dataQuery = dataQuery;
    }
    
    @Override
    public void render(RenderContext context) {
        // 执行数据查询
        List<DataPoint> data = context.getDataService().query(dataQuery);
        
        // 生成图表
        Chart chart = ChartFactory.createChart(chartType, data);
        
        // 渲染图表
        context.getRenderer().renderChart(chart, properties);
    }
    
    @Override
    public void addChild(ReportComponent child) {
        throw new UnsupportedOperationException("叶子组件不支持添加子组件");
    }
    
    @Override
    public void removeChild(ReportComponent child) {
        throw new UnsupportedOperationException("叶子组件不支持移除子组件");
    }
    
    @Override
    public List<ReportComponent> getChildren() {
        return Collections.emptyList();
    }
    
    @Override
    public boolean isComposite() {
        return false;
    }
}

// 组合组件 - 报表页面
public class ReportPageComponent extends ReportComponent {
    
    private final List<ReportComponent> children = new ArrayList<>();
    private final LayoutManager layoutManager;
    
    public ReportPageComponent(String id, String name, LayoutManager layoutManager) {
        this.id = id;
        this.name = name;
        this.layoutManager = layoutManager;
    }
    
    @Override
    public void render(RenderContext context) {
        // 渲染页面容器
        context.getRenderer().startPage(id, name, properties);
        
        // 计算布局
        Layout layout = layoutManager.calculateLayout(children, context.getPageSize());
        
        // 渲染子组件
        for (int i = 0; i < children.size(); i++) {
            ReportComponent child = children.get(i);
            Position position = layout.getPosition(i);
            
            context.getRenderer().setPosition(position);
            child.render(context);
        }
        
        // 结束页面渲染
        context.getRenderer().endPage();
    }
    
    @Override
    public void addChild(ReportComponent child) {
        children.add(child);
    }
    
    @Override
    public void removeChild(ReportComponent child) {
        children.remove(child);
    }
    
    @Override
    public List<ReportComponent> getChildren() {
        return new ArrayList<>(children);
    }
    
    @Override
    public boolean isComposite() {
        return true;
    }
}

// 报表构建器
@Service
public class ReportBuilder {
    
    public ReportComponent buildDashboard(String industryCode) {
        ReportPageComponent dashboard = new ReportPageComponent(
            "dashboard", 
            "VOC仪表板", 
            new GridLayoutManager(2, 2)
        );
        
        // 添加情感分析图表
        dashboard.addChild(new ChartComponent(
            "sentiment-pie",
            "情感分布",
            ChartType.PIE,
            buildSentimentQuery(industryCode)
        ));
        
        // 添加趋势分析图表
        dashboard.addChild(new ChartComponent(
            "trend-line",
            "趋势分析", 
            ChartType.LINE,
            buildTrendQuery(industryCode)
        ));
        
        // 添加子报表
        ReportPageComponent detailPage = buildDetailPage(industryCode);
        dashboard.addChild(detailPage);
        
        return dashboard;
    }
}
```

---

## 🎭 外观模式（Facade Pattern）

### 5.1 VOC分析外观

**应用场景**：为复杂的VOC分析流程提供简化的统一接口

**实现代码**：
```java
// VOC分析外观类
@Service
public class VOCAnalysisFacade {
    
    private final DataIngestionService dataIngestionService;
    private final AIAnalysisService aiAnalysisService;
    private final BusinessRulesEngine businessRulesEngine;
    private final DataStorageService dataStorageService;
    private final NotificationService notificationService;
    
    public VOCAnalysisFacade(DataIngestionService dataIngestionService,
                           AIAnalysisService aiAnalysisService,
                           BusinessRulesEngine businessRulesEngine,
                           DataStorageService dataStorageService,
                           NotificationService notificationService) {
        this.dataIngestionService = dataIngestionService;
        this.aiAnalysisService = aiAnalysisService;
        this.businessRulesEngine = businessRulesEngine;
        this.dataStorageService = dataStorageService;
        this.notificationService = notificationService;
    }
    
    /**
     * 简化的VOC数据处理接口
     */
    public ProcessingResult processVOCData(VOCDataRequest request) {
        try {
            // 1. 数据接入和验证
            List<RawDataRecord> rawData = dataIngestionService.ingestData(
                request.getDataSource(),
                request.getIndustryCode(),
                request.getTenantId()
            );
            
            // 2. AI智能分析
            List<AnalysisResult> analysisResults = new ArrayList<>();
            for (RawDataRecord record : rawData) {
                AnalysisResult result = aiAnalysisService.analyzeText(
                    record.getText(),
                    request.getAnalysisConfig()
                );
                analysisResults.add(result);
            }
            
            // 3. 业务规则处理
            List<ProcessedRecord> processedRecords = businessRulesEngine.applyRules(
                rawData,
                analysisResults,
                request.getBusinessRules()
            );
            
            // 4. 数据存储
            List<VOCRecord> savedRecords = dataStorageService.saveRecords(
                processedRecords,
                request.getStorageConfig()
            );
            
            // 5. 触发通知
            triggerNotificationsIfNeeded(savedRecords, request);
            
            return ProcessingResult.success(savedRecords.size());
            
        } catch (Exception e) {
            log.error("VOC数据处理失败", e);
            return ProcessingResult.failure(e.getMessage());
        }
    }
    
    /**
     * 简化的报表生成接口
     */
    public ReportResult generateReport(ReportRequest request) {
        try {
            // 1. 数据查询
            List<VOCRecord> data = dataStorageService.queryRecords(
                request.getQueryCondition()
            );
            
            // 2. 数据分析
            AnalyticsResult analytics = performAnalytics(data, request.getAnalyticsType());
            
            // 3. 报表生成
            Report report = generateReportFromAnalytics(analytics, request.getReportType());
            
            // 4. 报表渲染
            RenderedReport renderedReport = renderReport(report, request.getOutputFormat());
            
            return ReportResult.success(renderedReport);
            
        } catch (Exception e) {
            log.error("报表生成失败", e);
            return ReportResult.failure(e.getMessage());
        }
    }
    
    private void triggerNotificationsIfNeeded(List<VOCRecord> records, VOCDataRequest request) {
        // 检查是否有高优先级问题
        List<VOCRecord> highPriorityRecords = records.stream()
            .filter(record -> record.getPriority() == Priority.HIGH)
            .collect(Collectors.toList());
            
        if (!highPriorityRecords.isEmpty()) {
            NotificationRequest notificationRequest = NotificationRequest.builder()
                .type(NotificationType.HIGH_PRIORITY_ALERT)
                .recipients(request.getAlertRecipients())
                .data(highPriorityRecords)
                .build();
                
            notificationService.sendNotification(notificationRequest);
        }
    }
}
```

### 5.2 系统配置外观

**应用场景**：简化复杂的系统配置管理操作

**实现代码**：
```java
// 系统配置外观
@Service
public class SystemConfigurationFacade {
    
    private final ConfigurationService configurationService;
    private final TemplateService templateService;
    private final ValidationService validationService;
    private final ApprovalService approvalService;
    private final DeploymentService deploymentService;
    
    /**
     * 一键部署行业配置
     */
    public DeploymentResult deployIndustryConfiguration(IndustryDeploymentRequest request) {
        try {
            // 1. 加载配置模板
            ConfigurationTemplate template = templateService.getTemplate(
                request.getTemplateId()
            );
            
            // 2. 定制化配置
            IndustryConfiguration config = customizeConfiguration(
                template, 
                request.getCustomizations()
            );
            
            // 3. 配置验证
            ValidationResult validation = validationService.validateConfiguration(config);
            if (!validation.isValid()) {
                return DeploymentResult.failure("配置验证失败: " + validation.getErrors());
            }
            
            // 4. 提交审批
            if (request.isRequireApproval()) {
                ApprovalResult approval = approvalService.submitForApproval(config);
                if (!approval.isApproved()) {
                    return DeploymentResult.pending("等待审批: " + approval.getApprovalId());
                }
            }
            
            // 5. 部署配置
            DeploymentResult deployment = deploymentService.deployConfiguration(
                config, 
                request.getEnvironment()
            );
            
            return deployment;
            
        } catch (Exception e) {
            log.error("行业配置部署失败", e);
            return DeploymentResult.failure(e.getMessage());
        }
    }
    
    /**
     * 批量配置更新
     */
    public BatchUpdateResult batchUpdateConfigurations(BatchUpdateRequest request) {
        List<UpdateResult> results = new ArrayList<>();
        
        for (ConfigurationUpdate update : request.getUpdates()) {
            try {
                UpdateResult result = updateSingleConfiguration(update);
                results.add(result);
            } catch (Exception e) {
                results.add(UpdateResult.failure(update.getKey(), e.getMessage()));
            }
        }
        
        return BatchUpdateResult.builder()
            .totalCount(request.getUpdates().size())
            .successCount((int) results.stream().filter(UpdateResult::isSuccess).count())
            .results(results)
            .build();
    }
}
```

---

## 📋 总结

### 结构型模式应用效果

1. **适配器模式**：统一了不同数据源和AI服务的接口，提高了系统的兼容性和扩展性
2. **装饰器模式**：为数据处理和服务调用添加了灵活的增强功能，支持功能的动态组合
3. **代理模式**：实现了访问控制、性能监控、安全审计等横切关注点
4. **组合模式**：构建了灵活的报表组件体系，支持复杂报表的动态构建
5. **外观模式**：简化了复杂子系统的使用，提供了便捷的统一接口

### 设计考虑

- **性能优化**：代理模式实现了缓存、限流等性能优化功能
- **安全控制**：代理模式提供了权限控制和数据脱敏功能
- **灵活扩展**：装饰器模式支持功能的动态组合和扩展
- **统一接口**：适配器模式统一了异构系统的接口
- **简化使用**：外观模式隐藏了子系统的复杂性

---

**文档维护**: 系统架构师、开发团队  
**审核**: 技术负责人、代码审查员  
**下次更新**: 设计模式应用变更时及时更新 