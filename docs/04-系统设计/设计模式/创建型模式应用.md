# 创建型设计模式应用

**文档版本**: v1.0.0  
**创建日期**: 2024年1月  
**最后更新**: 2024年1月  
**文档状态**: 设计阶段

---

## 📋 文档概述

本文档描述了通用VOC报表系统中应用的创建型设计模式，包括工厂模式、建造者模式、单例模式、原型模式等，详细说明了每种模式的应用场景、实现方式和设计考虑。

---

## 🏭 工厂模式（Factory Pattern）

### 1.1 简单工厂模式 - 数据适配器工厂

**应用场景**：根据不同的数据源类型创建相应的数据适配器

**实现代码**：
```java
// 数据适配器接口
public interface DataAdapter {
    boolean supports(String dataType);
    DataStream<RawDataRecord> readData(DataSourceConfig config);
    void validateConfig(DataSourceConfig config);
}

// 数据适配器工厂
@Component
public class DataAdapterFactory {
    
    private final Map<String, DataAdapter> adapters;
    
    public DataAdapterFactory(List<DataAdapter> adapterList) {
        this.adapters = adapterList.stream()
            .collect(Collectors.toMap(
                adapter -> adapter.getClass().getSimpleName().replace("DataAdapter", "").toLowerCase(),
                adapter -> adapter
            ));
    }
    
    public DataAdapter createAdapter(String dataType) {
        DataAdapter adapter = adapters.get(dataType.toLowerCase());
        if (adapter == null) {
            throw new UnsupportedDataTypeException("不支持的数据类型: " + dataType);
        }
        return adapter;
    }
    
    public List<String> getSupportedDataTypes() {
        return new ArrayList<>(adapters.keySet());
    }
}

// 具体适配器实现
@Component
public class CsvDataAdapter implements DataAdapter {
    
    @Override
    public boolean supports(String dataType) {
        return "csv".equalsIgnoreCase(dataType);
    }
    
    @Override
    public DataStream<RawDataRecord> readData(DataSourceConfig config) {
        CsvParserSettings settings = buildCsvSettings(config);
        return DataStream.fromSource(new CsvFileSource(config.getFilePath(), settings))
            .map(this::convertToRawDataRecord);
    }
    
    @Override
    public void validateConfig(DataSourceConfig config) {
        if (config.getFilePath() == null || !config.getFilePath().endsWith(".csv")) {
            throw new InvalidConfigException("CSV适配器需要有效的CSV文件路径");
        }
    }
}
```

### 1.2 抽象工厂模式 - 分析引擎工厂

**应用场景**：为不同行业创建特定的分析引擎组合

**实现代码**：
```java
// 抽象分析引擎工厂
public abstract class AnalysisEngineAbstractFactory {
    
    public abstract SentimentAnalysisEngine createSentimentEngine();
    public abstract IntentRecognitionEngine createIntentEngine();
    public abstract TopicClassificationEngine createTopicEngine();
    public abstract BusinessRulesEngine createBusinessRulesEngine();
    
    // 创建完整的分析引擎套件
    public AnalysisEngineSuite createAnalysisEngineSuite() {
        return AnalysisEngineSuite.builder()
            .sentimentEngine(createSentimentEngine())
            .intentEngine(createIntentEngine())
            .topicEngine(createTopicEngine())
            .businessRulesEngine(createBusinessRulesEngine())
            .build();
    }
}

// 汽车行业分析引擎工厂
@Component
public class AutomotiveAnalysisEngineFactory extends AnalysisEngineAbstractFactory {
    
    @Override
    public SentimentAnalysisEngine createSentimentEngine() {
        return SentimentAnalysisEngine.builder()
            .model("automotive-sentiment-v1")
            .threshold(0.7)
            .industryWeights(Map.of(
                "safety", 2.0,
                "quality", 1.5,
                "service", 1.2
            ))
            .build();
    }
    
    @Override
    public IntentRecognitionEngine createIntentEngine() {
        return IntentRecognitionEngine.builder()
            .model("automotive-intent-v1")
            .threshold(0.8)
            .supportedIntents(Arrays.asList(
                "complaint", "consultation", "warranty_claim", "recall_inquiry"
            ))
            .build();
    }
    
    @Override
    public TopicClassificationEngine createTopicEngine() {
        return TopicClassificationEngine.builder()
            .model("automotive-topic-v1")
            .threshold(0.75)
            .topicHierarchy(buildAutomotiveTopicHierarchy())
            .build();
    }
    
    @Override
    public BusinessRulesEngine createBusinessRulesEngine() {
        return BusinessRulesEngine.builder()
            .ruleSet("automotive-rules-v1")
            .priorityRules(buildAutomotivePriorityRules())
            .escalationRules(buildAutomotiveEscalationRules())
            .build();
    }
}

// 工厂管理器
@Service
public class AnalysisEngineFactoryManager {
    
    private final Map<String, AnalysisEngineAbstractFactory> factories;
    
    public AnalysisEngineFactoryManager(List<AnalysisEngineAbstractFactory> factoryList) {
        this.factories = factoryList.stream()
            .collect(Collectors.toMap(
                factory -> extractIndustryCode(factory),
                factory -> factory
            ));
    }
    
    public AnalysisEngineSuite createAnalysisEngineSuite(String industryCode) {
        AnalysisEngineAbstractFactory factory = factories.get(industryCode);
        if (factory == null) {
            throw new UnsupportedIndustryException("不支持的行业: " + industryCode);
        }
        return factory.createAnalysisEngineSuite();
    }
}
```

### 1.3 方法工厂模式 - 报表生成器工厂

**应用场景**：根据报表类型动态创建相应的报表生成器

**实现代码**：
```java
// 报表生成器基类
public abstract class ReportGenerator {
    
    protected abstract ReportTemplate loadTemplate();
    protected abstract List<DataQuery> buildQueries();
    protected abstract List<ChartConfig> configureCharts();
    
    // 模板方法
    public final ReportResult generateReport(ReportRequest request) {
        ReportTemplate template = loadTemplate();
        List<DataQuery> queries = buildQueries();
        List<ChartConfig> charts = configureCharts();
        
        return executeReportGeneration(template, queries, charts, request);
    }
    
    private ReportResult executeReportGeneration(ReportTemplate template, 
                                               List<DataQuery> queries,
                                               List<ChartConfig> charts,
                                               ReportRequest request) {
        // 统一的报表生成逻辑
        return reportExecutor.execute(template, queries, charts, request);
    }
}

// 情感分析报表生成器
@Component
public class SentimentAnalysisReportGenerator extends ReportGenerator {
    
    @Override
    protected ReportTemplate loadTemplate() {
        return templateRepository.findByName("sentiment_analysis_template");
    }
    
    @Override
    protected List<DataQuery> buildQueries() {
        return Arrays.asList(
            DataQuery.builder()
                .name("sentiment_distribution")
                .sql("SELECT sentiment, COUNT(*) as count FROM dwd_voc_detail WHERE feedback_time >= ? GROUP BY sentiment")
                .build(),
            DataQuery.builder()
                .name("sentiment_trend")
                .sql("SELECT DATE(feedback_time) as date, sentiment, COUNT(*) as count FROM dwd_voc_detail WHERE feedback_time >= ? GROUP BY DATE(feedback_time), sentiment")
                .build()
        );
    }
    
    @Override
    protected List<ChartConfig> configureCharts() {
        return Arrays.asList(
            ChartConfig.pieChart("sentiment_pie", "sentiment_distribution", "sentiment", "count"),
            ChartConfig.lineChart("sentiment_trend", "sentiment_trend", "date", "count", "sentiment")
        );
    }
}

// 报表生成器工厂
@Component
public class ReportGeneratorFactory {
    
    private final Map<ReportType, ReportGenerator> generators;
    
    public ReportGeneratorFactory(List<ReportGenerator> generatorList) {
        this.generators = generatorList.stream()
            .collect(Collectors.toMap(
                this::extractReportType,
                generator -> generator
            ));
    }
    
    public ReportGenerator getGenerator(ReportType reportType) {
        ReportGenerator generator = generators.get(reportType);
        if (generator == null) {
            throw new UnsupportedReportTypeException("不支持的报表类型: " + reportType);
        }
        return generator;
    }
}
```

---

## 🏗️ 建造者模式（Builder Pattern）

### 2.1 复杂配置对象建造者

**应用场景**：构建复杂的行业配置对象

**实现代码**：
```java
// 行业配置类
public class IndustryConfiguration {
    
    private final String industryCode;
    private final String industryName;
    private final AnalysisConfig analysisConfig;
    private final ValidationConfig validationConfig;
    private final BusinessRulesConfig businessRulesConfig;
    private final ReportConfig reportConfig;
    private final Map<String, Object> customProperties;
    
    private IndustryConfiguration(Builder builder) {
        this.industryCode = builder.industryCode;
        this.industryName = builder.industryName;
        this.analysisConfig = builder.analysisConfig;
        this.validationConfig = builder.validationConfig;
        this.businessRulesConfig = builder.businessRulesConfig;
        this.reportConfig = builder.reportConfig;
        this.customProperties = builder.customProperties;
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static class Builder {
        private String industryCode;
        private String industryName;
        private AnalysisConfig analysisConfig;
        private ValidationConfig validationConfig;
        private BusinessRulesConfig businessRulesConfig;
        private ReportConfig reportConfig;
        private Map<String, Object> customProperties = new HashMap<>();
        
        public Builder industryCode(String industryCode) {
            this.industryCode = industryCode;
            return this;
        }
        
        public Builder industryName(String industryName) {
            this.industryName = industryName;
            return this;
        }
        
        public Builder analysisConfig(AnalysisConfig analysisConfig) {
            this.analysisConfig = analysisConfig;
            return this;
        }
        
        public Builder withSentimentAnalysis(double threshold, String model) {
            if (this.analysisConfig == null) {
                this.analysisConfig = new AnalysisConfig();
            }
            this.analysisConfig.setSentimentThreshold(threshold);
            this.analysisConfig.setSentimentModel(model);
            return this;
        }
        
        public Builder withIntentRecognition(double threshold, List<String> intents) {
            if (this.analysisConfig == null) {
                this.analysisConfig = new AnalysisConfig();
            }
            this.analysisConfig.setIntentThreshold(threshold);
            this.analysisConfig.setSupportedIntents(intents);
            return this;
        }
        
        public Builder validationRules(List<ValidationRule> rules) {
            if (this.validationConfig == null) {
                this.validationConfig = new ValidationConfig();
            }
            this.validationConfig.setRules(rules);
            return this;
        }
        
        public Builder businessRules(List<BusinessRule> rules) {
            if (this.businessRulesConfig == null) {
                this.businessRulesConfig = new BusinessRulesConfig();
            }
            this.businessRulesConfig.setRules(rules);
            return this;
        }
        
        public Builder defaultReports(List<String> reportTypes) {
            if (this.reportConfig == null) {
                this.reportConfig = new ReportConfig();
            }
            this.reportConfig.setDefaultReportTypes(reportTypes);
            return this;
        }
        
        public Builder customProperty(String key, Object value) {
            this.customProperties.put(key, value);
            return this;
        }
        
        public IndustryConfiguration build() {
            validate();
            return new IndustryConfiguration(this);
        }
        
        private void validate() {
            if (industryCode == null || industryCode.trim().isEmpty()) {
                throw new IllegalArgumentException("行业代码不能为空");
            }
            if (industryName == null || industryName.trim().isEmpty()) {
                throw new IllegalArgumentException("行业名称不能为空");
            }
            // 其他验证逻辑...
        }
    }
}

// 配置建造者服务
@Service
public class IndustryConfigurationBuilder {
    
    public IndustryConfiguration buildAutomotiveConfig() {
        return IndustryConfiguration.builder()
            .industryCode("automotive")
            .industryName("汽车行业")
            .withSentimentAnalysis(0.7, "automotive-sentiment-v1")
            .withIntentRecognition(0.8, Arrays.asList("complaint", "consultation", "warranty_claim"))
            .validationRules(buildAutomotiveValidationRules())
            .businessRules(buildAutomotiveBusinessRules())
            .defaultReports(Arrays.asList("sentiment_analysis", "quality_issues", "dealer_performance"))
            .customProperty("safety_priority_weight", 2.0)
            .customProperty("quality_focus_keywords", Arrays.asList("安全", "质量", "故障"))
            .build();
    }
    
    public IndustryConfiguration buildStarbucksConfig() {
        return IndustryConfiguration.builder()
            .industryCode("starbucks")
            .industryName("星巴克")
            .withSentimentAnalysis(0.7, "service-sentiment-v1")
            .withIntentRecognition(0.8, Arrays.asList("complaint", "compliment", "suggestion"))
            .validationRules(buildServiceValidationRules())
            .businessRules(buildServiceBusinessRules())
            .defaultReports(Arrays.asList("customer_satisfaction", "service_quality", "product_feedback"))
            .customProperty("service_priority", true)
            .customProperty("experience_keywords", Arrays.asList("服务", "体验", "环境"))
            .build();
    }
}
```

### 2.2 查询建造者模式

**应用场景**：构建复杂的数据查询条件

**实现代码**：
```java
// 查询建造者
public class VOCQueryBuilder {
    
    private final List<Condition> conditions = new ArrayList<>();
    private final List<String> selectFields = new ArrayList<>();
    private final List<OrderBy> orderByList = new ArrayList<>();
    private final List<GroupBy> groupByList = new ArrayList<>();
    private Integer limit;
    private Integer offset;
    private String industryCode;
    private String tenantId;
    
    public static VOCQueryBuilder newQuery() {
        return new VOCQueryBuilder();
    }
    
    public VOCQueryBuilder select(String... fields) {
        this.selectFields.addAll(Arrays.asList(fields));
        return this;
    }
    
    public VOCQueryBuilder forIndustry(String industryCode) {
        this.industryCode = industryCode;
        return this;
    }
    
    public VOCQueryBuilder forTenant(String tenantId) {
        this.tenantId = tenantId;
        return this;
    }
    
    public VOCQueryBuilder whereSentiment(String sentiment) {
        conditions.add(Condition.equals("sentiment", sentiment));
        return this;
    }
    
    public VOCQueryBuilder whereIntent(String... intents) {
        conditions.add(Condition.in("intent", Arrays.asList(intents)));
        return this;
    }
    
    public VOCQueryBuilder whereFeedbackTimeBetween(LocalDateTime start, LocalDateTime end) {
        conditions.add(Condition.between("feedback_time", start, end));
        return this;
    }
    
    public VOCQueryBuilder whereTextContains(String keyword) {
        conditions.add(Condition.contains("cleaned_text", keyword));
        return this;
    }
    
    public VOCQueryBuilder whereConfidenceGreaterThan(String field, double threshold) {
        conditions.add(Condition.greaterThan(field + "_confidence", threshold));
        return this;
    }
    
    public VOCQueryBuilder orderByFeedbackTime(SortDirection direction) {
        orderByList.add(new OrderBy("feedback_time", direction));
        return this;
    }
    
    public VOCQueryBuilder orderBySentimentConfidence(SortDirection direction) {
        orderByList.add(new OrderBy("sentiment_confidence", direction));
        return this;
    }
    
    public VOCQueryBuilder groupBySentiment() {
        groupByList.add(new GroupBy("sentiment"));
        return this;
    }
    
    public VOCQueryBuilder groupByDate() {
        groupByList.add(new GroupBy("DATE(feedback_time)"));
        return this;
    }
    
    public VOCQueryBuilder limit(int limit) {
        this.limit = limit;
        return this;
    }
    
    public VOCQueryBuilder offset(int offset) {
        this.offset = offset;
        return this;
    }
    
    public VOCQuery build() {
        // 添加默认条件
        if (industryCode != null) {
            conditions.add(Condition.equals("industry_code", industryCode));
        }
        if (tenantId != null) {
            conditions.add(Condition.equals("tenant_id", tenantId));
        }
        
        return VOCQuery.builder()
            .selectFields(selectFields.isEmpty() ? Arrays.asList("*") : selectFields)
            .conditions(conditions)
            .orderBy(orderByList)
            .groupBy(groupByList)
            .limit(limit)
            .offset(offset)
            .build();
    }
    
    // 预定义查询模板
    public static VOCQueryBuilder sentimentAnalysisQuery(String industryCode, String tenantId) {
        return newQuery()
            .forIndustry(industryCode)
            .forTenant(tenantId)
            .select("sentiment", "COUNT(*) as count", "AVG(sentiment_confidence) as avg_confidence")
            .groupBySentiment()
            .orderBy("count", SortDirection.DESC);
    }
    
    public static VOCQueryBuilder recentNegativeFeedbackQuery(String industryCode, String tenantId, int days) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        return newQuery()
            .forIndustry(industryCode)
            .forTenant(tenantId)
            .whereSentiment("negative")
            .whereFeedbackTimeBetween(startTime, LocalDateTime.now())
            .whereConfidenceGreaterThan("sentiment", 0.8)
            .orderByFeedbackTime(SortDirection.DESC);
    }
}

// 使用示例
@Service
public class VOCQueryService {
    
    public List<VOCRecord> getHighConfidenceNegativeFeedback(String industryCode, String tenantId) {
        VOCQuery query = VOCQueryBuilder.newQuery()
            .forIndustry(industryCode)
            .forTenant(tenantId)
            .whereSentiment("negative")
            .whereConfidenceGreaterThan("sentiment", 0.8)
            .whereFeedbackTimeBetween(LocalDateTime.now().minusDays(7), LocalDateTime.now())
            .orderByFeedbackTime(SortDirection.DESC)
            .limit(100)
            .build();
        
        return vocRepository.findByQuery(query);
    }
}
```

---

## 🔒 单例模式（Singleton Pattern）

### 3.1 配置管理器单例

**应用场景**：全局配置管理器，确保配置的一致性

**实现代码**：
```java
// 线程安全的配置管理器单例
public class ConfigurationManager {
    
    private static volatile ConfigurationManager instance;
    private final Map<String, String> configurations;
    private final ReadWriteLock lock;
    private final List<ConfigurationChangeListener> listeners;
    
    private ConfigurationManager() {
        this.configurations = new ConcurrentHashMap<>();
        this.lock = new ReentrantReadWriteLock();
        this.listeners = new CopyOnWriteArrayList<>();
        initializeConfigurations();
    }
    
    public static ConfigurationManager getInstance() {
        if (instance == null) {
            synchronized (ConfigurationManager.class) {
                if (instance == null) {
                    instance = new ConfigurationManager();
                }
            }
        }
        return instance;
    }
    
    public String getConfiguration(String key) {
        lock.readLock().lock();
        try {
            return configurations.get(key);
        } finally {
            lock.readLock().unlock();
        }
    }
    
    public void setConfiguration(String key, String value) {
        String oldValue;
        lock.writeLock().lock();
        try {
            oldValue = configurations.put(key, value);
        } finally {
            lock.writeLock().unlock();
        }
        
        // 通知监听器
        notifyConfigurationChanged(key, oldValue, value);
    }
    
    public void addConfigurationChangeListener(ConfigurationChangeListener listener) {
        listeners.add(listener);
    }
    
    private void notifyConfigurationChanged(String key, String oldValue, String newValue) {
        ConfigurationChangeEvent event = new ConfigurationChangeEvent(key, oldValue, newValue);
        listeners.forEach(listener -> listener.onConfigurationChanged(event));
    }
}

// Spring Bean单例管理
@Component
@Scope("singleton")
public class ApplicationContextHolder implements ApplicationContextAware {
    
    private static ApplicationContext applicationContext;
    
    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        applicationContext = context;
    }
    
    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }
    
    public static <T> T getBean(Class<T> clazz) {
        return applicationContext.getBean(clazz);
    }
    
    public static <T> T getBean(String name, Class<T> clazz) {
        return applicationContext.getBean(name, clazz);
    }
}
```

### 3.2 连接池管理器单例

**应用场景**：数据库连接池的全局管理

**实现代码**：
```java
// 连接池管理器
public class ConnectionPoolManager {
    
    private static volatile ConnectionPoolManager instance;
    private final Map<String, HikariDataSource> dataSources;
    private final Object lock = new Object();
    
    private ConnectionPoolManager() {
        this.dataSources = new ConcurrentHashMap<>();
    }
    
    public static ConnectionPoolManager getInstance() {
        if (instance == null) {
            synchronized (ConnectionPoolManager.class) {
                if (instance == null) {
                    instance = new ConnectionPoolManager();
                }
            }
        }
        return instance;
    }
    
    public DataSource getDataSource(String key) {
        return dataSources.computeIfAbsent(key, this::createDataSource);
    }
    
    private HikariDataSource createDataSource(String key) {
        synchronized (lock) {
            HikariConfig config = new HikariConfig();
            config.setJdbcUrl(getJdbcUrl(key));
            config.setUsername(getUsername(key));
            config.setPassword(getPassword(key));
            config.setMaximumPoolSize(getMaxPoolSize(key));
            config.setMinimumIdle(getMinIdle(key));
            config.setConnectionTimeout(getConnectionTimeout(key));
            
            return new HikariDataSource(config);
        }
    }
    
    public void closeAllDataSources() {
        dataSources.values().forEach(HikariDataSource::close);
        dataSources.clear();
    }
}
```

---

## 🧬 原型模式（Prototype Pattern）

### 4.1 配置模板克隆

**应用场景**：快速复制和定制配置模板

**实现代码**：
```java
// 可克隆的配置模板
public class ConfigurationTemplate implements Cloneable {
    
    private String templateId;
    private String templateName;
    private String industryCode;
    private Map<String, Object> configurations;
    private List<ValidationRule> validationRules;
    private List<BusinessRule> businessRules;
    private Date createdAt;
    private Date updatedAt;
    
    // 深克隆实现
    @Override
    public ConfigurationTemplate clone() {
        try {
            ConfigurationTemplate cloned = (ConfigurationTemplate) super.clone();
            
            // 深克隆复杂对象
            cloned.configurations = deepCloneConfigurations(this.configurations);
            cloned.validationRules = deepCloneValidationRules(this.validationRules);
            cloned.businessRules = deepCloneBusinessRules(this.businessRules);
            
            // 重置时间戳
            cloned.createdAt = new Date();
            cloned.updatedAt = new Date();
            
            return cloned;
        } catch (CloneNotSupportedException e) {
            throw new RuntimeException("配置模板克隆失败", e);
        }
    }
    
    private Map<String, Object> deepCloneConfigurations(Map<String, Object> original) {
        if (original == null) return null;
        
        Map<String, Object> cloned = new HashMap<>();
        original.forEach((key, value) -> {
            if (value instanceof Map) {
                cloned.put(key, deepCloneMap((Map<?, ?>) value));
            } else if (value instanceof List) {
                cloned.put(key, deepCloneList((List<?>) value));
            } else {
                cloned.put(key, cloneValue(value));
            }
        });
        return cloned;
    }
    
    // 便利的克隆方法
    public ConfigurationTemplate cloneForIndustry(String newIndustryCode) {
        ConfigurationTemplate cloned = this.clone();
        cloned.setIndustryCode(newIndustryCode);
        cloned.setTemplateId(generateNewTemplateId());
        cloned.setTemplateName(this.templateName + " - " + newIndustryCode);
        return cloned;
    }
    
    public ConfigurationTemplate cloneWithCustomizations(Map<String, Object> customizations) {
        ConfigurationTemplate cloned = this.clone();
        cloned.configurations.putAll(customizations);
        cloned.setTemplateId(generateNewTemplateId());
        return cloned;
    }
}

// 配置模板管理器
@Service
public class ConfigurationTemplateManager {
    
    private final Map<String, ConfigurationTemplate> templateCache = new ConcurrentHashMap<>();
    
    @Autowired
    private ConfigurationTemplateRepository templateRepository;
    
    public ConfigurationTemplate getTemplate(String templateId) {
        return templateCache.computeIfAbsent(templateId, this::loadTemplate);
    }
    
    public ConfigurationTemplate createCustomTemplate(String baseTemplateId, 
                                                    String newIndustryCode,
                                                    Map<String, Object> customizations) {
        ConfigurationTemplate baseTemplate = getTemplate(baseTemplateId);
        ConfigurationTemplate customTemplate = baseTemplate
            .cloneForIndustry(newIndustryCode)
            .cloneWithCustomizations(customizations);
        
        // 保存新模板
        templateRepository.save(customTemplate);
        templateCache.put(customTemplate.getTemplateId(), customTemplate);
        
        return customTemplate;
    }
    
    // 预定义模板的快速克隆
    public ConfigurationTemplate createAutomotiveTemplate() {
        ConfigurationTemplate baseTemplate = getTemplate("base_industry_template");
        return baseTemplate.cloneForIndustry("automotive");
    }
    
    public ConfigurationTemplate createStarbucksTemplate() {
        ConfigurationTemplate baseTemplate = getTemplate("service_industry_template");
        return baseTemplate.cloneForIndustry("starbucks");
    }
}
```

### 4.2 分析结果原型

**应用场景**：复制分析结果结构用于批量处理

**实现代码**：
```java
// 分析结果原型
public class AnalysisResultPrototype implements Cloneable {
    
    private String recordId;
    private String originalText;
    private SentimentResult sentimentResult;
    private IntentResult intentResult;
    private TopicResult topicResult;
    private Map<String, Object> metadata;
    private Instant analysisTime;
    
    @Override
    public AnalysisResultPrototype clone() {
        try {
            AnalysisResultPrototype cloned = (AnalysisResultPrototype) super.clone();
            
            // 深克隆复杂对象
            cloned.sentimentResult = this.sentimentResult != null ? this.sentimentResult.clone() : null;
            cloned.intentResult = this.intentResult != null ? this.intentResult.clone() : null;
            cloned.topicResult = this.topicResult != null ? this.topicResult.clone() : null;
            cloned.metadata = this.metadata != null ? new HashMap<>(this.metadata) : null;
            
            return cloned;
        } catch (CloneNotSupportedException e) {
            throw new RuntimeException("分析结果克隆失败", e);
        }
    }
    
    // 为新记录创建分析结果实例
    public AnalysisResultPrototype createForRecord(String recordId, String originalText) {
        AnalysisResultPrototype newResult = this.clone();
        newResult.recordId = recordId;
        newResult.originalText = originalText;
        newResult.analysisTime = Instant.now();
        
        // 重置分析结果
        newResult.sentimentResult = null;
        newResult.intentResult = null;
        newResult.topicResult = null;
        
        return newResult;
    }
}

// 批量分析处理器
@Service
public class BatchAnalysisProcessor {
    
    private final AnalysisResultPrototype resultPrototype;
    
    public BatchAnalysisProcessor() {
        // 初始化结果原型
        this.resultPrototype = createBaseResultPrototype();
    }
    
    public List<AnalysisResultPrototype> processBatch(List<RawDataRecord> records) {
        return records.parallelStream()
            .map(record -> {
                // 克隆原型为每条记录创建分析结果
                AnalysisResultPrototype result = resultPrototype.createForRecord(
                    record.getId(), 
                    record.getText()
                );
                
                // 执行分析
                performAnalysis(result);
                
                return result;
            })
            .collect(Collectors.toList());
    }
    
    private AnalysisResultPrototype createBaseResultPrototype() {
        AnalysisResultPrototype prototype = new AnalysisResultPrototype();
        prototype.setMetadata(createDefaultMetadata());
        return prototype;
    }
}
```

---

## 📋 总结

### 创建型模式应用效果

1. **工厂模式**：提供了灵活的对象创建机制，支持多种数据源和分析引擎的动态创建
2. **建造者模式**：简化了复杂配置对象的构建过程，提高了代码的可读性和可维护性
3. **单例模式**：确保了关键资源（如配置管理器、连接池）的全局唯一性和线程安全
4. **原型模式**：提高了对象创建的效率，特别适用于批量数据处理场景

### 设计考虑

- **线程安全**：所有单例实现都考虑了多线程环境下的安全性
- **性能优化**：使用了延迟加载、对象池等技术优化性能
- **扩展性**：工厂模式支持新类型的动态注册和扩展
- **可维护性**：建造者模式使复杂对象的构建过程更加清晰

---

**文档维护**: 系统架构师、开发团队  
**审核**: 技术负责人、代码审查员  
**下次更新**: 设计模式应用变更时及时更新 