# ODS到DWD层ETL流程设计

## 1. 概述

本文档详细描述了通用VOC报表系统中从ODS原数据层到DWD明细层的ETL（Extract、Transform、Load）流程设计，采用配置驱动的方式，实现多行业、多数据源的统一数据建模。

## 2. 数据流架构

### 2.1 架构设计原则
- **配置驱动**：通过配置文件驱动整个ETL流程，支持不同行业的灵活适配
- **标准化处理**：统一的数据处理流程，保证数据质量和一致性
- **可扩展性**：支持新数据源和新字段的快速接入
- **可追溯性**：完整的数据血缘和处理记录

### 2.2 数据分层说明
```
ODS层(Operational Data Store) --> DWD层(Data Warehouse Detail)
原始数据存储层                --> 明细数据层(宽表)
```

## 3. ODS原数据层表结构

### 3.1 原始反馈数据表 (ods_voc_feedback_raw)
```sql
CREATE TABLE ods_voc_feedback_raw (
    id VARCHAR(64) PRIMARY KEY COMMENT '反馈ID',
    content TEXT COMMENT '反馈内容',
    create_time DATETIME COMMENT '创建时间',
    source_channel VARCHAR(32) COMMENT '来源渠道',
    data_source VARCHAR(32) COMMENT '数据源标识',
    industry_code VARCHAR(16) COMMENT '行业代码',
    raw_data JSON COMMENT '原始数据JSON',
    etl_insert_time DATETIME COMMENT 'ETL插入时间'
);
```

### 3.2 渠道信息表 (ods_channel_info)
```sql
CREATE TABLE ods_channel_info (
    channel_id VARCHAR(32) PRIMARY KEY COMMENT '渠道ID',
    channel_name VARCHAR(64) COMMENT '渠道名称',
    channel_type VARCHAR(32) COMMENT '渠道类型',
    platform VARCHAR(32) COMMENT '平台',
    create_time DATETIME COMMENT '创建时间'
);
```

### 3.3 品牌产品信息表 (ods_product_info)
```sql
CREATE TABLE ods_product_info (
    product_id VARCHAR(32) PRIMARY KEY COMMENT '产品ID',
    product_name VARCHAR(128) COMMENT '产品名称',
    brand_id VARCHAR(32) COMMENT '品牌ID',
    brand_name VARCHAR(64) COMMENT '品牌名称',
    category VARCHAR(64) COMMENT '产品类别',
    model VARCHAR(64) COMMENT '型号',
    version VARCHAR(32) COMMENT '版本'
);
```

### 3.4 客户信息表 (ods_customer_info)
```sql
CREATE TABLE ods_customer_info (
    customer_id VARCHAR(32) PRIMARY KEY COMMENT '客户ID',
    customer_name VARCHAR(64) COMMENT '客户姓名',
    age INT COMMENT '年龄',
    gender VARCHAR(8) COMMENT '性别',
    region VARCHAR(64) COMMENT '地区',
    value_level VARCHAR(16) COMMENT '价值等级',
    contact_info VARCHAR(128) COMMENT '联系信息'
);
```

### 3.5 经销商门店信息表 (ods_dealer_info)
```sql
CREATE TABLE ods_dealer_info (
    dealer_id VARCHAR(32) PRIMARY KEY COMMENT '经销商ID',
    dealer_name VARCHAR(128) COMMENT '经销商名称',
    region VARCHAR(64) COMMENT '所在地区',
    level VARCHAR(8) COMMENT '经销商等级',
    type VARCHAR(32) COMMENT '经销商类型',
    contact_info VARCHAR(128) COMMENT '联系信息'
);
```

### 3.6 社交媒体信息表 (ods_social_media)
```sql
CREATE TABLE ods_social_media (
    id VARCHAR(64) PRIMARY KEY COMMENT '记录ID',
    platform VARCHAR(32) COMMENT '社交平台',
    account_id VARCHAR(64) COMMENT '账号ID',
    follower_count BIGINT COMMENT '粉丝数',
    verified BOOLEAN COMMENT '是否认证',
    post_type VARCHAR(32) COMMENT '帖子类型',
    engagement_score DECIMAL(10,2) COMMENT '互动分数'
);
```

### 3.7 工单信息表 (ods_ticket_info)
```sql
CREATE TABLE ods_ticket_info (
    ticket_id VARCHAR(32) PRIMARY KEY COMMENT '工单ID',
    status VARCHAR(16) COMMENT '工单状态',
    priority VARCHAR(16) COMMENT '优先级',
    category VARCHAR(32) COMMENT '分类',
    handler VARCHAR(64) COMMENT '处理人',
    resolution_time DATETIME COMMENT '解决时间'
);
```

### 3.8 标签信息表 (ods_tag_info)
```sql
CREATE TABLE ods_tag_info (
    tag_id VARCHAR(32) PRIMARY KEY COMMENT '标签ID',
    tag_name VARCHAR(64) COMMENT '标签名称',
    tag_type VARCHAR(32) COMMENT '标签类型',
    weight DECIMAL(5,2) COMMENT '权重',
    parent_tag VARCHAR(32) COMMENT '父标签'
);
```

### 3.9 时间维度表 (ods_time_dimension)
```sql
CREATE TABLE ods_time_dimension (
    date_key INT PRIMARY KEY COMMENT '日期键',
    full_date DATE COMMENT '完整日期',
    year INT COMMENT '年',
    quarter INT COMMENT '季度',
    month INT COMMENT '月',
    week INT COMMENT '周',
    day_of_week INT COMMENT '星期',
    is_holiday BOOLEAN COMMENT '是否节假日',
    season VARCHAR(16) COMMENT '季节'
);
```

## 4. 配置管理层

### 4.1 行业配置管理 (config_industry_mapping)
```sql
CREATE TABLE config_industry_mapping (
    id INT PRIMARY KEY AUTO_INCREMENT,
    industry_code VARCHAR(16) COMMENT '行业代码',
    config_name VARCHAR(64) COMMENT '配置名称',
    config_value JSON COMMENT '配置值',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    create_time DATETIME COMMENT '创建时间',
    update_time DATETIME COMMENT '更新时间'
);
```

**配置示例**：
```json
{
  "automotive": {
    "field_mapping": {
      "vehicle_vin": "product_id",
      "dealer_code": "dealer_id",
      "customer_phone": "contact_info"
    },
    "data_quality": {
      "required_fields": ["content", "create_time", "product_id"],
      "validation_rules": {
        "content_min_length": 10,
        "vin_pattern": "^[A-HJ-NPR-Z0-9]{17}$"
      }
    }
  }
}
```

### 4.2 字段映射配置 (config_field_mapping)
```sql
CREATE TABLE config_field_mapping (
    id INT PRIMARY KEY AUTO_INCREMENT,
    industry_code VARCHAR(16) COMMENT '行业代码',
    source_table VARCHAR(64) COMMENT '源表名',
    source_field VARCHAR(64) COMMENT '源字段名',
    target_field VARCHAR(64) COMMENT '目标字段名',
    data_type VARCHAR(32) COMMENT '数据类型',
    transformation_rule TEXT COMMENT '转换规则',
    is_required BOOLEAN DEFAULT FALSE COMMENT '是否必填',
    default_value VARCHAR(128) COMMENT '默认值'
);
```

### 4.3 数据质量配置 (config_data_quality)
```sql
CREATE TABLE config_data_quality (
    id INT PRIMARY KEY AUTO_INCREMENT,
    industry_code VARCHAR(16) COMMENT '行业代码',
    rule_name VARCHAR(64) COMMENT '规则名称',
    rule_type VARCHAR(32) COMMENT '规则类型',
    validation_logic TEXT COMMENT '验证逻辑',
    error_handling VARCHAR(32) COMMENT '错误处理方式',
    threshold_value DECIMAL(10,2) COMMENT '阈值'
);
```

### 4.4 业务规则配置 (config_business_rules)
```sql
CREATE TABLE config_business_rules (
    id INT PRIMARY KEY AUTO_INCREMENT,
    industry_code VARCHAR(16) COMMENT '行业代码',
    rule_name VARCHAR(64) COMMENT '规则名称',
    rule_type VARCHAR(32) COMMENT '规则类型',
    calculation_logic TEXT COMMENT '计算逻辑',
    dependency_fields JSON COMMENT '依赖字段',
    output_field VARCHAR(64) COMMENT '输出字段'
);
```

## 5. ETL处理流程

### 5.1 数据提取模块 (Extract Module)

**功能说明**：
- 从多个ODS表中提取数据
- 支持全量和增量提取
- 数据预处理和格式标准化

**处理逻辑**：
```python
def extract_data(industry_code, extract_type='incremental'):
    """
    数据提取函数
    """
    # 1. 根据行业代码获取数据源配置
    config = get_industry_config(industry_code)
    
    # 2. 确定提取范围
    if extract_type == 'incremental':
        last_update_time = get_last_etl_time()
        where_condition = f"WHERE update_time > '{last_update_time}'"
    else:
        where_condition = ""
    
    # 3. 从各个ODS表提取数据
    feedback_data = extract_from_table('ods_voc_feedback_raw', where_condition)
    channel_data = extract_from_table('ods_channel_info', where_condition)
    product_data = extract_from_table('ods_product_info', where_condition)
    # ... 其他表的数据提取
    
    return {
        'feedback': feedback_data,
        'channel': channel_data,
        'product': product_data,
        # ... 其他数据
    }
```

### 5.2 配置加载引擎 (Config Loader)

**功能说明**：
- 动态加载行业配置
- 字段映射规则加载
- 质量规则和业务规则加载

**处理逻辑**：
```python
def load_etl_config(industry_code):
    """
    加载ETL配置
    """
    config = {
        'field_mapping': load_field_mapping_config(industry_code),
        'data_quality': load_data_quality_config(industry_code),
        'business_rules': load_business_rules_config(industry_code)
    }
    return config
```

### 5.3 数据转换引擎 (Transform Engine)

**功能说明**：
- 字段映射转换
- 数据类型转换
- 业务规则应用
- 数据清洗

**核心转换逻辑**：
```python
def transform_data(raw_data, config):
    """
    数据转换函数
    """
    transformed_data = {}
    
    # 1. 字段映射转换
    for source_field, target_field in config['field_mapping'].items():
        if source_field in raw_data:
            transformed_data[target_field] = apply_field_transformation(
                raw_data[source_field], 
                config['transformation_rules'].get(source_field)
            )
    
    # 2. 业务规则应用
    for rule in config['business_rules']:
        if rule['rule_type'] == 'calculation':
            transformed_data[rule['output_field']] = apply_calculation_rule(
                transformed_data, 
                rule['calculation_logic']
            )
        elif rule['rule_type'] == 'derivation':
            transformed_data[rule['output_field']] = apply_derivation_rule(
                transformed_data, 
                rule['derivation_logic']
            )
    
    return transformed_data
```

### 5.4 数据质量检查 (Quality Check)

**检查维度**：
- **完整性检查**：必填字段是否为空
- **一致性检查**：数据格式是否一致
- **准确性检查**：数据值是否符合业务规则
- **及时性检查**：数据是否在有效时间范围内

**质量检查逻辑**：
```python
def quality_check(data, quality_rules):
    """
    数据质量检查
    """
    quality_result = {
        'is_valid': True,
        'error_code': None,
        'error_message': None,
        'quality_score': 100.0
    }
    
    for rule in quality_rules:
        if rule['rule_type'] == 'completeness':
            if not check_completeness(data, rule):
                quality_result['is_valid'] = False
                quality_result['error_code'] = 'COMPLETENESS_ERROR'
        elif rule['rule_type'] == 'consistency':
            if not check_consistency(data, rule):
                quality_result['is_valid'] = False
                quality_result['error_code'] = 'CONSISTENCY_ERROR'
        # ... 其他质量检查
    
    return quality_result
```

### 5.5 宽表构建器 (Wide Table Builder)

**功能说明**：
- 多表关联合并
- 字段冗余处理
- 索引优化
- 分区策略

**宽表构建逻辑**：
```python
def build_wide_table(processed_data):
    """
    构建DWD宽表
    """
    # 1. 主表关联
    wide_table_data = processed_data['feedback']
    
    # 2. 关联渠道信息
    wide_table_data = left_join(
        wide_table_data, 
        processed_data['channel'], 
        on='channel_id'
    )
    
    # 3. 关联产品信息
    wide_table_data = left_join(
        wide_table_data, 
        processed_data['product'], 
        on='product_id'
    )
    
    # 4. 关联客户信息
    wide_table_data = left_join(
        wide_table_data, 
        processed_data['customer'], 
        on='customer_id'
    )
    
    # 5. 其他维度表关联
    # ... 
    
    return wide_table_data
```

## 6. DWD明细宽表结构

### 6.1 表结构定义
```sql
CREATE TABLE dwd_voc_feedback_detail_wide (
    -- 主键和基础信息
    feedback_id VARCHAR(64) PRIMARY KEY COMMENT '反馈ID',
    content TEXT COMMENT '反馈内容',
    create_time DATETIME COMMENT '创建时间',
    update_time DATETIME COMMENT '更新时间',
    industry_code VARCHAR(16) COMMENT '行业代码',
    data_source VARCHAR(32) COMMENT '数据来源',
    
    -- 渠道信息字段
    channel_id VARCHAR(32) COMMENT '渠道ID',
    channel_name VARCHAR(64) COMMENT '渠道名称',
    channel_type VARCHAR(32) COMMENT '渠道类型',
    platform VARCHAR(32) COMMENT '平台',
    channel_category VARCHAR(32) COMMENT '渠道分类',
    
    -- 品牌产品信息字段
    product_id VARCHAR(32) COMMENT '产品ID',
    product_name VARCHAR(128) COMMENT '产品名称',
    brand_id VARCHAR(32) COMMENT '品牌ID',
    brand_name VARCHAR(64) COMMENT '品牌名称',
    category VARCHAR(64) COMMENT '产品类别',
    model VARCHAR(64) COMMENT '型号',
    version VARCHAR(32) COMMENT '版本',
    product_line VARCHAR(64) COMMENT '产品线',
    
    -- 客户信息字段
    customer_id VARCHAR(32) COMMENT '客户ID',
    customer_name VARCHAR(64) COMMENT '客户姓名',
    age INT COMMENT '年龄',
    gender VARCHAR(8) COMMENT '性别',
    region VARCHAR(64) COMMENT '地区',
    value_level VARCHAR(16) COMMENT '价值等级',
    customer_type VARCHAR(32) COMMENT '客户类型',
    contact_info VARCHAR(128) COMMENT '联系信息',
    
    -- 经销商门店信息字段
    dealer_id VARCHAR(32) COMMENT '经销商ID',
    dealer_name VARCHAR(128) COMMENT '经销商名称',
    dealer_region VARCHAR(64) COMMENT '经销商地区',
    dealer_level VARCHAR(8) COMMENT '经销商等级',
    dealer_type VARCHAR(32) COMMENT '经销商类型',
    dealer_contact VARCHAR(128) COMMENT '经销商联系方式',
    
    -- 社交媒体信息字段
    social_platform VARCHAR(32) COMMENT '社交平台',
    account_id VARCHAR(64) COMMENT '账号ID',
    follower_count BIGINT COMMENT '粉丝数',
    verified_flag BOOLEAN COMMENT '认证标识',
    post_type VARCHAR(32) COMMENT '帖子类型',
    engagement_score DECIMAL(10,2) COMMENT '互动分数',
    
    -- 工单信息字段
    ticket_id VARCHAR(32) COMMENT '工单ID',
    ticket_status VARCHAR(16) COMMENT '工单状态',
    priority VARCHAR(16) COMMENT '优先级',
    ticket_category VARCHAR(32) COMMENT '工单分类',
    handler VARCHAR(64) COMMENT '处理人',
    resolution_time DATETIME COMMENT '解决时间',
    
    -- 标签信息字段
    primary_tags JSON COMMENT '主要标签',
    secondary_tags JSON COMMENT '次要标签',
    tag_weights JSON COMMENT '标签权重',
    tag_hierarchy JSON COMMENT '标签层级',
    auto_tags JSON COMMENT '自动标签',
    manual_tags JSON COMMENT '手动标签',
    
    -- AI分析结果字段
    sentiment_score DECIMAL(5,2) COMMENT '情感分数',
    sentiment_label VARCHAR(16) COMMENT '情感标签',
    intent_category VARCHAR(32) COMMENT '意图分类',
    topic_category VARCHAR(32) COMMENT '主题分类',
    urgency_level VARCHAR(16) COMMENT '紧急程度',
    confidence_score DECIMAL(5,2) COMMENT '置信度分数',
    
    -- 时间维度字段
    date_key INT COMMENT '日期键',
    year INT COMMENT '年',
    quarter INT COMMENT '季度',
    month INT COMMENT '月',
    week INT COMMENT '周',
    day_of_week INT COMMENT '星期',
    is_holiday BOOLEAN COMMENT '是否节假日',
    season VARCHAR(16) COMMENT '季节',
    
    -- 质量和元数据字段
    data_quality_score DECIMAL(5,2) COMMENT '数据质量分数',
    is_valid BOOLEAN DEFAULT TRUE COMMENT '数据是否有效',
    error_code VARCHAR(32) COMMENT '错误代码',
    process_time DATETIME COMMENT '处理时间',
    etl_batch_id VARCHAR(32) COMMENT 'ETL批次ID',
    
    -- 分区和索引
    INDEX idx_industry_time (industry_code, create_time),
    INDEX idx_channel_sentiment (channel_id, sentiment_label),
    INDEX idx_product_topic (product_id, topic_category),
    INDEX idx_customer_time (customer_id, create_time),
    INDEX idx_dealer_region (dealer_id, dealer_region)
    
) ENGINE=InnoDB 
PARTITION BY HASH(feedback_id) PARTITIONS 32
COMMENT='VOC反馈明细宽表';
```

## 7. ETL调度和监控

### 7.1 调度策略
- **实时处理**：关键数据5分钟内处理完成
- **批量处理**：每小时批量处理一次
- **全量重建**：每周末进行全量数据重建

### 7.2 监控指标
- **处理时效**：数据处理时间监控
- **数据质量**：质量分数监控
- **错误率**：处理错误率监控
- **数据量**：数据量变化监控

### 7.3 异常处理
- **数据回滚**：支持数据版本回滚
- **错误恢复**：自动重试机制
- **告警通知**：实时告警通知

## 8. 性能优化

### 8.1 分区策略
```sql
-- 按行业和时间分区
PARTITION BY LIST(industry_code) 
SUBPARTITION BY RANGE(create_time)
```

### 8.2 索引优化
- **复合索引**：基于查询模式创建复合索引
- **分区索引**：每个分区独立索引
- **列式存储**：分析场景使用列式存储

### 8.3 缓存策略
- **配置缓存**：ETL配置信息缓存
- **字典缓存**：维度数据缓存
- **结果缓存**：计算结果缓存

## 9. 扩展和维护

### 9.1 新行业接入
1. 配置行业映射规则
2. 定义字段转换逻辑
3. 设置数据质量规则
4. 配置业务计算规则

### 9.2 新字段扩展
1. 更新表结构
2. 修改ETL配置
3. 更新数据质量检查
4. 测试验证

### 9.3 版本管理
- **配置版本化**：配置变更版本管理
- **脚本版本化**：ETL脚本版本管理
- **数据版本化**：数据血缘版本管理

通过这套配置驱动的ETL流程，可以快速适配不同行业的VOC数据处理需求，保证数据质量的同时提高开发效率。