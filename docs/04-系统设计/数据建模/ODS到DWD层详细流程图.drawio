<mxfile host="app.diagrams.net" modified="2025-08-08T00:00:00.000Z" agent="Augment Agent" version="24.7.10" type="device">
  <diagram id="ods-dwd-detail" name="ODS→DWD详细流程">
    <mxGraphModel dx="1600" dy="900" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="2200" pageHeight="1400" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>

        <!-- Swimlanes -->
        <mxCell id="lane_sources" value="数据源" style="swimlane;childLayout=stackLayout;horizontal=0;startSize=30;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="20" y="20" width="340" height="1300" as="geometry"/>
        </mxCell>
        <mxCell id="lane_ingest" value="采集/落地" style="swimlane;childLayout=stackLayout;horizontal=0;startSize=30;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="370" y="20" width="340" height="1300" as="geometry"/>
        </mxCell>
        <mxCell id="lane_ods" value="ODS层" style="swimlane;childLayout=stackLayout;horizontal=0;startSize=30;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="720" y="20" width="340" height="1300" as="geometry"/>
        </mxCell>
        <mxCell id="lane_dq" value="数据质量DQC" style="swimlane;childLayout=stackLayout;horizontal=0;startSize=30;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="1070" y="20" width="340" height="1300" as="geometry"/>
        </mxCell>
        <mxCell id="lane_transform" value="标准化/转换" style="swimlane;childLayout=stackLayout;horizontal=0;startSize=30;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="1420" y="20" width="340" height="1300" as="geometry"/>
        </mxCell>
        <mxCell id="lane_dwd" value="DWD层(维度/事实)" style="swimlane;childLayout=stackLayout;horizontal=0;startSize=30;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="1770" y="20" width="380" height="1300" as="geometry"/>
        </mxCell>

        <!-- 数据源节点 -->
        <mxCell id="src_db" value="关系型DB\n(全量/增量/CDC)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="lane_sources">
          <mxGeometry x="20" y="60" width="300" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="src_api" value="API/第三方平台" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="lane_sources">
          <mxGeometry x="20" y="160" width="300" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="src_log" value="日志/埋点/消息队列" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="lane_sources">
          <mxGeometry x="20" y="260" width="300" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="src_file" value="文件/对象存储(CSV/Parquet)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="lane_sources">
          <mxGeometry x="20" y="360" width="300" height="80" as="geometry"/>
        </mxCell>

        <!-- 采集/落地 -->
        <mxCell id="ingest_connect" value="采集连接器\n(Flink/Spark/KafkaConnect)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="lane_ingest">
          <mxGeometry x="20" y="60" width="300" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="landing_raw" value="着陆区/原始区\n(分区: dt/hour)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="lane_ingest">
          <mxGeometry x="20" y="160" width="300" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="ingest_dedup" value="初步去重/幂等\n(唯一键+事件时间)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="lane_ingest">
          <mxGeometry x="20" y="260" width="300" height="80" as="geometry"/>
        </mxCell>

        <!-- ODS层 -->
        <mxCell id="ods_schema" value="模式对齐/字段标准化\n(数据类型/时区/编码)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="lane_ods">
          <mxGeometry x="20" y="60" width="300" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="ods_partition" value="ODS落表\n(命名: ods.xxx; 分区: dt)\n写入: upsert/merge" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="lane_ods">
          <mxGeometry x="20" y="160" width="300" height="90" as="geometry"/>
        </mxCell>

        <!-- DQ层 -->
        <mxCell id="dq_schema" value="模式校验/必填/范围/引用一致性" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="lane_dq">
          <mxGeometry x="20" y="60" width="300" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="dq_uniqueness" value="主键/唯一性/重复检测" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="lane_dq">
          <mxGeometry x="20" y="160" width="300" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="dq_dlq" value="异常与脏数据隔离(DLQ)\n回灌/重放机制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="lane_dq">
          <mxGeometry x="20" y="260" width="300" height="90" as="geometry"/>
        </mxCell>

        <!-- 转换层 -->
        <mxCell id="std_mapping" value="码表映射/口径统一\n(枚举/字典/单位)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="lane_transform">
          <mxGeometry x="20" y="60" width="300" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="dim_build" value="维度构建(SCD)\n• SCD1 覆盖\n• SCD2 拉链: start/end/is_current/version\n• SCD3 位点" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="lane_transform">
          <mxGeometry x="20" y="160" width="300" height="120" as="geometry"/>
        </mxCell>
        <mxCell id="fact_build" value="事实构建\n维度关联/度量聚合\n晚到/跨窗补算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="lane_transform">
          <mxGeometry x="20" y="300" width="300" height="100" as="geometry"/>
        </mxCell>

        <!-- DWD层 -->
        <mxCell id="dwd_dim" value="DWD维度表\n命名: dwd.dim_xxx\n分区: dt 或快照日\n键: surrogate_key/自然键" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="lane_dwd">
          <mxGeometry x="20" y="60" width="320" height="120" as="geometry"/>
        </mxCell>
        <mxCell id="dwd_fact" value="DWD事实表\n命名: dwd.fact_xxx\n粒度: 明确定义\n分区: dt/hour\n写入: merge/upsert" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="lane_dwd">
          <mxGeometry x="20" y="200" width="320" height="120" as="geometry"/>
        </mxCell>
        <mxCell id="dwd_opt" value="性能优化\n小文件合并/排序键\nZ-Order/Cluster/物化视图" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="lane_dwd">
          <mxGeometry x="20" y="340" width="320" height="100" as="geometry"/>
        </mxCell>

        <!-- 编排/监控（悬浮区说明） -->
        <mxCell id="orchestration" value="调度与监控\n• 调度: Airflow/DS\n• 依赖/SLA/重跑\n• 质量告警/血缘登记/权限脱敏" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#4d88ff;" vertex="1" parent="1">
          <mxGeometry x="20" y="1350" width="2130" height="120" as="geometry"/>
        </mxCell>

        <!-- 连线 -->
        <mxCell id="e1" edge="1" parent="1" source="src_db" target="ingest_connect" style="endArrow=block;strokeWidth=2;"/>
        <mxCell id="e2" edge="1" parent="1" source="src_api" target="ingest_connect" style="endArrow=block;strokeWidth=2;"/>
        <mxCell id="e3" edge="1" parent="1" source="src_log" target="ingest_connect" style="endArrow=block;strokeWidth=2;"/>
        <mxCell id="e4" edge="1" parent="1" source="src_file" target="ingest_connect" style="endArrow=block;strokeWidth=2;"/>

        <mxCell id="e5" edge="1" parent="1" source="ingest_connect" target="landing_raw" style="endArrow=block;strokeWidth=2;"/>
        <mxCell id="e6" edge="1" parent="1" source="landing_raw" target="ingest_dedup" style="endArrow=block;strokeWidth=2;"/>

        <mxCell id="e7" edge="1" parent="1" source="ingest_dedup" target="ods_schema" style="endArrow=block;strokeWidth=2;"/>
        <mxCell id="e8" edge="1" parent="1" source="ods_schema" target="ods_partition" style="endArrow=block;strokeWidth=2;"/>

        <mxCell id="e9" edge="1" parent="1" source="ods_partition" target="dq_schema" style="endArrow=block;strokeWidth=2;"/>
        <mxCell id="e10" edge="1" parent="1" source="dq_schema" target="dq_uniqueness" style="endArrow=block;strokeWidth=2;"/>
        <mxCell id="e11" edge="1" parent="1" source="dq_uniqueness" target="dq_dlq" style="endArrow=block;strokeWidth=2;"/>

        <mxCell id="e12" edge="1" parent="1" source="dq_uniqueness" target="std_mapping" style="endArrow=block;strokeWidth=2;"/>
        <mxCell id="e13" edge="1" parent="1" source="std_mapping" target="dim_build" style="endArrow=block;strokeWidth=2;"/>
        <mxCell id="e14" edge="1" parent="1" source="dim_build" target="fact_build" style="endArrow=block;strokeWidth=2;"/>

        <mxCell id="e15" edge="1" parent="1" source="dim_build" target="dwd_dim" style="endArrow=block;strokeWidth=2;dashed=1;"/>
        <mxCell id="e16" edge="1" parent="1" source="fact_build" target="dwd_fact" style="endArrow=block;strokeWidth=2;"/>
        <mxCell id="e17" edge="1" parent="1" source="dwd_dim" target="dwd_opt" style="endArrow=block;strokeWidth=2;dashed=1;"/>
        <mxCell id="e18" edge="1" parent="1" source="dwd_fact" target="dwd_opt" style="endArrow=block;strokeWidth=2;dashed=1;"/>

        <!-- 编排联动 -->
        <mxCell id="e19" edge="1" parent="1" source="orchestration" target="ingest_connect" style="endArrow=block;strokeWidth=2;dashed=1;"/>
        <mxCell id="e20" edge="1" parent="1" source="orchestration" target="ods_partition" style="endArrow=block;strokeWidth=2;dashed=1;"/>
        <mxCell id="e21" edge="1" parent="1" source="orchestration" target="dq_schema" style="endArrow=block;strokeWidth=2;dashed=1;"/>
        <mxCell id="e22" edge="1" parent="1" source="orchestration" target="fact_build" style="endArrow=block;strokeWidth=2;dashed=1;"/>
        <mxCell id="e23" edge="1" parent="1" source="orchestration" target="dwd_fact" style="endArrow=block;strokeWidth=2;dashed=1;"/>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

