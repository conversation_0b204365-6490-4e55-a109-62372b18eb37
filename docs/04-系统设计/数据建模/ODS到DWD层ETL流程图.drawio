<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" version="28.0.7">
  <diagram id="0ifcwR8jgRXGxxK5kF64" name="第 1 页">
    <mxGraphModel dx="950" dy="554" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="2000" pageHeight="2500" background="#ffffff" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="title" value="VOC系统 ODS原数据到DWD明细层ETL流程图" style="text;strokeColor=none;fillColor=none;html=1;fontSize=24;fontStyle=1;verticalAlign=middle;align=center;fontColor=#2c3e50;" parent="1" vertex="1">
          <mxGeometry x="600" y="20" width="497" height="40" as="geometry" />
        </mxCell>
        <mxCell id="subtitle" value="配置驱动的数据建模与宽表生成流程" style="text;strokeColor=none;fillColor=none;html=1;fontSize=16;fontStyle=0;verticalAlign=middle;align=center;fontColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="650" y="65" width="397" height="25" as="geometry" />
        </mxCell>
        <mxCell id="ods-layer" value="ODS原数据层 (Operational Data Store)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="50" y="120" width="1400" height="300" as="geometry" />
        </mxCell>
        <mxCell id="ods-feedback" value="原始反馈数据表&#xa;ods_voc_feedback_raw&#xa;&#xa;• id, content&#xa;• create_time&#xa;• source_channel&#xa;• data_source&#xa;• industry_code&#xa;• raw_data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="ods-layer" vertex="1">
          <mxGeometry x="20" y="40" width="160" height="110" as="geometry" />
        </mxCell>
        <mxCell id="ods-channel" value="渠道信息表&#xa;ods_channel_info&#xa;&#xa;• channel_id&#xa;• channel_name&#xa;• channel_type&#xa;• platform&#xa;• create_time" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="ods-layer" vertex="1">
          <mxGeometry x="200" y="40" width="160" height="110" as="geometry" />
        </mxCell>
        <mxCell id="ods-product" value="品牌产品信息表&#xa;ods_product_info&#xa;&#xa;• product_id&#xa;• product_name&#xa;• brand_id, brand_name&#xa;• category, model&#xa;• version" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="ods-layer" vertex="1">
          <mxGeometry x="380" y="40" width="160" height="110" as="geometry" />
        </mxCell>
        <mxCell id="ods-customer" value="客户信息表&#xa;ods_customer_info&#xa;&#xa;• customer_id&#xa;• customer_name&#xa;• age, gender&#xa;• region&#xa;• value_level&#xa;• contact_info" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="ods-layer" vertex="1">
          <mxGeometry x="560" y="40" width="160" height="110" as="geometry" />
        </mxCell>
        <mxCell id="ods-dealer" value="经销商门店信息表&#xa;ods_dealer_info&#xa;&#xa;• dealer_id&#xa;• dealer_name&#xa;• region, level&#xa;• type&#xa;• contact_info" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="ods-layer" vertex="1">
          <mxGeometry x="740" y="40" width="160" height="110" as="geometry" />
        </mxCell>
        <mxCell id="ods-social" value="社交媒体信息表&#xa;ods_social_media&#xa;&#xa;• platform&#xa;• account_id&#xa;• follower_count&#xa;• verified&#xa;• post_type&#xa;• engagement_score" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="ods-layer" vertex="1">
          <mxGeometry x="920" y="40" width="160" height="110" as="geometry" />
        </mxCell>
        <mxCell id="ods-ticket" value="工单信息表&#xa;ods_ticket_info&#xa;&#xa;• ticket_id&#xa;• status&#xa;• priority&#xa;• category&#xa;• handler&#xa;• resolution_time" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="ods-layer" vertex="1">
          <mxGeometry x="1100" y="40" width="160" height="110" as="geometry" />
        </mxCell>
        <mxCell id="ods-tag" value="标签信息表&#xa;ods_tag_info&#xa;&#xa;• tag_id&#xa;• tag_name&#xa;• tag_type&#xa;• weight&#xa;• parent_tag" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="ods-layer" vertex="1">
          <mxGeometry x="20" y="170" width="160" height="110" as="geometry" />
        </mxCell>
        <mxCell id="ods-time" value="时间维度表&#xa;ods_time_dimension&#xa;&#xa;• date_key&#xa;• year, quarter&#xa;• month, week&#xa;• day_of_week&#xa;• is_holiday&#xa;• season" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="ods-layer" vertex="1">
          <mxGeometry x="200" y="170" width="160" height="110" as="geometry" />
        </mxCell>
        <mxCell id="config-layer" value="配置管理层 (Configuration Management)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#2e7d32;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1480" y="120" width="400" height="300" as="geometry" />
        </mxCell>
        <mxCell id="config-industry" value="行业配置管理&#xa;config_industry_mapping&#xa;&#xa;• industry_code&#xa;• field_mapping_rules&#xa;• data_quality_rules&#xa;• business_rules" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="config-layer" vertex="1">
          <mxGeometry x="20" y="40" width="170" height="110" as="geometry" />
        </mxCell>
        <mxCell id="config-field" value="字段映射配置&#xa;config_field_mapping&#xa;&#xa;• source_field&#xa;• target_field&#xa;• data_type&#xa;• transformation_rule" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="config-layer" vertex="1">
          <mxGeometry x="210" y="40" width="170" height="110" as="geometry" />
        </mxCell>
        <mxCell id="config-quality" value="数据质量配置&#xa;config_data_quality&#xa;&#xa;• quality_rules&#xa;• validation_logic&#xa;• error_handling&#xa;• threshold_settings" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="config-layer" vertex="1">
          <mxGeometry x="20" y="170" width="170" height="110" as="geometry" />
        </mxCell>
        <mxCell id="config-business" value="业务规则配置&#xa;config_business_rules&#xa;&#xa;• calculation_rules&#xa;• derivation_logic&#xa;• aggregation_rules&#xa;• classification_rules" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="config-layer" vertex="1">
          <mxGeometry x="210" y="170" width="170" height="110" as="geometry" />
        </mxCell>
        <mxCell id="etl-layer" value="ETL处理层 (Extract Transform Load)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="50" y="450" width="1400" height="400" as="geometry" />
        </mxCell>
        <mxCell id="etl-extract" value="数据提取模块&#xa;Extract Module&#xa;&#xa;• 多源数据抽取&#xa;• 增量识别&#xa;• 数据预处理&#xa;• 格式标准化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=10;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="etl-layer" vertex="1">
          <mxGeometry x="50" y="40" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="etl-config-loader" value="配置加载引擎&#xa;Config Loader&#xa;&#xa;• 动态加载行业配置&#xa;• 字段映射规则加载&#xa;• 质量规则加载&#xa;• 业务规则加载" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=10;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="etl-layer" vertex="1">
          <mxGeometry x="230" y="40" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="etl-transform" value="数据转换引擎&#xa;Transform Engine&#xa;&#xa;• 字段映射转换&#xa;• 数据类型转换&#xa;• 业务规则应用&#xa;• 数据清洗" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=10;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="etl-layer" vertex="1">
          <mxGeometry x="410" y="40" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="etl-quality" value="数据质量检查&#xa;Quality Check&#xa;&#xa;• 完整性检查&#xa;• 一致性检查&#xa;• 准确性检查&#xa;• 及时性检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=10;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="etl-layer" vertex="1">
          <mxGeometry x="590" y="40" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="etl-builder" value="宽表构建器&#xa;Wide Table Builder&#xa;&#xa;• 多表关联&#xa;• 字段合并&#xa;• 冗余字段生成&#xa;• 索引优化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=10;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="etl-layer" vertex="1">
          <mxGeometry x="770" y="40" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="temp-joined" value="临时关联表&#xa;temp_joined_data&#xa;&#xa;• 多源数据初步关联&#xa;• 保留原始字段&#xa;• 添加关联键" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f9a825;fontSize=10;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="etl-layer" vertex="1">
          <mxGeometry x="230" y="150" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="temp-standardized" value="标准化临时表&#xa;temp_standardized&#xa;&#xa;• 字段标准化后数据&#xa;• 统一数据格式&#xa;• 应用映射规则" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor:#f9a825;fontSize=10;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="etl-layer" vertex="1">
          <mxGeometry x="410" y="150" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="step1" value="步骤1: 数据提取" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;fontSize=10;" parent="etl-layer" vertex="1">
          <mxGeometry x="50" y="260" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="step2" value="步骤2: 配置加载" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;fontSize=10;" parent="etl-layer" vertex="1">
          <mxGeometry x="230" y="260" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="step3" value="步骤3: 数据转换" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;fontSize=10;" parent="etl-layer" vertex="1">
          <mxGeometry x="410" y="260" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="step4" value="步骤4: 质量检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;fontSize=10;" parent="etl-layer" vertex="1">
          <mxGeometry x="590" y="260" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="step5" value="步骤5: 宽表构建" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;fontSize=10;" parent="etl-layer" vertex="1">
          <mxGeometry x="770" y="260" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="process-flow" value="ETL处理流程说明：&#xa;1. Extract：从ODS层提取数据，支持全量和增量&#xa;2. Config Loading：动态加载行业配置和转换规则&#xa;3. Transform：应用字段映射、数据转换、业务规则&#xa;4. Quality Check：多维度数据质量检查和验证&#xa;5. Wide Table Building：构建DWD层宽表" style="text;html=1;strokeColor=#cccccc;fillColor=#f8f9fa;align=left;verticalAlign=top;whiteSpace=wrap;rounded=1;fontSize=11;spacing=5;" parent="etl-layer" vertex="1">
          <mxGeometry x="50" y="310" width="820" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dwd-layer" value="DWD明细层 (Data Warehouse Detail)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="50" y="900" width="1800" height="600" as="geometry" />
        </mxCell>
        <mxCell id="dwd-wide-table" value="DWD层VOC明细宽表 - dwd_voc_feedback_detail_wide" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1976d2;fontSize=14;fontStyle=1;verticalAlign=top;spacingTop=10;" parent="dwd-layer" vertex="1">
          <mxGeometry x="50" y="40" width="1700" height="520" as="geometry" />
        </mxCell>
        <mxCell id="basic-fields" value="基础信息字段&#xa;• feedback_id (主键)&#xa;• content (反馈内容)&#xa;• create_time (创建时间)&#xa;• update_time (更新时间)&#xa;• industry_code (行业代码)&#xa;• data_source (数据来源)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0277bd;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="dwd-wide-table" vertex="1">
          <mxGeometry x="20" y="40" width="200" height="130" as="geometry" />
        </mxCell>
        <mxCell id="channel-fields" value="渠道信息字段&#xa;• channel_id, channel_name&#xa;• channel_type, platform&#xa;• channel_category" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0277bd;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="dwd-wide-table" vertex="1">
          <mxGeometry x="240" y="40" width="200" height="130" as="geometry" />
        </mxCell>
        <mxCell id="product-fields" value="品牌产品信息字段&#xa;• product_id, product_name&#xa;• brand_id, brand_name&#xa;• category, model, version&#xa;• product_line" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0277bd;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="dwd-wide-table" vertex="1">
          <mxGeometry x="460" y="40" width="200" height="130" as="geometry" />
        </mxCell>
        <mxCell id="customer-fields" value="客户信息字段&#xa;• customer_id, customer_name&#xa;• age, gender, region&#xa;• value_level, customer_type&#xa;• contact_info" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0277bd;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="dwd-wide-table" vertex="1">
          <mxGeometry x="680" y="40" width="200" height="130" as="geometry" />
        </mxCell>
        <mxCell id="dealer-fields" value="经销商门店信息字段&#xa;• dealer_id, dealer_name&#xa;• dealer_region, dealer_level&#xa;• dealer_type, dealer_contact" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0277bd;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="dwd-wide-table" vertex="1">
          <mxGeometry x="900" y="40" width="200" height="130" as="geometry" />
        </mxCell>
        <mxCell id="social-fields" value="社交媒体信息字段&#xa;• social_platform, account_id&#xa;• follower_count, verified_flag&#xa;• post_type, engagement_score" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0277bd;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="dwd-wide-table" vertex="1">
          <mxGeometry x="1120" y="40" width="200" height="130" as="geometry" />
        </mxCell>
        <mxCell id="ticket-fields" value="工单信息字段&#xa;• ticket_id, ticket_status&#xa;• priority, category&#xa;• handler, resolution_time" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0277bd;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="dwd-wide-table" vertex="1">
          <mxGeometry x="1340" y="40" width="200" height="130" as="geometry" />
        </mxCell>
        <mxCell id="tag-fields" value="标签信息字段&#xa;• primary_tags, secondary_tags&#xa;• tag_weights, tag_hierarchy&#xa;• auto_tags, manual_tags" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0277bd;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="dwd-wide-table" vertex="1">
          <mxGeometry x="20" y="190" width="200" height="130" as="geometry" />
        </mxCell>
        <mxCell id="analysis-fields" value="AI分析结果字段&#xa;• sentiment_score, sentiment_label&#xa;• intent_category, topic_category&#xa;• urgency_level, confidence_score" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0277bd;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="dwd-wide-table" vertex="1">
          <mxGeometry x="240" y="190" width="200" height="130" as="geometry" />
        </mxCell>
        <mxCell id="time-fields" value="时间维度字段&#xa;• date_key, year, quarter&#xa;• month, week, day_of_week&#xa;• is_holiday, season" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0277bd;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="dwd-wide-table" vertex="1">
          <mxGeometry x="460" y="190" width="200" height="130" as="geometry" />
        </mxCell>
        <mxCell id="quality-fields" value="质量标识字段&#xa;• data_quality_score&#xa;• is_valid, error_code&#xa;• process_time, etl_batch_id" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0277bd;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="dwd-wide-table" vertex="1">
          <mxGeometry x="680" y="190" width="200" height="130" as="geometry" />
        </mxCell>
        <mxCell id="index-info" value="索引和分区信息&#xa;• 复合索引：industry_code + create_time&#xa;• 业务索引：channel_id + sentiment_label&#xa;• 产品索引：product_id + topic_category&#xa;• 客户索引：customer_id + create_time&#xa;• 经销商索引：dealer_id + dealer_region&#xa;• 分区策略：按feedback_id哈希分区(32个分区)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="dwd-wide-table" vertex="1">
          <mxGeometry x="900" y="190" width="420" height="130" as="geometry" />
        </mxCell>
        <mxCell id="performance-info" value="性能优化信息&#xa;• 存储引擎：InnoDB&#xa;• 字符集：UTF8MB4&#xa;• 压缩算法：Row压缩&#xa;• 统计信息：自动更新&#xa;• 缓存策略：查询缓存 + 结果缓存&#xa;• 监控指标：查询性能 + 存储使用率" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="dwd-wide-table" vertex="1">
          <mxGeometry x="1340" y="190" width="300" height="130" as="geometry" />
        </mxCell>
        <mxCell id="lineage-info" value="数据血缘追踪&#xa;• 源系统：ODS各业务表&#xa;• 转换过程：ETL配置驱动转换&#xa;• 质量检查：多维度质量验证&#xa;• 目标表：DWD宽表&#xa;• 更新频率：实时/批量&#xa;• 监控告警：数据质量异常告警" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="dwd-wide-table" vertex="1">
          <mxGeometry x="20" y="340" width="800" height="130" as="geometry" />
        </mxCell>
        <mxCell id="usage-info" value="应用场景说明&#xa;• 实时查询：支持高并发OLTP查询&#xa;• 分析报表：提供ADS层数据源&#xa;• 数据挖掘：支持机器学习特征工程&#xa;• 业务监控：实时业务指标监控&#xa;• 数据导出：支持多格式数据导出&#xa;• API服务：提供统一数据服务接口" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f9a825;fontSize=9;verticalAlign=top;spacingTop=5;spacingLeft=5;" parent="dwd-wide-table" vertex="1">
          <mxGeometry x="840" y="340" width="800" height="130" as="geometry" />
        </mxCell>
        <mxCell id="flow1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#7b1fa2;" parent="1" source="ods-feedback" target="etl-extract" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="flow-label1" value="多源数据提取" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=10;fontColor=#7b1fa2;fontStyle=1;" parent="flow1" vertex="1" connectable="0">
          <mxGeometry x="-0.2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="flow2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#7b1fa2;" parent="1" source="ods-channel" target="etl-extract" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="flow3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#7b1fa2;" parent="1" source="ods-product" target="etl-extract" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="config-flow1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#2e7d32;" parent="1" source="config-industry" target="etl-config-loader" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1585" y="430" />
              <mxPoint x="310" y="430" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="config-label1" value="配置驱动" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=10;fontColor=#2e7d32;fontStyle=1;" parent="config-flow1" vertex="1" connectable="0">
          <mxGeometry x="-0.1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="config-flow2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2e7d32;" parent="1" source="config-field" target="etl-config-loader" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1785" y="430" />
              <mxPoint x="310" y="430" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="etl-flow1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#f57c00;" parent="1" source="etl-extract" target="temp-joined" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="etl-flow2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#f57c00;" parent="1" source="etl-config-loader" target="etl-transform" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="etl-flow3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#f57c00;" parent="1" source="temp-joined" target="etl-transform" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="etl-flow4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#f57c00;" parent="1" source="etl-transform" target="temp-standardized" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="etl-flow5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#f57c00;" parent="1" source="temp-standardized" target="etl-quality" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="etl-flow6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#f57c00;" parent="1" source="etl-quality" target="etl-builder" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="final-flow" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=4;strokeColor=#1976d2;" parent="1" source="etl-builder" target="dwd-wide-table" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="final-label" value="生成DWD宽表" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=11;fontColor=#1976d2;fontStyle=1;" parent="final-flow" vertex="1" connectable="0">
          <mxGeometry x="-0.1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bottom-description" value="ETL流程核心特性" style="text;strokeColor=none;fillColor=none;html=1;fontSize=16;fontStyle=1;verticalAlign=middle;align=center;fontColor=#2c3e50;" parent="1" vertex="1">
          <mxGeometry x="850" y="1520" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="features" value="1. 配置驱动：支持多行业灵活配置，零代码适配新业务场景&#xa;2. 质量保证：多维度数据质量检查，确保数据准确性和完整性&#xa;3. 高性能：分区表设计+索引优化，支持大规模数据处理&#xa;4. 可扩展：模块化设计，支持新数据源和字段快速接入&#xa;5. 可追溯：完整的数据血缘和处理记录，支持问题追踪&#xa;6. 实时性：支持实时和批量两种处理模式，满足不同业务需求" style="text;strokeColor=#cccccc;fillColor=#f8f9fa;html=1;fontSize=12;verticalAlign=top;align=left;whiteSpace=wrap;rounded=1;strokeWidth=1;spacing=10;" parent="1" vertex="1">
          <mxGeometry x="200" y="1560" width="1400" height="120" as="geometry" />
        </mxCell>
        <mxCell id="tech-tag1" value="配置驱动" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4caf50;strokeColor=#2e7d32;fontColor=white;fontSize=10;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="500" y="95" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="tech-tag2" value="质量保证" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ff9800;strokeColor=#e65100;fontColor=white;fontSize=10;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="580" y="95" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="tech-tag3" value="高性能" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2196f3;strokeColor=#1976d2;fontColor=white;fontSize=10;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="660" y="95" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="tech-tag4" value="可扩展" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#9c27b0;strokeColor=#7b1fa2;fontColor=white;fontSize=10;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="730" y="95" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="tech-tag5" value="数据血缘" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f44336;strokeColor=#d32f2f;fontColor=white;fontSize=10;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="800" y="95" width="70" height="20" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
