# 通用VOC报表系统配置管理设计

**文档版本**: v1.0.0  
**创建日期**: 2024年1月  
**最后更新**: 2024年1月  
**文档状态**: 设计阶段

---

## 📋 文档概述

本文档详细描述了通用VOC报表系统的配置管理架构设计，包括动态配置管理、配置热更新机制、行业配置模板、配置版本控制以及配置安全管理等核心内容。

### 配置管理原则
- **集中管理**：统一的配置管理中心，避免配置分散
- **动态更新**：支持配置的热更新，无需重启服务
- **版本控制**：完整的配置版本管理和回滚机制
- **环境隔离**：不同环境的配置完全隔离
- **安全可控**：配置变更的审批流程和安全控制

---

## 🏗️ 配置管理架构

### 1.1 整体架构设计

```mermaid
graph TB
    subgraph "配置管理层 Configuration Management"
        A1[配置控制台]
        A2[配置API]
        A3[配置验证器]
        A4[配置分发器]
    end
    
    subgraph "配置存储层 Configuration Storage"
        B1[配置数据库]
        B2[配置缓存]
        B3[版本存储]
        B4[备份存储]
    end
    
    subgraph "配置消费层 Configuration Consumer"
        C1[数据接入服务]
        C2[分析服务]
        C3[报表服务]
        C4[认证服务]
    end
    
    subgraph "配置监听层 Configuration Listener"
        D1[变更监听器]
        D2[热更新处理器]
        D3[回滚处理器]
        D4[通知服务]
    end
    
    A1 --> A2
    A2 --> A3
    A3 --> A4
    A4 --> B1
    B1 --> B2
    B2 --> D1
    D1 --> D2
    D2 --> C1
    D2 --> C2
    D2 --> C3
    D2 --> C4
```

### 1.2 配置分层模型

**配置层次结构**：
```json
{
  "configurationHierarchy": {
    "global": {
      "level": 1,
      "description": "全局通用配置",
      "scope": "all_environments",
      "examples": ["系统名称", "版本信息", "基础安全设置"],
      "inheritance": "none"
    },
    "environment": {
      "level": 2,
      "description": "环境特定配置",
      "scope": "dev|test|staging|prod",
      "examples": ["数据库连接", "外部服务地址", "日志级别"],
      "inheritance": "global"
    },
    "industry": {
      "level": 3,
      "description": "行业特定配置",
      "scope": "automotive|starbucks|petition|mobile|cosmetics",
      "examples": ["行业词典", "分析模型", "业务规则"],
      "inheritance": "environment"
    },
    "tenant": {
      "level": 4,
      "description": "租户自定义配置",
      "scope": "tenant_specific",
      "examples": ["主题配置", "个性化设置", "特殊业务规则"],
      "inheritance": "industry"
    },
    "user": {
      "level": 5,
      "description": "用户个人配置",
      "scope": "user_specific",
      "examples": ["界面设置", "报表偏好", "通知设置"],
      "inheritance": "tenant"
    }
  }
}
```

---

## ⚙️ 配置中心架构

### 2.1 配置存储设计

**配置数据模型**：
```sql
-- 配置主表
CREATE TABLE sys_configuration (
    id BIGINT PRIMARY KEY,
    config_key VARCHAR(200) NOT NULL,
    config_value TEXT NOT NULL,
    config_type VARCHAR(50) NOT NULL,
    data_type VARCHAR(20) NOT NULL DEFAULT 'STRING',
    environment VARCHAR(20) NOT NULL,
    industry_code VARCHAR(50),
    tenant_id VARCHAR(50),
    user_id VARCHAR(50),
    description TEXT,
    is_encrypted BOOLEAN DEFAULT FALSE,
    is_sensitive BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    version INT DEFAULT 1,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    
    UNIQUE KEY uk_config_key (config_key, environment, industry_code, tenant_id, user_id),
    INDEX idx_config_type (config_type),
    INDEX idx_config_env (environment),
    INDEX idx_config_industry (industry_code),
    INDEX idx_config_tenant (tenant_id),
    INDEX idx_config_status (status)
);

-- 配置版本历史表
CREATE TABLE sys_configuration_history (
    id BIGINT PRIMARY KEY,
    config_id BIGINT NOT NULL,
    config_key VARCHAR(200) NOT NULL,
    old_value TEXT,
    new_value TEXT,
    change_type VARCHAR(20) NOT NULL,
    change_reason TEXT,
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    changed_by VARCHAR(100),
    version_from INT,
    version_to INT,
    
    INDEX idx_config_id (config_id),
    INDEX idx_changed_at (changed_at),
    FOREIGN KEY (config_id) REFERENCES sys_configuration(id)
);

-- 配置模板表
CREATE TABLE sys_configuration_template (
    id BIGINT PRIMARY KEY,
    template_name VARCHAR(100) NOT NULL,
    template_type VARCHAR(50) NOT NULL,
    industry_code VARCHAR(50),
    template_content JSON NOT NULL,
    description TEXT,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_template_type (template_type),
    INDEX idx_template_industry (industry_code)
);
```

**配置服务实现**：
```java
@Service
public class ConfigurationService {
    
    @Autowired
    private ConfigurationRepository configRepository;
    
    @Autowired
    private ConfigurationCache configCache;
    
    @Autowired
    private ConfigurationPublisher configPublisher;
    
    @Autowired
    private EncryptionService encryptionService;
    
    public String getConfiguration(String key, ConfigurationContext context) {
        // 1. 构建配置查询键
        String cacheKey = buildCacheKey(key, context);
        
        // 2. 尝试从缓存获取
        String cachedValue = configCache.get(cacheKey);
        if (cachedValue != null) {
            return decryptIfNeeded(cachedValue);
        }
        
        // 3. 从数据库获取（按层次查找）
        String value = findConfigurationWithInheritance(key, context);
        
        // 4. 缓存结果
        if (value != null) {
            configCache.put(cacheKey, value);
        }
        
        return decryptIfNeeded(value);
    }
    
    private String findConfigurationWithInheritance(String key, ConfigurationContext context) {
        // 按层次查找配置：用户 -> 租户 -> 行业 -> 环境 -> 全局
        List<ConfigurationQuery> queries = buildInheritanceQueries(key, context);
        
        for (ConfigurationQuery query : queries) {
            Configuration config = configRepository.findByQuery(query);
            if (config != null) {
                return config.getValue();
            }
        }
        
        return null;
    }
    
    @Transactional
    public void updateConfiguration(String key, String value, ConfigurationContext context) {
        try {
            // 1. 验证配置
            validateConfiguration(key, value, context);
            
            // 2. 加密敏感配置
            String encryptedValue = encryptIfNeeded(key, value);
            
            // 3. 查找现有配置
            Configuration existing = configRepository.findByKeyAndContext(key, context);
            
            if (existing != null) {
                // 更新现有配置
                Configuration updated = existing.toBuilder()
                    .value(encryptedValue)
                    .version(existing.getVersion() + 1)
                    .updatedAt(Instant.now())
                    .updatedBy(getCurrentUser())
                    .build();
                
                configRepository.save(updated);
                
                // 记录变更历史
                recordConfigurationChange(existing, updated, "UPDATE");
            } else {
                // 创建新配置
                Configuration newConfig = Configuration.builder()
                    .key(key)
                    .value(encryptedValue)
                    .context(context)
                    .version(1)
                    .createdAt(Instant.now())
                    .createdBy(getCurrentUser())
                    .build();
                
                configRepository.save(newConfig);
                recordConfigurationChange(null, newConfig, "CREATE");
            }
            
            // 4. 清除缓存
            invalidateCache(key, context);
            
            // 5. 发布配置变更事件
            publishConfigurationChangeEvent(key, value, context);
            
        } catch (Exception e) {
            log.error("更新配置失败: key={}, context={}", key, context, e);
            throw new ConfigurationException("配置更新失败", e);
        }
    }
}
```

### 2.2 配置热更新机制

**配置变更监听器**：
```java
@Component
public class ConfigurationChangeListener {
    
    @Autowired
    private List<ConfigurationChangeHandler> changeHandlers;
    
    @Autowired
    private NotificationService notificationService;
    
    @EventListener
    @Async("configExecutor")
    public void handleConfigurationChangeEvent(ConfigurationChangeEvent event) {
        try {
            log.info("处理配置变更: key={}, oldValue={}, newValue={}", 
                event.getKey(), event.getOldValue(), event.getNewValue());
            
            // 1. 查找相关的处理器
            List<ConfigurationChangeHandler> applicableHandlers = changeHandlers.stream()
                .filter(handler -> handler.supports(event.getKey()))
                .collect(Collectors.toList());
            
            // 2. 并行执行配置更新
            List<CompletableFuture<Void>> futures = applicableHandlers.stream()
                .map(handler -> CompletableFuture.runAsync(() -> {
                    try {
                        handler.handle(event);
                    } catch (Exception e) {
                        log.error("配置处理器执行失败: handler={}, key={}", 
                            handler.getClass().getSimpleName(), event.getKey(), e);
                    }
                }))
                .collect(Collectors.toList());
            
            // 3. 等待所有处理器完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenRun(() -> {
                    log.info("配置变更处理完成: key={}", event.getKey());
                    notifyConfigurationUpdated(event);
                })
                .exceptionally(throwable -> {
                    log.error("配置变更处理异常: key={}", event.getKey(), throwable);
                    notifyConfigurationError(event, throwable);
                    return null;
                });
                
        } catch (Exception e) {
            log.error("配置变更事件处理失败", e);
        }
    }
}

// 数据库配置变更处理器
@Component
public class DatabaseConfigurationHandler implements ConfigurationChangeHandler {
    
    @Autowired
    private DataSource dataSource;
    
    @Override
    public boolean supports(String configKey) {
        return configKey.startsWith("datasource.");
    }
    
    @Override
    public void handle(ConfigurationChangeEvent event) {
        if (event.getKey().equals("datasource.pool.maximum-pool-size")) {
            updateConnectionPoolSize(Integer.parseInt(event.getNewValue()));
        } else if (event.getKey().equals("datasource.pool.connection-timeout")) {
            updateConnectionTimeout(Long.parseLong(event.getNewValue()));
        }
    }
    
    private void updateConnectionPoolSize(int newSize) {
        if (dataSource instanceof HikariDataSource) {
            HikariDataSource hikariDS = (HikariDataSource) dataSource;
            hikariDS.setMaximumPoolSize(newSize);
            log.info("数据库连接池大小已更新: {}", newSize);
        }
    }
}

// AI模型配置变更处理器
@Component
public class AIModelConfigurationHandler implements ConfigurationChangeHandler {
    
    @Autowired
    private AIModelManager modelManager;
    
    @Override
    public boolean supports(String configKey) {
        return configKey.startsWith("ai.model.");
    }
    
    @Override
    public void handle(ConfigurationChangeEvent event) {
        String key = event.getKey();
        String newValue = event.getNewValue();
        
        if (key.endsWith(".threshold")) {
            String modelType = extractModelType(key);
            double threshold = Double.parseDouble(newValue);
            modelManager.updateThreshold(modelType, threshold);
        } else if (key.endsWith(".enabled")) {
            String modelType = extractModelType(key);
            boolean enabled = Boolean.parseBoolean(newValue);
            if (enabled) {
                modelManager.enableModel(modelType);
            } else {
                modelManager.disableModel(modelType);
            }
        }
    }
}
```

---

## 🏭 行业配置模板

### 3.1 配置模板设计

**汽车行业配置模板**：
```json
{
  "templateName": "automotive_industry_config",
  "templateType": "industry",
  "industryCode": "automotive",
  "version": "1.0.0",
  "configurations": {
    "ai.analysis.sentiment": {
      "threshold": 0.7,
      "model": "automotive-sentiment-v1",
      "enabled": true,
      "customWeights": {
        "safety": 2.0,
        "quality": 1.5,
        "service": 1.2,
        "price": 1.0
      }
    },
    "ai.analysis.intent": {
      "threshold": 0.8,
      "model": "automotive-intent-v1",
      "enabled": true,
      "categories": [
        "complaint",
        "consultation", 
        "suggestion",
        "praise",
        "warranty_claim"
      ]
    },
    "business.rules.priority": {
      "high_priority_keywords": [
        "安全", "事故", "召回", "伤亡", "故障"
      ],
      "escalation_rules": {
        "safety_issue": {
          "condition": "sentiment=negative AND topic=safety",
          "action": "immediate_escalation",
          "notify": ["safety_manager", "quality_director"]
        }
      }
    },
    "data.validation.rules": {
      "required_fields": [
        "customer_id", "vehicle_vin", "feedback_text", "feedback_time"
      ],
      "field_formats": {
        "vehicle_vin": "^[A-HJ-NPR-Z0-9]{17}$",
        "phone": "^1[3-9]\\d{9}$"
      }
    },
    "reports.templates": {
      "default_charts": [
        "sentiment_distribution",
        "quality_issues_trend", 
        "dealer_performance",
        "product_satisfaction"
      ],
      "refresh_interval": 300,
      "cache_ttl": 1800
    }
  }
}
```

**配置模板管理服务**：
```java
@Service
public class ConfigurationTemplateService {
    
    @Autowired
    private ConfigurationTemplateRepository templateRepository;
    
    @Autowired
    private ConfigurationService configurationService;
    
    public void applyTemplate(String templateName, String industryCode, String tenantId) {
        try {
            // 1. 获取配置模板
            ConfigurationTemplate template = templateRepository.findByName(templateName);
            if (template == null) {
                throw new TemplateNotFoundException("配置模板不存在: " + templateName);
            }
            
            // 2. 解析模板内容
            Map<String, Object> templateConfig = parseTemplateContent(template.getContent());
            
            // 3. 创建配置上下文
            ConfigurationContext context = ConfigurationContext.builder()
                .environment(getCurrentEnvironment())
                .industryCode(industryCode)
                .tenantId(tenantId)
                .build();
            
            // 4. 应用配置
            applyTemplateConfigurations(templateConfig, context);
            
            // 5. 记录应用日志
            auditService.logTemplateApplication(templateName, context);
            
        } catch (Exception e) {
            log.error("应用配置模板失败: template={}, industry={}, tenant={}", 
                templateName, industryCode, tenantId, e);
            throw new TemplateApplicationException("配置模板应用失败", e);
        }
    }
    
    private void applyTemplateConfigurations(Map<String, Object> templateConfig, 
                                           ConfigurationContext context) {
        templateConfig.forEach((key, value) -> {
            try {
                String stringValue = convertToString(value);
                configurationService.updateConfiguration(key, stringValue, context);
            } catch (Exception e) {
                log.warn("应用配置项失败: key={}, value={}", key, value, e);
            }
        });
    }
    
    public ConfigurationTemplate createTemplate(String templateName, String industryCode, 
                                              Map<String, Object> configurations) {
        ConfigurationTemplate template = ConfigurationTemplate.builder()
            .name(templateName)
            .type("industry")
            .industryCode(industryCode)
            .content(objectMapper.writeValueAsString(configurations))
            .createdAt(Instant.now())
            .createdBy(getCurrentUser())
            .build();
        
        return templateRepository.save(template);
    }
}
```

### 3.2 配置继承机制

**配置继承解析器**：
```java
@Component
public class ConfigurationInheritanceResolver {
    
    @Autowired
    private ConfigurationRepository configRepository;
    
    public String resolveConfiguration(String key, ConfigurationContext context) {
        // 按优先级顺序查找配置
        List<ConfigurationLevel> levels = Arrays.asList(
            ConfigurationLevel.USER,
            ConfigurationLevel.TENANT,
            ConfigurationLevel.INDUSTRY,
            ConfigurationLevel.ENVIRONMENT,
            ConfigurationLevel.GLOBAL
        );
        
        for (ConfigurationLevel level : levels) {
            Configuration config = findConfigurationAtLevel(key, context, level);
            if (config != null) {
                return config.getValue();
            }
        }
        
        return null;
    }
    
    public Map<String, String> resolveAllConfigurations(ConfigurationContext context) {
        Map<String, String> resolvedConfigs = new HashMap<>();
        
        // 1. 从全局配置开始
        addConfigurationsFromLevel(resolvedConfigs, context, ConfigurationLevel.GLOBAL);
        
        // 2. 叠加环境配置
        addConfigurationsFromLevel(resolvedConfigs, context, ConfigurationLevel.ENVIRONMENT);
        
        // 3. 叠加行业配置
        if (context.getIndustryCode() != null) {
            addConfigurationsFromLevel(resolvedConfigs, context, ConfigurationLevel.INDUSTRY);
        }
        
        // 4. 叠加租户配置
        if (context.getTenantId() != null) {
            addConfigurationsFromLevel(resolvedConfigs, context, ConfigurationLevel.TENANT);
        }
        
        // 5. 叠加用户配置
        if (context.getUserId() != null) {
            addConfigurationsFromLevel(resolvedConfigs, context, ConfigurationLevel.USER);
        }
        
        return resolvedConfigs;
    }
    
    private void addConfigurationsFromLevel(Map<String, String> configs, 
                                          ConfigurationContext context, 
                                          ConfigurationLevel level) {
        List<Configuration> levelConfigs = configRepository.findByContextAndLevel(context, level);
        levelConfigs.forEach(config -> {
            configs.put(config.getKey(), config.getValue());
        });
    }
}
```

---

## 🔄 配置版本控制

### 4.1 版本管理策略

**配置版本模型**：
```java
@Entity
@Table(name = "sys_configuration_version")
public class ConfigurationVersion {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "config_key")
    private String configKey;
    
    @Column(name = "version_number")
    private Integer versionNumber;
    
    @Column(name = "config_value", columnDefinition = "TEXT")
    private String configValue;
    
    @Column(name = "change_type")
    @Enumerated(EnumType.STRING)
    private ChangeType changeType;
    
    @Column(name = "change_description")
    private String changeDescription;
    
    @Column(name = "created_at")
    private Instant createdAt;
    
    @Column(name = "created_by")
    private String createdBy;
    
    @Column(name = "approved_at")
    private Instant approvedAt;
    
    @Column(name = "approved_by")
    private String approvedBy;
    
    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private VersionStatus status;
    
    // getters and setters
}

public enum ChangeType {
    CREATE,
    UPDATE, 
    DELETE,
    MERGE,
    ROLLBACK
}

public enum VersionStatus {
    DRAFT,
    PENDING_APPROVAL,
    APPROVED,
    REJECTED,
    DEPLOYED,
    DEPRECATED
}
```

**版本控制服务**：
```java
@Service
public class ConfigurationVersionService {
    
    @Autowired
    private ConfigurationVersionRepository versionRepository;
    
    @Autowired
    private ApprovalWorkflowService approvalService;
    
    public ConfigurationVersion createVersion(String configKey, String newValue, 
                                            String changeDescription) {
        // 1. 获取当前版本号
        Integer currentVersion = getCurrentVersion(configKey);
        Integer newVersion = currentVersion + 1;
        
        // 2. 创建新版本
        ConfigurationVersion version = ConfigurationVersion.builder()
            .configKey(configKey)
            .versionNumber(newVersion)
            .configValue(newValue)
            .changeType(ChangeType.UPDATE)
            .changeDescription(changeDescription)
            .createdAt(Instant.now())
            .createdBy(getCurrentUser())
            .status(VersionStatus.DRAFT)
            .build();
        
        return versionRepository.save(version);
    }
    
    public void submitForApproval(Long versionId) {
        ConfigurationVersion version = versionRepository.findById(versionId)
            .orElseThrow(() -> new VersionNotFoundException("版本不存在: " + versionId));
        
        if (version.getStatus() != VersionStatus.DRAFT) {
            throw new IllegalStateException("只有草稿状态的版本可以提交审批");
        }
        
        // 1. 更新版本状态
        version.setStatus(VersionStatus.PENDING_APPROVAL);
        versionRepository.save(version);
        
        // 2. 启动审批流程
        approvalService.startApprovalProcess(version);
    }
    
    @Transactional
    public void deployVersion(Long versionId, String approvedBy) {
        ConfigurationVersion version = versionRepository.findById(versionId)
            .orElseThrow(() -> new VersionNotFoundException("版本不存在: " + versionId));
        
        if (version.getStatus() != VersionStatus.APPROVED) {
            throw new IllegalStateException("只有已审批的版本可以部署");
        }
        
        try {
            // 1. 部署配置
            ConfigurationContext context = buildContext(version);
            configurationService.updateConfiguration(
                version.getConfigKey(), 
                version.getConfigValue(), 
                context
            );
            
            // 2. 更新版本状态
            version.setStatus(VersionStatus.DEPLOYED);
            version.setApprovedAt(Instant.now());
            version.setApprovedBy(approvedBy);
            versionRepository.save(version);
            
            // 3. 废弃旧版本
            deprecateOldVersions(version.getConfigKey(), version.getVersionNumber());
            
        } catch (Exception e) {
            log.error("部署配置版本失败: versionId={}", versionId, e);
            throw new VersionDeploymentException("配置版本部署失败", e);
        }
    }
    
    @Transactional
    public void rollbackToVersion(String configKey, Integer targetVersion) {
        // 1. 查找目标版本
        ConfigurationVersion targetVersionEntity = versionRepository
            .findByConfigKeyAndVersionNumber(configKey, targetVersion)
            .orElseThrow(() -> new VersionNotFoundException(
                "目标版本不存在: " + configKey + "@" + targetVersion));
        
        // 2. 创建回滚版本
        Integer newVersion = getCurrentVersion(configKey) + 1;
        ConfigurationVersion rollbackVersion = ConfigurationVersion.builder()
            .configKey(configKey)
            .versionNumber(newVersion)
            .configValue(targetVersionEntity.getConfigValue())
            .changeType(ChangeType.ROLLBACK)
            .changeDescription("回滚到版本 " + targetVersion)
            .createdAt(Instant.now())
            .createdBy(getCurrentUser())
            .status(VersionStatus.APPROVED)
            .approvedAt(Instant.now())
            .approvedBy(getCurrentUser())
            .build();
        
        versionRepository.save(rollbackVersion);
        
        // 3. 部署回滚版本
        deployVersion(rollbackVersion.getId(), getCurrentUser());
    }
}
```

### 4.2 配置审批流程

**审批工作流**：
```java
@Service
public class ConfigurationApprovalService {
    
    @Autowired
    private ApprovalRuleEngine approvalRuleEngine;
    
    @Autowired
    private NotificationService notificationService;
    
    public void startApprovalProcess(ConfigurationVersion version) {
        // 1. 确定审批规则
        ApprovalRule rule = approvalRuleEngine.determineApprovalRule(version);
        
        // 2. 创建审批实例
        ApprovalInstance instance = ApprovalInstance.builder()
            .versionId(version.getId())
            .approvalRule(rule)
            .currentStep(0)
            .status(ApprovalStatus.IN_PROGRESS)
            .startedAt(Instant.now())
            .startedBy(version.getCreatedBy())
            .build();
        
        approvalInstanceRepository.save(instance);
        
        // 3. 通知第一层审批人
        notifyApprovers(instance, rule.getSteps().get(0));
    }
    
    public void approve(Long instanceId, String approver, String comment) {
        ApprovalInstance instance = getApprovalInstance(instanceId);
        ApprovalRule rule = instance.getApprovalRule();
        
        // 1. 记录审批结果
        ApprovalStep currentStep = rule.getSteps().get(instance.getCurrentStep());
        recordApprovalResult(instance, currentStep, approver, ApprovalResult.APPROVED, comment);
        
        // 2. 检查是否还有下一步
        if (instance.getCurrentStep() < rule.getSteps().size() - 1) {
            // 进入下一审批步骤
            instance.setCurrentStep(instance.getCurrentStep() + 1);
            approvalInstanceRepository.save(instance);
            
            ApprovalStep nextStep = rule.getSteps().get(instance.getCurrentStep());
            notifyApprovers(instance, nextStep);
        } else {
            // 审批完成
            completeApproval(instance);
        }
    }
    
    private void completeApproval(ApprovalInstance instance) {
        // 1. 更新审批实例状态
        instance.setStatus(ApprovalStatus.APPROVED);
        instance.setCompletedAt(Instant.now());
        approvalInstanceRepository.save(instance);
        
        // 2. 更新配置版本状态
        ConfigurationVersion version = versionRepository.findById(instance.getVersionId())
            .orElseThrow(() -> new VersionNotFoundException("版本不存在"));
        
        version.setStatus(VersionStatus.APPROVED);
        versionRepository.save(version);
        
        // 3. 通知配置提交者
        notificationService.notifyApprovalCompleted(version);
    }
}
```

---

## 🔐 配置安全管理

### 5.1 敏感配置加密

**配置加密服务**：
```java
@Service
public class ConfigurationEncryptionService {
    
    private static final String ENCRYPTION_PREFIX = "{cipher}";
    private static final Set<String> SENSITIVE_PATTERNS = Set.of(
        "password", "secret", "key", "token", "credential"
    );
    
    @Autowired
    private AESEncryptionService aesEncryptionService;
    
    public String encryptSensitiveConfiguration(String key, String value) {
        if (isSensitiveConfiguration(key)) {
            String encrypted = aesEncryptionService.encrypt(value);
            return ENCRYPTION_PREFIX + encrypted;
        }
        return value;
    }
    
    public String decryptConfiguration(String encryptedValue) {
        if (encryptedValue != null && encryptedValue.startsWith(ENCRYPTION_PREFIX)) {
            String cipherText = encryptedValue.substring(ENCRYPTION_PREFIX.length());
            return aesEncryptionService.decrypt(cipherText);
        }
        return encryptedValue;
    }
    
    private boolean isSensitiveConfiguration(String key) {
        String lowerKey = key.toLowerCase();
        return SENSITIVE_PATTERNS.stream()
            .anyMatch(lowerKey::contains);
    }
    
    @EventListener
    public void handleConfigurationAccessEvent(ConfigurationAccessEvent event) {
        if (isSensitiveConfiguration(event.getConfigKey())) {
            // 记录敏感配置访问日志
            auditService.logSensitiveConfigurationAccess(
                event.getConfigKey(),
                event.getAccessor(),
                event.getAccessTime(),
                event.getSourceIp()
            );
        }
    }
}
```

### 5.2 配置访问控制

**配置权限管理**：
```java
@Component
public class ConfigurationAccessControl {
    
    @Autowired
    private UserService userService;
    
    public boolean hasConfigurationAccess(String userId, String configKey, ConfigurationOperation operation) {
        User user = userService.findById(userId);
        if (user == null) {
            return false;
        }
        
        // 1. 检查全局权限
        if (hasGlobalConfigurationPermission(user, operation)) {
            return true;
        }
        
        // 2. 检查配置类型权限
        ConfigurationType configType = determineConfigurationType(configKey);
        if (hasConfigurationTypePermission(user, configType, operation)) {
            return true;
        }
        
        // 3. 检查特定配置权限
        return hasSpecificConfigurationPermission(user, configKey, operation);
    }
    
    private boolean hasGlobalConfigurationPermission(User user, ConfigurationOperation operation) {
        return user.hasRole("SYSTEM_ADMIN") || 
               (operation == ConfigurationOperation.READ && user.hasRole("CONFIG_VIEWER"));
    }
    
    private boolean hasConfigurationTypePermission(User user, ConfigurationType configType, 
                                                 ConfigurationOperation operation) {
        switch (configType) {
            case AI_MODEL:
                return user.hasPermission("ai.model." + operation.name().toLowerCase());
            case BUSINESS_RULE:
                return user.hasPermission("business.rule." + operation.name().toLowerCase());
            case SYSTEM_SETTING:
                return user.hasPermission("system.setting." + operation.name().toLowerCase());
            default:
                return false;
        }
    }
}

public enum ConfigurationOperation {
    READ,
    WRITE,
    DELETE,
    APPROVE
}
```

---

**文档维护**: 配置管理员、系统架构师  
**审核**: 技术负责人、安全专家  
**下次更新**: 配置管理策略变更时及时更新 