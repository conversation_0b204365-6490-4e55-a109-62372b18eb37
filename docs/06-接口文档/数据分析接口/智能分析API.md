# 智能分析API

## 基本信息
- **接口描述**：基于AI模型对客户反馈数据进行智能分析，包括情感分析、意图识别、主题分类等
- **请求方式**：POST
- **接口地址**：/api/v1/analysis/smart
- **权限要求**：数据分析权限

## 请求参数

### Header
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 是 | Bearer Token |
| Content-Type | string | 是 | application/json |

### Body
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| industryCode | string | 是 | 行业代码 |
| content | string | 是 | 待分析的内容 |
| contentType | string | 否 | 内容类型 |
| metadata | object | 否 | 元数据信息 |
| &nbsp;&nbsp;channel | string | 否 | 渠道信息 |
| &nbsp;&nbsp;brand | string | 否 | 品牌信息 |
| &nbsp;&nbsp;product | string | 否 | 产品信息 |
| &nbsp;&nbsp;customerId | string | 否 | 客户ID |

## 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | integer | 响应码 |
| message | string | 响应消息 |
| data | object | 分析结果 |
| &nbsp;&nbsp;id | string | 数据唯一标识 |
| &nbsp;&nbsp;industryCode | string | 行业代码 |
| &nbsp;&nbsp;content | string | 原始内容 |
| &nbsp;&nbsp;sentiment | object | 情感分析结果 |
| &nbsp;&nbsp;&nbsp;&nbsp;label | string | 情感标签 |
| &nbsp;&nbsp;&nbsp;&nbsp;score | number | 置信度分数 |
| &nbsp;&nbsp;&nbsp;&nbsp;positiveScore | number | 正面情感分数 |
| &nbsp;&nbsp;&nbsp;&nbsp;negativeScore | number | 负面情感分数 |
| &nbsp;&nbsp;&nbsp;&nbsp;neutralScore | number | 中性情感分数 |
| &nbsp;&nbsp;intention | object | 意图识别结果 |
| &nbsp;&nbsp;&nbsp;&nbsp;label | string | 意图标签 |
| &nbsp;&nbsp;&nbsp;&nbsp;score | number | 置信度分数 |
| &nbsp;&nbsp;topic | object | 主题分类结果 |
| &nbsp;&nbsp;&nbsp;&nbsp;label | string | 主题标签 |
| &nbsp;&nbsp;&nbsp;&nbsp;score | number | 置信度分数 |
| &nbsp;&nbsp;keywords | array | 关键词 |
| &nbsp;&nbsp;urgencyLevel | string | 紧急程度 |
| &nbsp;&nbsp;urgencyScore | number | 紧急程度置信度分数 |
| &nbsp;&nbsp;emotionType | string | 情绪类型 |
| &nbsp;&nbsp;emotionScore | number | 情绪识别置信度分数 |
| &nbsp;&nbsp;satisfactionScore | number | 满意度评分 |
| &nbsp;&nbsp;npsScore | integer | NPS净推荐值 |
| &nbsp;&nbsp;aspectTerms | array | 属性词 |
| &nbsp;&nbsp;opinionTerms | array | 观点词 |
| &nbsp;&nbsp;aspectCategories | array | 属性分类 |
| &nbsp;&nbsp;qualityIssueFlag | boolean | 质量问题标记 |
| &nbsp;&nbsp;serviceIssueFlag | boolean | 服务问题标记 |
| &nbsp;&nbsp;priceIssueFlag | boolean | 价格问题标记 |
| &nbsp;&nbsp;deliveryIssueFlag | boolean | 交付问题标记 |
| &nbsp;&nbsp;promotionIssueFlag | boolean | 推广问题标记 |
| &nbsp;&nbsp;contentSummary | string | 内容摘要 |
| &nbsp;&nbsp;contentLength | integer | 内容长度 |
| &nbsp;&nbsp;languageType | string | 语言类型 |
| &nbsp;&nbsp;translationFlag | boolean | 是否需要翻译 |
| &nbsp;&nbsp;spamFlag | boolean | 垃圾信息标记 |
| &nbsp;&nbsp;duplicateFlag | boolean | 重复内容标记 |
| &nbsp;&nbsp;aiAnalysisTime | string | AI分析时间 |
| &nbsp;&nbsp;aiModelVersion | string | AI模型版本 |
| timestamp | string | 响应时间戳 |
| requestId | string | 请求ID |

## 示例

### 请求示例
```json
{
  "industryCode": "automotive",
  "content": "我对这辆车的性能非常满意，特别是油耗表现，但售后服务体验有待提升。",
  "contentType": "feedback",
  "metadata": {
    "channel": "官网",
    "brand": "大众",
    "product": "朗逸",
    "customerId": "CUST001234"
  }
}
```

### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "ANA202401010001",
    "industryCode": "automotive",
    "content": "我对这辆车的性能非常满意，特别是油耗表现，但售后服务体验有待提升。",
    "sentiment": {
      "label": "正面",
      "score": 0.78,
      "positiveScore": 0.78,
      "negativeScore": 0.15,
      "neutralScore": 0.07
    },
    "intention": {
      "label": "产品咨询",
      "score": 0.85
    },
    "topic": {
      "label": "产品性能",
      "score": 0.92
    },
    "keywords": ["性能", "油耗", "售后服务"],
    "urgencyLevel": "普通",
    "urgencyScore": 0.3,
    "emotionType": "满意",
    "emotionScore": 0.75,
    "satisfactionScore": 0.7,
    "npsScore": 8,
    "aspectTerms": ["性能", "油耗", "售后服务"],
    "opinionTerms": ["满意", "有待提升"],
    "aspectCategories": ["产品性能", "售后服务"],
    "qualityIssueFlag": false,
    "serviceIssueFlag": true,
    "priceIssueFlag": false,
    "deliveryIssueFlag": false,
    "promotionIssueFlag": false,
    "contentSummary": "客户对车辆性能满意但对售后服务不满",
    "contentLength": 36,
    "languageType": "zh-CN",
    "translationFlag": false,
    "spamFlag": false,
    "duplicateFlag": false,
    "aiAnalysisTime": "2024-01-01T12:00:00Z",
    "aiModelVersion": "v2.1.0"
  },
  "timestamp": "2024-01-01T12:00:05Z",
  "requestId": "req-anl-2024010112000001"
}
```

## 错误码

| 错误码 | 说明 |
|--------|------|
| 200 | 分析成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |
| 503 | 服务暂时不可用 |