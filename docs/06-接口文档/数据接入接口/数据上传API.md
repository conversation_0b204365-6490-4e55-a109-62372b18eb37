# 数据上传API

## 基本信息
- **接口描述**：上传客户反馈原始数据到系统中，支持单条和批量上传
- **请求方式**：POST
- **接口地址**：/api/v1/data/upload
- **权限要求**：数据上传权限

## 请求参数

### Header
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 是 | Bearer Token |
| Content-Type | string | 是 | application/json |

### Body
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| industryCode | string | 是 | 行业代码 |
| dataType | string | 是 | 数据类型(raw_feedback, raw_private_work_order等) |
| data | array | 是 | 数据列表 |
| &nbsp;&nbsp;id | string | 否 | 数据唯一标识 |
| &nbsp;&nbsp;content | string | 是 | 反馈内容 |
| &nbsp;&nbsp;channel | object | 否 | 渠道信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;category | string | 否 | 渠道分类 |
| &nbsp;&nbsp;&nbsp;&nbsp;code | string | 否 | 渠道代码 |
| &nbsp;&nbsp;&nbsp;&nbsp;name | string | 否 | 渠道名称 |
| &nbsp;&nbsp;customer | object | 否 | 客户信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;oneId | string | 否 | 统一客户ID |
| &nbsp;&nbsp;&nbsp;&nbsp;name | string | 否 | 客户姓名 |
| &nbsp;&nbsp;&nbsp;&nbsp;nick | string | 否 | 客户昵称 |
| &nbsp;&nbsp;&nbsp;&nbsp;age | string | 否 | 客户年龄 |
| &nbsp;&nbsp;&nbsp;&nbsp;gender | string | 否 | 客户性别 |
| &nbsp;&nbsp;&nbsp;&nbsp;province | string | 否 | 客户省份 |
| &nbsp;&nbsp;&nbsp;&nbsp;city | string | 否 | 客户城市 |
| &nbsp;&nbsp;&nbsp;&nbsp;mobile | string | 否 | 客户手机号 |
| &nbsp;&nbsp;&nbsp;&nbsp;email | string | 否 | 客户邮箱 |
| &nbsp;&nbsp;&nbsp;&nbsp;type | string | 否 | 客户类型 |
| &nbsp;&nbsp;brand | object | 否 | 品牌信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;code | string | 否 | 品牌代码 |
| &nbsp;&nbsp;&nbsp;&nbsp;name | string | 否 | 品牌名称 |
| &nbsp;&nbsp;product | object | 否 | 产品信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;code | string | 否 | 产品代码 |
| &nbsp;&nbsp;&nbsp;&nbsp;name | string | 否 | 产品名称 |
| &nbsp;&nbsp;dealer | object | 否 | 经销商信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;id | string | 否 | 经销商ID |
| &nbsp;&nbsp;&nbsp;&nbsp;code | string | 否 | 经销商代码 |
| &nbsp;&nbsp;&nbsp;&nbsp;name | string | 否 | 经销商名称 |
| &nbsp;&nbsp;&nbsp;&nbsp;province | string | 否 | 省份 |
| &nbsp;&nbsp;&nbsp;&nbsp;city | string | 否 | 城市 |
| &nbsp;&nbsp;socialMedia | object | 否 | 社交媒体信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;platform | string | 否 | 社交平台 |
| &nbsp;&nbsp;&nbsp;&nbsp;authorId | string | 否 | 作者ID |
| &nbsp;&nbsp;&nbsp;&nbsp;authorNick | string | 否 | 作者昵称 |
| &nbsp;&nbsp;&nbsp;&nbsp;viewCount | integer | 否 | 浏览数 |
| &nbsp;&nbsp;&nbsp;&nbsp;commentCount | integer | 否 | 评论数 |
| &nbsp;&nbsp;&nbsp;&nbsp;likeCount | integer | 否 | 点赞数 |
| &nbsp;&nbsp;workOrder | object | 否 | 工单信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;id | string | 否 | 工单ID |
| &nbsp;&nbsp;&nbsp;&nbsp;type | string | 否 | 问题类型 |
| &nbsp;&nbsp;&nbsp;&nbsp;businessType | string | 否 | 业务类型 |
| &nbsp;&nbsp;&nbsp;&nbsp;status | string | 否 | 工单状态 |
| &nbsp;&nbsp;&nbsp;&nbsp;priority | string | 否 | 工单优先级 |
| &nbsp;&nbsp;&nbsp;&nbsp;createTime | string | 否 | 工单创建时间 |
| &nbsp;&nbsp;&nbsp;&nbsp;resolveTime | string | 否 | 工单解决时间 |
| &nbsp;&nbsp;metadata | object | 否 | 元数据信息 |
| &nbsp;&nbsp;&nbsp;&nbsp;createDate | string | 否 | 创建日期 |
| &nbsp;&nbsp;&nbsp;&nbsp;contentType | string | 否 | 内容类型 |

## 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | integer | 响应码 |
| message | string | 响应消息 |
| data | object | 上传结果 |
| &nbsp;&nbsp;uploadedCount | integer | 成功上传记录数 |
| &nbsp;&nbsp;failedCount | integer | 上传失败记录数 |
| &nbsp;&nbsp;failedRecords | array | 失败记录详情 |
| &nbsp;&nbsp;&nbsp;&nbsp;index | integer | 失败记录索引 |
| &nbsp;&nbsp;&nbsp;&nbsp;reason | string | 失败原因 |
| &nbsp;&nbsp;taskId | string | 上传任务ID |
| timestamp | string | 响应时间戳 |
| requestId | string | 请求ID |

## 示例

### 请求示例
```json
{
  "industryCode": "automotive",
  "dataType": "raw_feedback",
  "data": [
    {
      "id": "FB202401010001",
      "content": "我对这辆车的性能非常满意，特别是油耗表现，但售后服务体验有待提升。",
      "channel": {
        "category": "官方网站",
        "name": "官网留言"
      },
      "customer": {
        "oneId": "CUST001234",
        "name": "张三",
        "gender": "男",
        "province": "广东",
        "city": "深圳",
        "type": "车主"
      },
      "brand": {
        "code": "VW",
        "name": "大众"
      },
      "product": {
        "code": "LAVIDA",
        "name": "朗逸"
      },
      "dealer": {
        "name": "深圳大众4S店",
        "province": "广东",
        "city": "深圳"
      },
      "metadata": {
        "createDate": "2024-01-01",
        "contentType": "feedback"
      }
    }
  ]
}
```

### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "uploadedCount": 1,
    "failedCount": 0,
    "failedRecords": [],
    "taskId": "TASK20240101120001"
  },
  "timestamp": "2024-01-01T12:00:05Z",
  "requestId": "req-upl-2024010112000001"
}
```

## 错误码

| 错误码 | 说明 |
|--------|------|
| 200 | 上传成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 413 | 请求数据过大 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |
| 503 | 服务暂时不可用 |