# 数据源管理API

## 基本信息
- **接口描述**：管理系统中各种客户反馈数据源的配置和状态
- **请求方式**：GET/POST/PUT/DELETE
- **接口地址**：/api/v1/data-sources
- **权限要求**：数据源管理权限

## 功能列表

### 1. 获取数据源列表
- **请求方式**：GET
- **接口地址**：/api/v1/data-sources
- **功能说明**：获取所有数据源配置列表

### 2. 获取数据源详情
- **请求方式**：GET
- **接口地址**：/api/v1/data-sources/{sourceId}
- **功能说明**：获取特定数据源的详细配置信息

### 3. 创建数据源
- **请求方式**：POST
- **接口地址**：/api/v1/data-sources
- **功能说明**：创建新的数据源配置

### 4. 更新数据源
- **请求方式**：PUT
- **接口地址**：/api/v1/data-sources/{sourceId}
- **功能说明**：更新现有数据源配置

### 5. 删除数据源
- **请求方式**：DELETE
- **接口地址**：/api/v1/data-sources/{sourceId}
- **功能说明**：删除数据源配置

### 6. 测试数据源连接
- **请求方式**：POST
- **接口地址**：/api/v1/data-sources/{sourceId}/test
- **功能说明**：测试数据源连接是否正常

### 7. 启用/禁用数据源
- **请求方式**：PUT
- **接口地址**：/api/v1/data-sources/{sourceId}/status
- **功能说明**：启用或禁用数据源

## 请求参数

### Header
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 是 | Bearer Token |
| Content-Type | string | 是 | application/json |

### 查询参数（GET /api/v1/data-sources）
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | integer | 否 | 页码 |
| size | integer | 否 | 每页记录数 |
| keyword | string | 否 | 搜索关键字 |
| industryCode | string | 否 | 行业代码筛选 |
| status | string | 否 | 状态筛选(ENABLED/DISABLED) |
| type | string | 否 | 数据源类型筛选 |

### 路径参数（GET/PUT/DELETE /api/v1/data-sources/{sourceId}）
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sourceId | string | 是 | 数据源ID |

### Body参数（POST/PUT）
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sourceName | string | 是 | 数据源名称 |
| sourceType | string | 是 | 数据源类型(DATABASE/API/FILE/STREAM) |
| industryCode | string | 是 | 行业代码 |
| description | string | 否 | 数据源描述 |
| connectionConfig | object | 是 | 连接配置 |
| &nbsp;&nbsp;host | string | 否 | 主机地址 |
| &nbsp;&nbsp;port | integer | 否 | 端口号 |
| &nbsp;&nbsp;database | string | 否 | 数据库名 |
| &nbsp;&nbsp;username | string | 否 | 用户名 |
| &nbsp;&nbsp;password | string | 否 | 密码(加密存储) |
| &nbsp;&nbsp;url | string | 否 | API地址 |
| &nbsp;&nbsp;apiKey | string | 否 | API密钥 |
| &nbsp;&nbsp;filePath | string | 否 | 文件路径 |
| &nbsp;&nbsp;otherProperties | object | 否 | 其他属性 |
| scheduleConfig | object | 否 | 调度配置 |
| &nbsp;&nbsp;enabled | boolean | 否 | 是否启用调度 |
| &nbsp;&nbsp;cronExpression | string | 否 | Cron表达式 |
| &nbsp;&nbsp;frequency | string | 否 | 频率(DAILY/HOURLY/REALTIME等) |
| dataMapping | object | 否 | 数据字段映射配置 |
| status | string | 否 | 状态(ENABLED/DISABLED) |

### Body参数（PUT /api/v1/data-sources/{sourceId}/status）
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | string | 是 | 目标状态(ENABLED/DISABLED) |

## 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | integer | 响应码 |
| message | string | 响应消息 |
| data | object | 数据源配置信息 |
| &nbsp;&nbsp;sourceId | string | 数据源ID |
| &nbsp;&nbsp;sourceName | string | 数据源名称 |
| &nbsp;&nbsp;sourceType | string | 数据源类型 |
| &nbsp;&nbsp;industryCode | string | 行业代码 |
| &nbsp;&nbsp;description | string | 数据源描述 |
| &nbsp;&nbsp;connectionConfig | object | 连接配置摘要(不包含敏感信息) |
| &nbsp;&nbsp;scheduleConfig | object | 调度配置 |
| &nbsp;&nbsp;dataMapping | object | 数据字段映射配置摘要 |
| &nbsp;&nbsp;status | string | 状态 |
| &nbsp;&nbsp;createTime | string | 创建时间 |
| &nbsp;&nbsp;updateTime | string | 更新时间 |
| &nbsp;&nbsp;lastTestTime | string | 最后测试时间 |
| &nbsp;&nbsp;lastTestResult | string | 最后测试结果(SUCCESS/FAILED) |
| timestamp | string | 响应时间戳 |
| requestId | string | 请求ID |

## 示例

### 1. 获取数据源列表请求示例
```http
GET /api/v1/data-sources?page=1&size=10&industryCode=automotive HTTP/1.1
Authorization: Bearer xxx
```

### 1. 获取数据源列表响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "sourceId": "DS202401010001",
      "sourceName": "官网客户反馈数据库",
      "sourceType": "DATABASE",
      "industryCode": "automotive",
      "description": "官网客户反馈数据源",
      "connectionConfig": {
        "host": "*************",
        "port": 3306,
        "database": "feedback_db"
      },
      "scheduleConfig": {
        "enabled": true,
        "cronExpression": "0 0/30 * * * ?",
        "frequency": "REALTIME"
      },
      "status": "ENABLED",
      "createTime": "2024-01-01T00:00:00Z",
      "updateTime": "2024-01-01T00:00:00Z",
      "lastTestTime": "2024-01-01T12:00:00Z",
      "lastTestResult": "SUCCESS"
    }
  ],
  "timestamp": "2024-01-01T12:00:05Z",
  "requestId": "req-ds-2024010112000001"
}
```

### 2. 创建数据源请求示例
```http
POST /api/v1/data-sources HTTP/1.1
Authorization: Bearer xxx
Content-Type: application/json

{
  "sourceName": "社交媒体API数据源",
  "sourceType": "API",
  "industryCode": "automotive",
  "description": "从社交媒体平台获取客户反馈",
  "connectionConfig": {
    "url": "https://api.socialmedia.com/v1/feedback",
    "apiKey": "encrypted_api_key"
  },
  "scheduleConfig": {
    "enabled": true,
    "cronExpression": "0 0 9 * * ?",
    "frequency": "DAILY"
  },
  "status": "ENABLED"
}
```

### 2. 创建数据源响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "sourceId": "DS202401010002",
    "sourceName": "社交媒体API数据源",
    "sourceType": "API",
    "industryCode": "automotive",
    "description": "从社交媒体平台获取客户反馈",
    "connectionConfig": {
      "url": "https://api.socialmedia.com/v1/feedback"
    },
    "scheduleConfig": {
      "enabled": true,
      "cronExpression": "0 0 9 * * ?",
      "frequency": "DAILY"
    },
    "status": "ENABLED",
    "createTime": "2024-01-01T12:00:05Z",
    "updateTime": "2024-01-01T12:00:05Z",
    "lastTestTime": null,
    "lastTestResult": null
  },
  "timestamp": "2024-01-01T12:00:05Z",
  "requestId": "req-ds-2024010112000002"
}
```

### 3. 测试数据源连接请求示例
```http
POST /api/v1/data-sources/DS202401010001/test HTTP/1.1
Authorization: Bearer xxx
```

### 3. 测试数据源连接响应示例
```json
{
  "code": 200,
  "message": "数据源连接测试成功",
  "data": {
    "sourceId": "DS202401010001",
    "testResult": "SUCCESS",
    "testMessage": "连接成功，可正常访问数据",
    "testTime": "2024-01-01T12:05:00Z"
  },
  "timestamp": "2024-01-01T12:05:00Z",
  "requestId": "req-ds-2024010112050001"
}
```

## 错误码

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 数据源不存在 |
| 409 | 数据源名称已存在 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |
| 503 | 服务暂时不可用 |
| 30101 | 数据源不存在 |
| 30102 | 数据源已存在 |
| 30103 | 数据源配置无效 |