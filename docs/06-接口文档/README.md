# 04-接口文档目录

## 📋 目录说明

本目录包含通用VOC报表系统的所有API接口文档。

## 📁 文档结构

```
04-接口文档/
├── README.md                    # 本文档
├── API设计规范.md               # API设计标准和规范
├── 接口总览.md                  # 所有接口的概览
├── 错误码定义.md                # 统一错误码定义
├── 鉴权机制.md                  # API鉴权和安全
├── 数据接入接口/                # 数据接入相关接口
│   ├── 数据源管理API.md
│   ├── 数据上传API.md
│   ├── 数据验证API.md
│   └── 批量处理API.md
├── 配置管理接口/                # 配置管理相关接口
│   ├── 行业配置API.md
│   ├── 字典管理API.md
│   ├── 规则引擎API.md
│   ├── 阈值配置API.md
│   └── 模板管理API.md
├── 数据分析接口/                # 数据分析相关接口
│   ├── 智能分析API.md
│   ├── 情感分析API.md
│   ├── 意图识别API.md
│   ├── 主题分类API.md
│   └── 批量分析API.md
├── 报表服务接口/                # 报表服务相关接口
│   ├── 报表生成API.md
│   ├── 数据查询API.md
│   ├── 报表导出API.md
│   ├── 可视化API.md
│   └── 实时统计API.md
├── 用户管理接口/                # 用户管理相关接口
│   ├── 用户认证API.md
│   ├── 权限管理API.md
│   ├── 组织架构API.md
│   └── 审计日志API.md
└── 系统管理接口/                # 系统管理相关接口
    ├── 监控告警API.md
    ├── 系统配置API.md
    ├── 健康检查API.md
    └── 版本管理API.md
```

## 🌐 API设计原则

### 1. RESTful设计
```http
GET    /api/v1/industries          # 获取行业列表
POST   /api/v1/industries          # 创建新行业配置
GET    /api/v1/industries/{id}     # 获取特定行业配置
PUT    /api/v1/industries/{id}     # 更新行业配置
DELETE /api/v1/industries/{id}     # 删除行业配置
```

### 2. 统一响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 响应数据
  },
  "timestamp": "2024-01-01T12:00:00Z",
  "requestId": "req-123456789"
}
```

### 3. 错误处理
```json
{
  "code": 400,
  "message": "参数验证失败",
  "error": {
    "type": "VALIDATION_ERROR",
    "details": [
      {
        "field": "industryName",
        "message": "行业名称不能为空"
      }
    ]
  },
  "timestamp": "2024-01-01T12:00:00Z",
  "requestId": "req-123456789"
}
```

## 🔐 安全认证

### JWT Token认证
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### API密钥认证
```http
X-API-Key: your-api-key
X-API-Secret: your-api-secret
```

### OAuth 2.0
```http
Authorization: Bearer oauth-token
```

## 📊 核心接口模块

### 1. 数据接入模块
- **数据源管理**：配置各种数据源
- **数据上传**：支持文件和API上传
- **数据验证**：数据格式和质量验证
- **批量处理**：大批量数据处理

### 2. 配置管理模块
- **行业配置**：动态行业配置管理
- **字典管理**：词汇库和术语管理
- **规则引擎**：业务规则配置
- **模板管理**：配置模板管理

### 3. 智能分析模块
- **AI分析**：大模型智能分析
- **情感分析**：文本情感识别
- **意图识别**：用户意图分类
- **主题分类**：内容主题归类

### 4. 报表服务模块
- **报表生成**：动态报表生成
- **数据查询**：灵活的数据查询
- **可视化**：图表和可视化组件
- **导出服务**：多格式数据导出

## 🚀 性能要求

- **响应时间**：平均 < 200ms
- **并发处理**：支持1000+并发请求
- **吞吐量**：10000+ requests/minute
- **可用性**：99.9%接口可用性

## 📝 接口文档规范

### 1. 文档结构
```markdown
# 接口名称

## 基本信息
- **接口描述**：接口功能说明
- **请求方式**：GET/POST/PUT/DELETE
- **接口地址**：/api/v1/xxx
- **权限要求**：需要的权限级别

## 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|

## 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|

## 示例
### 请求示例
### 响应示例

## 错误码
| 错误码 | 说明 |
|--------|------|
```

### 2. 版本管理
- **语义化版本**：v1.0.0、v1.1.0、v2.0.0
- **向后兼容**：保持接口向后兼容性
- **废弃通知**：提前通知接口废弃

## 🔗 相关文档

- [系统设计](../04-系统设计/) - 系统架构设计
- [技术方案](../05-技术方案/) - 技术实现方案
- [数据库设计](../07-数据库设计/) - 数据库设计

## 📅 维护信息

- **创建日期**：2024年1月
- **维护人员**：后端开发团队、API架构师
- **审核人员**：技术负责人、产品经理
- **更新周期**：接口变更时及时更新 