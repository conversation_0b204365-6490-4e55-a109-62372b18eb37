# API设计规范

## 1. 概述

本文档定义了通用VOC报表系统API的设计规范和标准，所有API接口都应遵循此规范进行设计和实现。

## 2. 设计原则

### 2.1 RESTful设计原则
- 使用标准HTTP方法（GET、POST、PUT、DELETE等）
- 资源URI使用名词复数形式
- URI统一小写，单词间使用连字符分隔
- 版本号通过URL路径控制（如/v1/）

### 2.2 一致性原则
- 统一的请求/响应格式
- 统一的错误处理机制
- 统一的命名规范
- 统一的状态码使用

### 2.3 安全性原则
- 所有API必须进行身份验证
- 敏感数据传输必须加密
- 输入参数必须进行验证和过滤
- 实现访问控制和权限管理

## 3. 命名规范

### 3.1 URI命名规范
- 使用小写字母
- 单词之间使用连字符(-)分隔
- 使用复数形式表示资源集合
- 避免使用下划线(_)和特殊字符

示例：
```
/api/v1/industries
/api/v1/data-sources
/api/v1/report-configs
```

### 3.2 参数命名规范
- 请求参数使用驼峰命名法（camelCase）
- 响应字段使用驼峰命名法（camelCase）
- 常量使用大写字母和下划线（CONSTANT_NAME）

### 3.3 枚举值命名规范
- 使用大写字母和下划线分隔
- 语义清晰且唯一

示例：
```
ENABLED, DISABLED, PENDING
SUCCESS, FAILED, PROCESSING
```

## 4. 请求规范

### 4.1 HTTP方法使用规范
| 方法 | 用途 | 幂等性 | 安全性 |
|------|------|--------|--------|
| GET | 获取资源 | 是 | 是 |
| POST | 创建资源 | 否 | 否 |
| PUT | 更新资源（全量） | 是 | 否 |
| PATCH | 更新资源（部分） | 否 | 否 |
| DELETE | 删除资源 | 是 | 否 |

### 4.2 请求头规范
```
Authorization: Bearer {token}
Content-Type: application/json
Accept: application/json
X-Request-ID: {request_id}
X-Client-Version: {version}
```

### 4.3 请求体规范
- 使用JSON格式传输数据
- 字段值不能为空字符串，应使用null或省略该字段
- 日期时间格式：ISO 8601标准（YYYY-MM-DDTHH:mm:ssZ）

## 5. 响应规范

### 5.1 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T12:00:00Z",
  "requestId": "req-123456789"
}
```

### 5.2 响应字段说明
| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| code | integer | 是 | 响应状态码 |
| message | string | 是 | 响应消息 |
| data | object/array | 否 | 响应数据 |
| timestamp | string | 是 | 响应时间戳 |
| requestId | string | 是 | 请求ID |

### 5.3 分页响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [],
    "totalCount": 100,
    "pageSize": 10,
    "currentPage": 1
  },
  "timestamp": "2024-01-01T12:00:00Z",
  "requestId": "req-123456789"
}
```

## 6. 状态码规范

### 6.1 成功状态码
| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 204 | 删除成功 |

### 6.2 客户端错误状态码
| 状态码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 413 | 请求数据过大 |
| 429 | 请求过于频繁 |

### 6.3 服务器错误状态码
| 状态码 | 说明 |
|--------|------|
| 500 | 服务器内部错误 |
| 503 | 服务暂时不可用 |

## 7. 版本管理

### 7.1 版本控制
- 版本号遵循语义化版本规范（主版本号.次版本号.修订号）
- 版本号通过URL路径控制：/api/v1/
- 向后兼容性：保证同一主版本内接口向后兼容

### 7.2 版本升级策略
- 主版本号变更：不兼容的API修改
- 次版本号变更：向下兼容的功能性新增
- 修订号变更：向下兼容的问题修正

## 8. 安全规范

### 8.1 认证与授权
- 使用JWT Token进行身份验证
- 实现基于角色的访问控制（RBAC）
- 敏感操作需要额外权限验证

### 8.2 数据安全
- 敏感数据传输使用HTTPS加密
- 密码等敏感信息必须加密存储
- 关键操作需要记录操作日志

### 8.3 输入验证
- 所有输入参数必须进行验证
- 防止SQL注入、XSS等攻击
- 限制请求频率防止恶意攻击

## 9. 性能规范

### 9.1 响应时间
- 普通查询接口响应时间 < 200ms
- 复杂分析接口响应时间 < 1000ms
- 批量处理接口响应时间 < 5000ms

### 9.2 并发处理
- 支持1000+并发请求
- 实现合理的限流机制
- 提供异步处理机制处理耗时操作

## 10. 文档规范

### 10.1 接口文档结构
每个接口文档应包含以下内容：
1. 基本信息
2. 请求参数
3. 响应参数
4. 示例
5. 错误码

### 10.2 文档更新
- 接口变更时及时更新文档
- 提供文档版本历史
- 保持文档与代码一致性