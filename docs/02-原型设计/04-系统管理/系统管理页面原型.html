<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VOC系统 - 系统管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f2f5;
            min-height: 100vh;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏 */
        .sidebar {
            width: 256px;
            background: #001529;
            color: white;
            flex-shrink: 0;
        }

        .sidebar-header {
            padding: 24px 16px;
            border-bottom: 1px solid #1f1f1f;
        }

        .logo {
            font-size: 18px;
            font-weight: 600;
            color: #1890ff;
        }

        .sidebar-menu {
            padding: 16px 0;
        }

        .menu-group {
            margin-bottom: 24px;
        }

        .menu-group-title {
            padding: 8px 16px;
            font-size: 12px;
            color: #8c8c8c;
            text-transform: uppercase;
            font-weight: 500;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            color: rgba(255, 255, 255, 0.65);
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .menu-item:hover {
            background: #1890ff;
            color: white;
        }

        .menu-item.active {
            background: #1890ff;
            color: white;
            position: relative;
        }

        .menu-item.active::after {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: #40a9ff;
        }

        .menu-icon {
            margin-right: 12px;
            font-size: 16px;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: white;
            padding: 16px 32px;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #262626;
        }

        .page-subtitle {
            font-size: 14px;
            color: #8c8c8c;
            margin-top: 4px;
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: 1px solid #d9d9d9;
            background: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .btn-primary {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background: #40a9ff;
        }

        .content-area {
            flex: 1;
            padding: 24px 32px;
        }

        /* 标签页 */
        .tabs {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .tab-headers {
            display: flex;
            border-bottom: 1px solid #e8e8e8;
        }

        .tab-header {
            padding: 16px 24px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: #8c8c8c;
            transition: all 0.2s;
        }

        .tab-header:hover {
            color: #1890ff;
        }

        .tab-header.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
            background: #f0f9ff;
        }

        .tab-content {
            padding: 24px;
            min-height: 600px;
        }

        /* 用户管理表格 */
        .table-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .search-box {
            display: flex;
            gap: 12px;
        }

        .search-input {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            width: 300px;
        }

        .table-container {
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e8e8e8;
        }

        .table th {
            background: #fafafa;
            font-weight: 600;
            color: #262626;
        }

        .table tbody tr:hover {
            background: #f5f5f5;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #f6ffed;
            color: #52c41a;
        }

        .status-inactive {
            background: #fff2e8;
            color: #fa8c16;
        }

        .status-disabled {
            background: #fff1f0;
            color: #ff4d4f;
        }

        .role-tag {
            padding: 2px 8px;
            background: #e6f7ff;
            color: #1890ff;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 4px;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            padding: 4px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }

        .action-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .action-btn.danger:hover {
            border-color: #ff4d4f;
            color: #ff4d4f;
        }

        /* 权限配置 */
        .permission-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 24px;
        }

        .role-list {
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }

        .role-item {
            padding: 16px;
            border-bottom: 1px solid #e8e8e8;
            cursor: pointer;
            transition: all 0.2s;
        }

        .role-item:hover {
            background: #f5f5f5;
        }

        .role-item.active {
            background: #e6f7ff;
            border-color: #1890ff;
        }

        .role-name {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .role-desc {
            font-size: 12px;
            color: #8c8c8c;
        }

        .permission-panel {
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }

        .permission-header {
            padding: 16px;
            background: #fafafa;
            border-bottom: 1px solid #e8e8e8;
            font-weight: 600;
        }

        .permission-content {
            padding: 16px;
        }

        .permission-group {
            margin-bottom: 24px;
        }

        .permission-group-title {
            font-weight: 600;
            margin-bottom: 12px;
            color: #262626;
        }

        .permission-items {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 8px;
        }

        .permission-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .permission-item input[type="checkbox"] {
            margin: 0;
        }

        /* 系统设置表单 */
        .settings-form {
            max-width: 600px;
        }

        .form-section {
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e8e8e8;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #262626;
            margin-bottom: 8px;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-control:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .form-help {
            font-size: 12px;
            color: #8c8c8c;
            margin-top: 4px;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 22px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 22px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #1890ff;
        }

        input:checked + .slider:before {
            transform: translateX(22px);
        }

        /* 操作日志 */
        .log-filters {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
            align-items: center;
        }

        .log-table {
            font-size: 13px;
        }

        .log-time {
            color: #8c8c8c;
        }

        .log-user {
            font-weight: 500;
        }

        .log-action {
            color: #1890ff;
        }

        .log-ip {
            font-family: monospace;
            color: #8c8c8c;
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            margin-top: 24px;
        }

        .page-btn {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .page-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .page-btn.active {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }

        /* 用户编辑模态框 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            padding: 24px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e8e8e8;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #262626;
        }

        .modal-close {
            cursor: pointer;
            font-size: 24px;
            color: #8c8c8c;
            border: none;
            background: none;
        }

        .modal-close:hover {
            color: #262626;
        }

        .modal-body {
            margin-bottom: 24px;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            padding-top: 16px;
            border-top: 1px solid #e8e8e8;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .form-grid .form-group {
            margin-bottom: 16px;
        }

        .form-grid .form-group.full-width {
            grid-column: 1 / -1;
        }

        .avatar-upload {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 20px;
        }

        .avatar-preview {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            background: #1890ff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: 600;
        }

        .avatar-upload-btn {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 14px;
        }

        .checkbox-group-inline {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
        }

        .checkbox-item-inline {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        /* 响应式 */
        @media (max-width: 1200px) {
            .permission-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                display: none;
            }
            
            .content-area {
                padding: 16px;
            }
            
            .table-container {
                overflow-x: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo" onclick="goToHome()" style="cursor: pointer;">🛡️ 系统管理</div>
            </div>
            <div class="sidebar-menu">
                <div class="menu-group">
                    <div class="menu-group-title">用户管理</div>
                    <a href="#" class="menu-item active">
                        <span class="menu-icon">👥</span>
                        用户列表
                    </a>
                    <a href="#" class="menu-item">
                        <span class="menu-icon">🔑</span>
                        权限管理
                    </a>
                    <a href="#" class="menu-item">
                        <span class="menu-icon">👨‍💼</span>
                        角色管理
                    </a>
                </div>
                <div class="menu-group">
                    <div class="menu-group-title">系统配置</div>
                    <a href="#" class="menu-item">
                        <span class="menu-icon">⚙️</span>
                        基础设置
                    </a>
                    <a href="#" class="menu-item">
                        <span class="menu-icon">🔒</span>
                        安全设置
                    </a>
                    <a href="#" class="menu-item">
                        <span class="menu-icon">💾</span>
                        备份管理
                    </a>
                </div>
                <div class="menu-group">
                    <div class="menu-group-title">监控日志</div>
                    <a href="#" class="menu-item">
                        <span class="menu-icon">📋</span>
                        操作日志
                    </a>
                    <a href="#" class="menu-item">
                        <span class="menu-icon">📊</span>
                        系统监控
                    </a>
                    <a href="#" class="menu-item">
                        <span class="menu-icon">⚠️</span>
                        异常告警
                    </a>
                </div>
            </div>
        </div>

        <!-- 主内容 -->
        <div class="main-content">
            <div class="header">
                <div>
                    <div class="page-title">系统管理</div>
                    <div class="page-subtitle">管理用户、权限、系统配置和监控日志</div>
                </div>
                <div class="header-actions">
                    <button class="btn" onclick="goToDashboard()">📊 返回仪表板</button>
                    <button class="btn" onclick="systemStatus()">📊 系统状态</button>
                    <button class="btn" onclick="dataBackup()">💾 数据备份</button>
                    <button class="btn btn-primary" onclick="addNewUser()">➕ 新增用户</button>
                </div>
            </div>

            <div class="content-area">
                <div class="tabs">
                    <div class="tab-headers">
                        <div class="tab-header active" onclick="switchTab(this, 'users')">
                            👥 用户管理
                        </div>
                        <div class="tab-header" onclick="switchTab(this, 'permissions')">
                            🔑 权限配置
                        </div>
                        <div class="tab-header" onclick="switchTab(this, 'settings')">
                            ⚙️ 系统设置
                        </div>
                        <div class="tab-header" onclick="switchTab(this, 'logs')">
                            📋 操作日志
                        </div>
                    </div>

                    <!-- 用户管理标签页 -->
                    <div class="tab-content" id="users-tab">
                        <div class="table-toolbar">
                            <div class="search-box">
                                <input type="text" class="search-input" placeholder="搜索用户名、邮箱或部门...">
                                <button class="btn">🔍 搜索</button>
                            </div>
                            <div>
                                <button class="btn">📤 导出</button>
                                <button class="btn">📥 导入</button>
                                <button class="btn btn-primary">➕ 新增用户</button>
                            </div>
                        </div>

                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>用户信息</th>
                                        <th>部门/角色</th>
                                        <th>权限</th>
                                        <th>状态</th>
                                        <th>最后登录</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 12px;">
                                                <div style="width: 32px; height: 32px; background: #1890ff; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 14px;">张</div>
                                                <div>
                                                    <div style="font-weight: 500;">张三</div>
                                                    <div style="font-size: 12px; color: #8c8c8c;"><EMAIL></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>产品部</div>
                                            <div class="role-tag">系统管理员</div>
                                        </td>
                                        <td>
                                            <div class="role-tag">用户管理</div>
                                            <div class="role-tag">系统配置</div>
                                            <div class="role-tag">数据分析</div>
                                        </td>
                                        <td>
                                            <span class="status-badge status-active">正常</span>
                                        </td>
                                        <td>
                                            <div>2024-01-15 14:30</div>
                                            <div style="font-size: 12px; color: #8c8c8c;">IP: *************</div>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="action-btn">✏️ 编辑</button>
                                                <button class="action-btn">🔑 权限</button>
                                                <button class="action-btn">🚫 禁用</button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 12px;">
                                                <div style="width: 32px; height: 32px; background: #52c41a; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 14px;">李</div>
                                                <div>
                                                    <div style="font-weight: 500;">李四</div>
                                                    <div style="font-size: 12px; color: #8c8c8c;"><EMAIL></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>运营部</div>
                                            <div class="role-tag">数据分析师</div>
                                        </td>
                                        <td>
                                            <div class="role-tag">数据查看</div>
                                            <div class="role-tag">报表生成</div>
                                        </td>
                                        <td>
                                            <span class="status-badge status-active">正常</span>
                                        </td>
                                        <td>
                                            <div>2024-01-15 13:45</div>
                                            <div style="font-size: 12px; color: #8c8c8c;">IP: *************</div>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="action-btn">✏️ 编辑</button>
                                                <button class="action-btn">🔑 权限</button>
                                                <button class="action-btn">🚫 禁用</button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 12px;">
                                                <div style="width: 32px; height: 32px; background: #fa8c16; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 14px;">王</div>
                                                <div>
                                                    <div style="font-weight: 500;">王五</div>
                                                    <div style="font-size: 12px; color: #8c8c8c;"><EMAIL></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>客服部</div>
                                            <div class="role-tag">普通用户</div>
                                        </td>
                                        <td>
                                            <div class="role-tag">数据查看</div>
                                        </td>
                                        <td>
                                            <span class="status-badge status-inactive">离线</span>
                                        </td>
                                        <td>
                                            <div>2024-01-14 18:00</div>
                                            <div style="font-size: 12px; color: #8c8c8c;">IP: *************</div>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="action-btn" onclick="editUser('wangwu', '王五', '<EMAIL>', '客服部', '普通用户')">✏️ 编辑</button>
                                                <button class="action-btn" onclick="managePermissions('wangwu')">🔑 权限</button>
                                                <button class="action-btn danger" onclick="deleteUser('wangwu')">🗑️ 删除</button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="pagination">
                            <button class="page-btn">« 上一页</button>
                            <button class="page-btn active">1</button>
                            <button class="page-btn">2</button>
                            <button class="page-btn">3</button>
                            <button class="page-btn">下一页 »</button>
                        </div>
                    </div>

                    <!-- 权限配置标签页 -->
                    <div class="tab-content" id="permissions-tab" style="display: none;">
                        <div class="permission-grid">
                            <div class="role-list">
                                <div class="role-item active">
                                    <div class="role-name">系统管理员</div>
                                    <div class="role-desc">拥有系统所有权限，可以管理用户和系统配置</div>
                                </div>
                                <div class="role-item">
                                    <div class="role-name">数据分析师</div>
                                    <div class="role-desc">可以查看和分析VOC数据，生成报表</div>
                                </div>
                                <div class="role-item">
                                    <div class="role-name">业务用户</div>
                                    <div class="role-desc">可以查看分析结果和报表，不能修改配置</div>
                                </div>
                                <div class="role-item">
                                    <div class="role-name">只读用户</div>
                                    <div class="role-desc">只能查看基础数据和报表</div>
                                </div>
                            </div>

                            <div class="permission-panel">
                                <div class="permission-header">
                                    系统管理员 - 权限配置
                                </div>
                                <div class="permission-content">
                                    <div class="permission-group">
                                        <div class="permission-group-title">🏠 首页权限</div>
                                        <div class="permission-items">
                                            <div class="permission-item">
                                                <input type="checkbox" checked>
                                                <span>查看仪表板</span>
                                            </div>
                                            <div class="permission-item">
                                                <input type="checkbox" checked>
                                                <span>查看统计数据</span>
                                            </div>
                                            <div class="permission-item">
                                                <input type="checkbox" checked>
                                                <span>导出数据</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="permission-group">
                                        <div class="permission-group-title">👥 用户管理</div>
                                        <div class="permission-items">
                                            <div class="permission-item">
                                                <input type="checkbox" checked>
                                                <span>查看用户列表</span>
                                            </div>
                                            <div class="permission-item">
                                                <input type="checkbox" checked>
                                                <span>新增用户</span>
                                            </div>
                                            <div class="permission-item">
                                                <input type="checkbox" checked>
                                                <span>编辑用户</span>
                                            </div>
                                            <div class="permission-item">
                                                <input type="checkbox" checked>
                                                <span>删除用户</span>
                                            </div>
                                            <div class="permission-item">
                                                <input type="checkbox" checked>
                                                <span>重置密码</span>
                                            </div>
                                            <div class="permission-item">
                                                <input type="checkbox" checked>
                                                <span>分配角色</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="permission-group">
                                        <div class="permission-group-title">🔑 权限管理</div>
                                        <div class="permission-items">
                                            <div class="permission-item">
                                                <input type="checkbox" checked>
                                                <span>查看角色列表</span>
                                            </div>
                                            <div class="permission-item">
                                                <input type="checkbox" checked>
                                                <span>创建角色</span>
                                            </div>
                                            <div class="permission-item">
                                                <input type="checkbox" checked>
                                                <span>编辑权限</span>
                                            </div>
                                            <div class="permission-item">
                                                <input type="checkbox" checked>
                                                <span>删除角色</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="permission-group">
                                        <div class="permission-group-title">📊 数据分析</div>
                                        <div class="permission-items">
                                            <div class="permission-item">
                                                <input type="checkbox" checked>
                                                <span>查看分析报表</span>
                                            </div>
                                            <div class="permission-item">
                                                <input type="checkbox" checked>
                                                <span>创建自定义报表</span>
                                            </div>
                                            <div class="permission-item">
                                                <input type="checkbox" checked>
                                                <span>导出报表数据</span>
                                            </div>
                                            <div class="permission-item">
                                                <input type="checkbox" checked>
                                                <span>配置分析规则</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="permission-group">
                                        <div class="permission-group-title">⚙️ 系统配置</div>
                                        <div class="permission-items">
                                            <div class="permission-item">
                                                <input type="checkbox" checked>
                                                <span>系统基础设置</span>
                                            </div>
                                            <div class="permission-item">
                                                <input type="checkbox" checked>
                                                <span>安全配置</span>
                                            </div>
                                            <div class="permission-item">
                                                <input type="checkbox" checked>
                                                <span>数据源配置</span>
                                            </div>
                                            <div class="permission-item">
                                                <input type="checkbox" checked>
                                                <span>备份管理</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div style="margin-top: 24px;">
                                        <button class="btn btn-primary">💾 保存权限配置</button>
                                        <button class="btn">↶ 重置</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统设置标签页 -->
                    <div class="tab-content" id="settings-tab" style="display: none;">
                        <div class="settings-form">
                            <div class="form-section">
                                <div class="section-title">🏢 基础信息</div>
                                <div class="form-group">
                                    <label class="form-label">系统名称</label>
                                    <input type="text" class="form-control" value="VOC客户声音分析系统">
                                    <div class="form-help">显示在系统各个页面的标题</div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">公司名称</label>
                                    <input type="text" class="form-control" value="某某科技有限公司">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">系统版本</label>
                                    <input type="text" class="form-control" value="v2.1.0" readonly>
                                    <div class="form-help">当前运行版本，自动更新</div>
                                </div>
                            </div>

                            <div class="form-section">
                                <div class="section-title">🔒 安全设置</div>
                                <div class="form-group">
                                    <label class="form-label">密码策略</label>
                                    <select class="form-control">
                                        <option>标准策略 (8位，包含字母数字)</option>
                                        <option>增强策略 (12位，包含特殊字符)</option>
                                        <option>自定义策略</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">会话超时时间</label>
                                    <select class="form-control">
                                        <option>30分钟</option>
                                        <option>1小时</option>
                                        <option>2小时</option>
                                        <option>4小时</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">登录失败锁定</label>
                                    <div style="display: flex; align-items: center; gap: 16px;">
                                        <span>连续失败</span>
                                        <input type="number" class="form-control" value="5" style="width: 80px;">
                                        <span>次后锁定</span>
                                        <input type="number" class="form-control" value="30" style="width: 80px;">
                                        <span>分钟</span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-section">
                                <div class="section-title">📧 邮件配置</div>
                                <div class="form-group">
                                    <label class="form-label">SMTP服务器</label>
                                    <input type="text" class="form-control" value="smtp.company.com">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">端口号</label>
                                    <input type="number" class="form-control" value="587">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">发件人邮箱</label>
                                    <input type="email" class="form-control" value="<EMAIL>">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">启用邮件通知</label>
                                    <div style="display: flex; align-items: center; gap: 16px;">
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                        <span>系统异常时发送邮件通知</span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-section">
                                <div class="section-title">💾 数据配置</div>
                                <div class="form-group">
                                    <label class="form-label">数据保留期限</label>
                                    <select class="form-control">
                                        <option>90天</option>
                                        <option>180天</option>
                                        <option>1年</option>
                                        <option>3年</option>
                                        <option>永久保留</option>
                                    </select>
                                    <div class="form-help">超过期限的数据将自动归档</div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">自动备份</label>
                                    <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 8px;">
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                        <span>启用自动备份</span>
                                    </div>
                                    <select class="form-control">
                                        <option>每日备份</option>
                                        <option>每周备份</option>
                                        <option>每月备份</option>
                                    </select>
                                </div>
                            </div>

                            <div style="margin-top: 32px;">
                                <button class="btn btn-primary">💾 保存设置</button>
                                <button class="btn">↶ 重置</button>
                                <button class="btn">🔄 恢复默认</button>
                            </div>
                        </div>
                    </div>

                    <!-- 操作日志标签页 -->
                    <div class="tab-content" id="logs-tab" style="display: none;">
                        <div class="log-filters">
                            <span>筛选条件:</span>
                            <select class="form-control" style="width: 150px;">
                                <option>全部操作</option>
                                <option>用户登录</option>
                                <option>权限变更</option>
                                <option>系统配置</option>
                                <option>数据导出</option>
                            </select>
                            <input type="date" class="form-control" style="width: 150px;" value="2024-01-01">
                            <span>至</span>
                            <input type="date" class="form-control" style="width: 150px;" value="2024-01-15">
                            <input type="text" class="form-control" placeholder="搜索用户..." style="width: 200px;">
                            <button class="btn">🔍 搜索</button>
                        </div>

                        <div class="table-container">
                            <table class="table log-table">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>用户</th>
                                        <th>操作</th>
                                        <th>详情</th>
                                        <th>IP地址</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="log-time">2024-01-15 14:30:25</td>
                                        <td class="log-user">张三</td>
                                        <td class="log-action">用户登录</td>
                                        <td>成功登录系统</td>
                                        <td class="log-ip">*************</td>
                                        <td><span class="status-badge status-active">成功</span></td>
                                    </tr>
                                    <tr>
                                        <td class="log-time">2024-01-15 14:25:10</td>
                                        <td class="log-user">张三</td>
                                        <td class="log-action">权限修改</td>
                                        <td>为用户李四分配数据分析师角色</td>
                                        <td class="log-ip">*************</td>
                                        <td><span class="status-badge status-active">成功</span></td>
                                    </tr>
                                    <tr>
                                        <td class="log-time">2024-01-15 14:20:33</td>
                                        <td class="log-user">李四</td>
                                        <td class="log-action">数据导出</td>
                                        <td>导出VOC分析报表 (2024年1月)</td>
                                        <td class="log-ip">*************</td>
                                        <td><span class="status-badge status-active">成功</span></td>
                                    </tr>
                                    <tr>
                                        <td class="log-time">2024-01-15 14:15:42</td>
                                        <td class="log-user">王五</td>
                                        <td class="log-action">登录失败</td>
                                        <td>密码错误，登录失败</td>
                                        <td class="log-ip">*************</td>
                                        <td><span class="status-badge status-disabled">失败</span></td>
                                    </tr>
                                    <tr>
                                        <td class="log-time">2024-01-15 14:10:15</td>
                                        <td class="log-user">张三</td>
                                        <td class="log-action">系统配置</td>
                                        <td>修改会话超时时间为2小时</td>
                                        <td class="log-ip">*************</td>
                                        <td><span class="status-badge status-active">成功</span></td>
                                    </tr>
                                    <tr>
                                        <td class="log-time">2024-01-15 14:05:28</td>
                                        <td class="log-user">系统</td>
                                        <td class="log-action">自动备份</td>
                                        <td>执行每日自动数据备份</td>
                                        <td class="log-ip">-</td>
                                        <td><span class="status-badge status-active">成功</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="pagination">
                            <button class="page-btn">« 上一页</button>
                            <button class="page-btn active">1</button>
                            <button class="page-btn">2</button>
                            <button class="page-btn">3</button>
                            <button class="page-btn">4</button>
                            <button class="page-btn">5</button>
                            <button class="page-btn">下一页 »</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户编辑模态框 -->
    <div class="modal" id="userEditModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">📝 编辑用户信息</div>
                <button class="modal-close" onclick="closeUserEditModal()">×</button>
            </div>
            <div class="modal-body">
                <div class="avatar-upload">
                    <div class="avatar-preview" id="avatarPreview">张</div>
                    <div>
                        <button class="avatar-upload-btn" onclick="uploadAvatar()">📷 更换头像</button>
                        <div style="font-size: 12px; color: #8c8c8c; margin-top: 4px;">支持 JPG、PNG 格式，建议尺寸 200x200</div>
                    </div>
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">用户名 *</label>
                        <input type="text" class="form-control" id="editUsername" placeholder="请输入用户名">
                    </div>
                    <div class="form-group">
                        <label class="form-label">姓名 *</label>
                        <input type="text" class="form-control" id="editRealName" placeholder="请输入真实姓名">
                    </div>
                    <div class="form-group">
                        <label class="form-label">邮箱 *</label>
                        <input type="email" class="form-control" id="editEmail" placeholder="请输入邮箱地址">
                    </div>
                    <div class="form-group">
                        <label class="form-label">手机号</label>
                        <input type="tel" class="form-control" id="editPhone" placeholder="请输入手机号">
                    </div>
                    <div class="form-group">
                        <label class="form-label">部门</label>
                        <select class="form-control" id="editDepartment">
                            <option>产品部</option>
                            <option>技术部</option>
                            <option>运营部</option>
                            <option>客服部</option>
                            <option>市场部</option>
                            <option>财务部</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">角色</label>
                        <select class="form-control" id="editRole">
                            <option>系统管理员</option>
                            <option>数据分析师</option>
                            <option>业务用户</option>
                            <option>普通用户</option>
                            <option>只读用户</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">状态</label>
                        <select class="form-control" id="editStatus">
                            <option value="active">正常</option>
                            <option value="inactive">禁用</option>
                            <option value="pending">待激活</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">入职日期</label>
                        <input type="date" class="form-control" id="editJoinDate">
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label">权限设置</label>
                        <div class="checkbox-group-inline">
                            <div class="checkbox-item-inline">
                                <input type="checkbox" id="perm1" checked>
                                <label for="perm1">数据查看</label>
                            </div>
                            <div class="checkbox-item-inline">
                                <input type="checkbox" id="perm2" checked>
                                <label for="perm2">报表生成</label>
                            </div>
                            <div class="checkbox-item-inline">
                                <input type="checkbox" id="perm3">
                                <label for="perm3">用户管理</label>
                            </div>
                            <div class="checkbox-item-inline">
                                <input type="checkbox" id="perm4">
                                <label for="perm4">系统配置</label>
                            </div>
                            <div class="checkbox-item-inline">
                                <input type="checkbox" id="perm5">
                                <label for="perm5">数据导出</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label">备注</label>
                        <textarea class="form-control" id="editRemark" rows="3" placeholder="请输入备注信息"></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="closeUserEditModal()">取消</button>
                <button class="btn" onclick="resetUserForm()">重置</button>
                <button class="btn btn-primary" onclick="saveUserChanges()">保存</button>
            </div>
        </div>
    </div>

    <script>
        function switchTab(tabHeader, tabId) {
            // 移除所有活跃状态
            document.querySelectorAll('.tab-header').forEach(header => {
                header.classList.remove('active');
            });
            document.querySelectorAll('.tab-content').forEach(content => {
                content.style.display = 'none';
            });
            
            // 激活选中的标签
            tabHeader.classList.add('active');
            document.getElementById(tabId + '-tab').style.display = 'block';
        }

        // 侧边栏菜单点击
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.menu-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 用户操作函数
        function editUser(userId, realName, email, department, role) {
            // 显示模态框
            document.getElementById('userEditModal').style.display = 'block';
            
            // 填充表单数据
            document.getElementById('editUsername').value = userId;
            document.getElementById('editRealName').value = realName;
            document.getElementById('editEmail').value = email;
            document.getElementById('editDepartment').value = department;
            document.getElementById('editRole').value = role;
            document.getElementById('editStatus').value = 'active';
            
            // 更新头像预览
            const avatarPreview = document.getElementById('avatarPreview');
            avatarPreview.textContent = realName.charAt(0);
            
            // 设置头像颜色
            const colors = ['#1890ff', '#52c41a', '#fa8c16', '#722ed1', '#eb2f96'];
            avatarPreview.style.background = colors[userId.length % colors.length];
        }

        function closeUserEditModal() {
            document.getElementById('userEditModal').style.display = 'none';
        }

        function uploadAvatar() {
            alert('📷 头像上传\n\n支持功能:\n• 选择本地图片\n• 在线拍照\n• 头像裁剪\n• 预设头像库\n\n请选择上传方式');
        }

        function resetUserForm() {
            if (confirm('确定要重置表单吗？所有修改将丢失。')) {
                // 重置表单逻辑
                document.querySelectorAll('#userEditModal input, #userEditModal select, #userEditModal textarea').forEach(el => {
                    if (el.type === 'checkbox') {
                        el.checked = false;
                    } else {
                        el.value = '';
                    }
                });
                alert('✅ 表单已重置');
            }
        }

        function saveUserChanges() {
            // 验证表单
            const username = document.getElementById('editUsername').value;
            const realName = document.getElementById('editRealName').value;
            const email = document.getElementById('editEmail').value;
            
            if (!username || !realName || !email) {
                alert('❌ 请填写必填字段\n\n• 用户名\n• 姓名\n• 邮箱');
                return;
            }
            
            // 模拟保存过程
            const saveBtn = event.target;
            saveBtn.innerHTML = '💾 保存中...';
            saveBtn.disabled = true;
            
            setTimeout(() => {
                saveBtn.innerHTML = '保存';
                saveBtn.disabled = false;
                alert('✅ 用户信息保存成功！\n\n修改内容:\n• 基本信息已更新\n• 权限设置已生效\n• 系统日志已记录');
                closeUserEditModal();
                
                // 更新表格显示（实际项目中应该刷新数据）
                location.reload();
            }, 1500);
        }

        function managePermissions(userId) {
            alert(`🔑 权限管理 - ${userId}\n\n当前权限:\n✅ 数据查看\n✅ 报表生成\n❌ 用户管理\n❌ 系统配置\n\n点击"编辑"按钮可修改权限设置`);
        }

        function toggleUserStatus(userId, isActive) {
            const action = isActive ? '禁用' : '启用';
            if (confirm(`确定要${action}用户 ${userId} 吗？\n\n${action}后用户将${isActive ? '无法登录系统' : '恢复正常使用'}`)) {
                alert(`✅ 用户状态已更改\n\n用户 ${userId} 已被${action}`);
                
                // 模拟更新状态（实际项目中应该发送请求）
                setTimeout(() => {
                    location.reload();
                }, 500);
            }
        }

        function deleteUser(userId) {
            if (confirm(`⚠️ 删除用户确认\n\n确定要删除用户 ${userId} 吗？\n\n此操作将：\n• 永久删除用户账户\n• 清除用户相关数据\n• 撤销所有权限\n\n此操作不可恢复！`)) {
                // 二次确认
                const confirmation = prompt('请输入 "DELETE" 确认删除操作：');
                if (confirmation === 'DELETE') {
                    alert('✅ 用户删除成功\n\n用户账户已被永久删除');
                    setTimeout(() => {
                        location.reload();
                    }, 500);
                } else {
                    alert('❌ 删除操作已取消');
                }
            }
        }

        // 权限配置角色切换
        document.querySelectorAll('.role-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.role-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
                
                const roleName = this.querySelector('.role-name').textContent;
                document.querySelector('.permission-header').textContent = roleName + ' - 权限配置';
                
                // 这里可以加载对应角色的权限配置
                loadRolePermissions(roleName);
            });
        });

        function loadRolePermissions(roleName) {
            // 模拟加载不同角色的权限配置
            const checkboxes = document.querySelectorAll('#permissions-tab input[type="checkbox"]');
            
            switch(roleName) {
                case '数据分析师':
                    checkboxes.forEach((cb, index) => {
                        cb.checked = index < 8; // 前8个权限为true
                    });
                    break;
                case '业务用户':
                    checkboxes.forEach((cb, index) => {
                        cb.checked = index < 5; // 前5个权限为true
                    });
                    break;
                case '只读用户':
                    checkboxes.forEach((cb, index) => {
                        cb.checked = index < 3; // 前3个权限为true
                    });
                    break;
                default:
                    checkboxes.forEach(cb => cb.checked = true); // 系统管理员全部权限
            }
        }

        // 设置保存
        function saveSettings() {
            alert('✅ 系统设置已保存！\n\n已更新配置:\n• 基础信息\n• 安全设置\n• 邮件配置\n• 数据配置\n\n某些设置可能需要重启系统后生效。');
        }

        // 导航函数
        function goToHome() {
            window.location.href = '../01-系统入口/登录页面原型.html';
        }

        function goToDashboard() {
            window.location.href = '../03-核心功能/数据分析仪表板原型.html';
        }

        function systemStatus() {
            alert('📊 系统状态\n\n• CPU使用率: 45%\n• 内存使用率: 62%\n• 磁盘使用率: 78%\n• 网络状态: 正常\n• 数据库连接: 正常\n• 缓存状态: 正常');
        }

        function dataBackup() {
            alert('💾 数据备份\n\n上次备份: 2024-01-15 02:00\n备份状态: 成功\n备份大小: 2.3GB\n\n是否立即执行手动备份？');
        }

        function addNewUser() {
            alert('➕ 新增用户\n\n请填写用户信息:\n• 用户名\n• 邮箱\n• 部门\n• 角色\n• 权限设置\n\n点击确定打开用户创建表单');
        }

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 模拟实时更新最后登录时间
            setInterval(() => {
                const timeElements = document.querySelectorAll('.log-time');
                if (timeElements.length > 0) {
                    // 更新第一条记录的时间（模拟新的操作日志）
                    const now = new Date();
                    const timeStr = now.getFullYear() + '-' + 
                                  String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                                  String(now.getDate()).padStart(2, '0') + ' ' +
                                  String(now.getHours()).padStart(2, '0') + ':' +
                                  String(now.getMinutes()).padStart(2, '0') + ':' +
                                  String(now.getSeconds()).padStart(2, '0');
                    // 这里可以添加新的日志记录到表格顶部
                }
            }, 60000); // 每分钟检查一次
        });
    </script>
</body>
</html> 