<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VOC系统原型导航</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 16px;
        }

        .header p {
            font-size: 20px;
            opacity: 0.9;
            margin-bottom: 8px;
        }

        .header .version {
            font-size: 14px;
            opacity: 0.7;
        }

        .pages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 24px;
            margin-bottom: 40px;
        }

        .page-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .page-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
        }

        .page-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #1890ff, #52c41a);
        }

        .page-icon {
            font-size: 32px;
            margin-bottom: 16px;
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
        }

        .page-desc {
            font-size: 14px;
            color: #8c8c8c;
            margin-bottom: 16px;
            line-height: 1.5;
        }

        .page-features {
            list-style: none;
            margin-bottom: 20px;
        }

        .page-features li {
            font-size: 13px;
            color: #595959;
            margin-bottom: 4px;
            padding-left: 16px;
            position: relative;
        }

        .page-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #52c41a;
            font-weight: 600;
        }

        .page-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 16px;
        }

        .status-complete {
            background: #f6ffed;
            color: #52c41a;
        }

        .status-demo {
            background: #e6f7ff;
            color: #1890ff;
        }

        .page-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            flex: 1;
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
            text-decoration: none;
            color: #262626;
            text-align: center;
        }

        .action-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .action-btn.primary {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }

        .action-btn.primary:hover {
            background: #40a9ff;
        }

        .info-section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .info-title {
            font-size: 18px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-content {
            color: #595959;
            line-height: 1.6;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .stat-item {
            text-align: center;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 600;
            color: #1890ff;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            color: #8c8c8c;
        }

        .footer {
            text-align: center;
            color: white;
            opacity: 0.7;
            font-size: 14px;
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 32px;
            }
            
            .pages-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .page-card {
            animation: fadeInUp 0.6s ease forwards;
        }

        .page-card:nth-child(1) { animation-delay: 0.1s; }
        .page-card:nth-child(2) { animation-delay: 0.2s; }
        .page-card:nth-child(3) { animation-delay: 0.3s; }
        .page-card:nth-child(4) { animation-delay: 0.4s; }
        .page-card:nth-child(5) { animation-delay: 0.5s; }
        .page-card:nth-child(6) { animation-delay: 0.6s; }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>🎯 VOC系统原型集合</h1>
            <p>AI驱动的多行业客户声音分析平台</p>
            <div class="version">原型版本 v2.1.0 | 更新时间: 2024年1月15日</div>
        </div>

        <!-- 页面导航 -->
        <div class="pages-grid">
            <!-- 登录页面 -->
            <div class="page-card" onclick="openPage('登录页面原型.html')">
                <div class="page-icon">🚪</div>
                <div class="page-title">登录页面</div>
                <div class="page-desc">系统登录入口，支持多种角色演示账户</div>
                <div class="page-status status-complete">✨ 完整功能</div>
                <ul class="page-features">
                    <li>响应式设计，支持多设备</li>
                    <li>表单验证和错误提示</li>
                    <li>演示账户快速登录</li>
                    <li>加载动画和交互效果</li>
                </ul>
                <div class="page-actions">
                    <a href="01-系统入口/登录页面原型.html" class="action-btn primary">🚀 体验登录</a>
                    <button class="action-btn" onclick="viewPageInfo('login')">📋 查看详情</button>
                </div>
            </div>

            <!-- 行业选择页面 -->
            <div class="page-card" onclick="openPage('行业选择页面原型.html')">
                <div class="page-icon">🏭</div>
                <div class="page-title">行业选择页面</div>
                <div class="page-desc">多行业配置选择，支持智能推荐和自定义配置</div>
                <div class="page-status status-complete">✨ 完整功能</div>
                <ul class="page-features">
                    <li>6个行业模板可选</li>
                    <li>AI智能推荐系统</li>
                    <li>配置进度实时更新</li>
                    <li>自定义行业配置</li>
                </ul>
                <div class="page-actions">
                    <a href="02-配置流程/行业选择页面原型.html" class="action-btn primary">🎯 选择行业</a>
                    <button class="action-btn" onclick="viewPageInfo('industry')">📋 查看详情</button>
                </div>
            </div>

            <!-- 配置向导页面 -->
            <div class="page-card" onclick="openPage('配置向导页面原型.html')">
                <div class="page-icon">⚙️</div>
                <div class="page-title">配置向导页面</div>
                <div class="page-desc">智能配置向导，帮助用户快速完成系统配置</div>
                <div class="page-status status-complete">✨ 完整功能</div>
                <ul class="page-features">
                    <li>分步骤配置流程</li>
                    <li>AI智能建议</li>
                    <li>实时预览和验证</li>
                    <li>自动保存功能</li>
                </ul>
                <div class="page-actions">
                    <a href="02-配置流程/配置向导页面原型.html" class="action-btn primary">🛠️ 开始配置</a>
                    <button class="action-btn" onclick="viewPageInfo('config')">📋 查看详情</button>
                </div>
            </div>

            <!-- 数据分析仪表板 -->
            <div class="page-card" onclick="openPage('数据分析仪表板原型.html')">
                <div class="page-icon">📊</div>
                <div class="page-title">数据分析仪表板</div>
                <div class="page-desc">主要工作台，展示关键指标和实时监控数据</div>
                <div class="page-status status-complete">✨ 完整功能</div>
                <ul class="page-features">
                    <li>实时数据监控</li>
                    <li>交互式图表展示</li>
                    <li>AI智能洞察面板</li>
                    <li>快速操作功能</li>
                </ul>
                <div class="page-actions">
                    <a href="03-核心功能/数据分析仪表板原型.html" class="action-btn primary">📈 查看仪表板</a>
                    <button class="action-btn" onclick="viewPageInfo('dashboard')">📋 查看详情</button>
                </div>
            </div>

            <!-- 报表分析页面 -->
            <div class="page-card" onclick="openPage('报表分析页面原型.html')">
                <div class="page-icon">📈</div>
                <div class="page-title">报表分析页面</div>
                <div class="page-desc">专业报表分析工具，支持多维度数据分析</div>
                <div class="page-status status-complete">✨ 完整功能</div>
                <ul class="page-features">
                    <li>多种图表类型</li>
                    <li>高级筛选器</li>
                    <li>数据导出功能</li>
                    <li>钻取分析支持</li>
                </ul>
                <div class="page-actions">
                    <a href="03-核心功能/报表分析页面原型.html" class="action-btn primary">📊 查看报表</a>
                    <button class="action-btn" onclick="viewPageInfo('reports')">📋 查看详情</button>
                </div>
            </div>

            <!-- 深度洞察页面 -->
            <div class="page-card" onclick="openPage('深度洞察页面原型.html')">
                <div class="page-icon">🔍</div>
                <div class="page-title">深度洞察页面</div>
                <div class="page-desc">AI驱动的智能洞察分析，发现隐藏的商业价值</div>
                <div class="page-status status-complete">✨ 完整功能</div>
                <ul class="page-features">
                    <li>AI智能洞察生成</li>
                    <li>趋势预测分析</li>
                    <li>问题聚类识别</li>
                    <li>机会发现推荐</li>
                </ul>
                <div class="page-actions">
                    <a href="03-核心功能/深度洞察页面原型.html" class="action-btn primary">🤖 AI洞察</a>
                    <button class="action-btn" onclick="viewPageInfo('insights')">📋 查看详情</button>
                </div>
            </div>

            <!-- 系统管理页面 -->
            <div class="page-card" onclick="openPage('系统管理页面原型.html')">
                <div class="page-icon">🛡️</div>
                <div class="page-title">系统管理页面</div>
                <div class="page-desc">系统后台管理，用户权限和系统配置管理</div>
                <div class="page-status status-complete">✨ 完整功能</div>
                <ul class="page-features">
                    <li>用户账户管理</li>
                    <li>权限配置系统</li>
                    <li>系统设置面板</li>
                    <li>操作日志查看</li>
                </ul>
                <div class="page-actions">
                    <a href="04-系统管理/系统管理页面原型.html" class="action-btn primary">⚙️ 系统管理</a>
                    <button class="action-btn" onclick="viewPageInfo('admin')">📋 查看详情</button>
                </div>
            </div>
        </div>

        <!-- 项目信息 -->
        <div class="info-section">
            <div class="info-title">
                📋 项目信息
            </div>
            <div class="info-content">
                <p><strong>项目名称：</strong>VOC客户声音分析系统原型</p>
                <p><strong>技术栈：</strong>HTML5 + CSS3 + JavaScript (原生)</p>
                <p><strong>设计理念：</strong>响应式设计、用户体验优先、AI智能化</p>
                <p><strong>适用场景：</strong>产品演示、用户测试、需求验证、投资路演</p>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="info-section">
            <div class="info-title">
                📊 原型统计
            </div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">7</div>
                    <div class="stat-label">完整页面</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">45+</div>
                    <div class="stat-label">交互功能</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">响应式适配</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">6</div>
                    <div class="stat-label">行业模板</div>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="info-section">
            <div class="info-title">
                💡 使用说明
            </div>
            <div class="info-content">
                <p><strong>1. 快速体验：</strong>点击"🚀 体验登录"开始完整流程体验</p>
                <p><strong>2. 演示账户：</strong>admin/123456 (管理员)、analyst/123456 (分析师)、user/123456 (普通用户)</p>
                <p><strong>3. 页面导航：</strong>所有页面都有完整的导航链接，可以自由跳转</p>
                <p><strong>4. 交互功能：</strong>点击按钮、图表、表格等元素都有相应的交互反馈</p>
                <p><strong>5. 模拟数据：</strong>所有数据都是模拟生成，用于演示系统功能</p>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="footer">
            <p>VOC客户声音分析系统原型 | 为产品演示和用户体验而设计</p>
        </div>
    </div>

    <script>
        function openPage(pageUrl) {
            window.open(pageUrl, '_blank');
        }

        function viewPageInfo(pageType) {
            const pageInfos = {
                'login': {
                    title: '🚪 登录页面详情',
                    content: '功能特性:\n• 现代化UI设计\n• 表单验证机制\n• 多角色演示账户\n• 加载动画效果\n• 响应式布局\n\n技术实现:\n• 原生JavaScript\n• CSS3动画\n• 移动端适配\n• 无障碍访问支持'
                },
                'industry': {
                    title: '🏭 行业选择页面详情', 
                    content: '功能特性:\n• 6个行业模板\n• AI智能推荐\n• 配置进度跟踪\n• 自定义配置\n• 实时状态更新\n\n支持行业:\n• 手机通讯\n• 汽车制造\n• 美妆护肤\n• 政务信访\n• 零售电商\n• 金融保险'
                },
                'config': {
                    title: '⚙️ 配置向导页面详情',
                    content: '功能特性:\n• 分步骤引导\n• AI智能建议\n• 实时配置预览\n• 自动保存机制\n• 配置验证\n\n配置内容:\n• 基础信息设置\n• 数据源配置\n• 字段映射\n• 业务规则\n• 系统发布'
                },
                'dashboard': {
                    title: '📊 数据分析仪表板详情',
                    content: '功能特性:\n• 实时数据展示\n• 交互式图表\n• AI洞察面板\n• 快速操作按钮\n• 响应式布局\n\n数据模块:\n• 关键指标统计\n• 情感分析图表\n• 实时问题监控\n• 趋势分析\n• 智能建议'
                },
                'reports': {
                    title: '📈 报表分析页面详情',
                    content: '功能特性:\n• 多维度筛选\n• 多种图表类型\n• 数据导出功能\n• 钻取分析\n• 实时数据更新\n\n分析能力:\n• 趋势分析\n• 对比分析\n• 分布分析\n• 关联分析\n• 预测分析'
                },
                'insights': {
                    title: '🔍 深度洞察页面详情',
                    content: '功能特性:\n• AI洞察生成\n• 智能问题发现\n• 趋势预测\n• 机会识别\n• 自动化建议\n\nAI能力:\n• 情感分析\n• 文本挖掘\n• 聚类分析\n• 异常检测\n• 模式识别'
                },
                'admin': {
                    title: '🛡️ 系统管理页面详情',
                    content: '功能特性:\n• 用户账户管理\n• 权限配置系统\n• 系统参数设置\n• 操作日志审计\n• 数据备份\n\n管理功能:\n• 用户增删改查\n• 角色权限分配\n• 系统监控\n• 安全设置\n• 备份恢复'
                }
            };

            const info = pageInfos[pageType];
            if (info) {
                alert(info.title + '\n\n' + info.content);
            }
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 头部动画
            const header = document.querySelector('.header');
            header.style.opacity = '0';
            header.style.transform = 'translateY(-30px)';
            
            setTimeout(() => {
                header.style.transition = 'all 0.8s ease';
                header.style.opacity = '1';
                header.style.transform = 'translateY(0)';
            }, 200);

            // 信息区域动画
            const infoSections = document.querySelectorAll('.info-section');
            infoSections.forEach((section, index) => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    section.style.transition = 'all 0.6s ease';
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, 1000 + index * 200);
            });
        });

        // 卡片点击效果
        document.querySelectorAll('.page-card').forEach(card => {
            card.addEventListener('click', function(e) {
                // 阻止子元素的点击事件冒泡
                if (e.target.tagName === 'A' || e.target.tagName === 'BUTTON') {
                    return;
                }
                
                // 添加点击动画
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // 统计数字动画
        function animateNumbers() {
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(num => {
                const finalValue = num.textContent;
                if (!isNaN(finalValue)) {
                    let currentValue = 0;
                    const increment = Math.ceil(finalValue / 20);
                    const timer = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= finalValue) {
                            num.textContent = finalValue;
                            clearInterval(timer);
                        } else {
                            num.textContent = currentValue;
                        }
                    }, 50);
                }
            });
        }

        // 延迟执行数字动画
        setTimeout(animateNumbers, 1500);
    </script>
</body>
</html> 