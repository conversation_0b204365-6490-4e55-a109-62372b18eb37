<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VOC系统 - 行业选择页面原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
        }

        .logo {
            font-size: 32px;
            font-weight: 700;
            color: white;
            margin-bottom: 16px;
        }

        .subtitle {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 8px;
        }

        .description {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            max-width: 600px;
            margin: 0 auto;
        }

        .industry-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
            margin-bottom: 40px;
        }

        .industry-card {
            background: white;
            border-radius: 16px;
            padding: 32px 24px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .industry-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .industry-card.configured {
            border: 2px solid #52c41a;
        }

        .industry-card.ai-recommended {
            border: 2px solid #1890ff;
        }

        .industry-card.ai-recommended::before {
            content: "AI推荐";
            position: absolute;
            top: 12px;
            right: 12px;
            background: #1890ff;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .industry-card.configured::before {
            content: "已配置";
            position: absolute;
            top: 12px;
            right: 12px;
            background: #52c41a;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .industry-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            color: white;
        }

        .industry-icon.telecom { background: #ff7a45; }
        .industry-icon.automotive { background: #52c41a; }
        .industry-icon.beauty { background: #eb2f96; }
        .industry-icon.petition { background: #722ed1; }
        .industry-icon.retail { background: #13c2c2; }
        .industry-icon.finance { background: #fa8c16; }

        .industry-title {
            font-size: 20px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
        }

        .industry-desc {
            font-size: 14px;
            color: #8c8c8c;
            margin-bottom: 16px;
            line-height: 1.5;
        }

        .industry-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 16px;
            font-weight: 600;
            color: #1890ff;
        }

        .stat-label {
            font-size: 12px;
            color: #8c8c8c;
        }

        .config-status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        .config-status.complete {
            background: #f6ffed;
            color: #52c41a;
        }

        .config-status.partial {
            background: #fff7e6;
            color: #fa8c16;
        }

        .config-status.none {
            background: #f5f5f5;
            color: #8c8c8c;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
            margin-top: 40px;
        }

        .btn {
            padding: 12px 32px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background: #40a9ff;
        }

        .btn-secondary {
            background: white;
            color: #1890ff;
            border: 1px solid #1890ff;
        }

        .btn-secondary:hover {
            background: #f0f9ff;
        }

        .ai-assistant {
            position: fixed;
            bottom: 24px;
            right: 24px;
            width: 56px;
            height: 56px;
            background: #1890ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(24, 144, 255, 0.3);
            z-index: 1000;
        }

        .ai-assistant:hover {
            background: #40a9ff;
            transform: scale(1.1);
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #f0f0f0;
            border-radius: 3px;
            margin-top: 12px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #52c41a, #73d13d);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .industry-grid {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }

        .tooltip {
            position: relative;
        }

        .tooltip:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: #262626;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🎯 通用VOC分析系统</div>
            <div class="subtitle">AI驱动的多行业客户声音分析平台</div>
            <div class="description">
                一套系统，适配多个行业。通过智能配置快速部署不同行业的VOC分析解决方案，
                助力企业深度洞察客户需求，提升服务质量和客户满意度。
            </div>
        </div>

        <div class="industry-grid">
            <!-- 手机行业 -->
            <div class="industry-card configured" onclick="selectIndustry('telecom')">
                <div class="industry-icon telecom">📱</div>
                <div class="industry-title">手机通讯行业</div>
                <div class="industry-desc">智能手机、通讯设备客户反馈分析，产品体验优化</div>
                <div class="industry-stats">
                    <div class="stat-item">
                        <div class="stat-number">15万+</div>
                        <div class="stat-label">月活用户</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">98%</div>
                        <div class="stat-label">配置完成</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">4.8</div>
                        <div class="stat-label">满意度</div>
                    </div>
                </div>
                <div class="config-status complete">
                    ✅ 配置完整，可直接使用
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 98%"></div>
                </div>
            </div>

            <!-- 汽车行业 -->
            <div class="industry-card ai-recommended" onclick="selectIndustry('automotive')">
                <div class="industry-icon automotive">🚗</div>
                <div class="industry-title">汽车制造行业</div>
                <div class="industry-desc">汽车销售、售后服务反馈分析，品牌口碑监测</div>
                <div class="industry-stats">
                    <div class="stat-item">
                        <div class="stat-number">8万+</div>
                        <div class="stat-label">车主用户</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">75%</div>
                        <div class="stat-label">配置完成</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">4.6</div>
                        <div class="stat-label">满意度</div>
                    </div>
                </div>
                <div class="config-status partial">
                    ⚡ 智能配置中，预计2小时完成
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 75%"></div>
                </div>
            </div>

            <!-- 美妆行业 -->
            <div class="industry-card configured" onclick="selectIndustry('beauty')">
                <div class="industry-icon beauty">💄</div>
                <div class="industry-title">美妆护肤行业</div>
                <div class="industry-desc">化妆品用户体验、护肤效果反馈分析</div>
                <div class="industry-stats">
                    <div class="stat-item">
                        <div class="stat-number">22万+</div>
                        <div class="stat-label">用户评价</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">96%</div>
                        <div class="stat-label">配置完成</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">4.7</div>
                        <div class="stat-label">满意度</div>
                    </div>
                </div>
                <div class="config-status complete">
                    ✅ 配置完整，可直接使用
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 96%"></div>
                </div>
            </div>

            <!-- 信访行业 -->
            <div class="industry-card" onclick="selectIndustry('petition')">
                <div class="industry-icon petition">📋</div>
                <div class="industry-title">政务信访行业</div>
                <div class="industry-desc">市民投诉、建议分析，政务服务质量提升</div>
                <div class="industry-stats">
                    <div class="stat-item">
                        <div class="stat-number">5万+</div>
                        <div class="stat-label">信访案例</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">0%</div>
                        <div class="stat-label">配置完成</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">-</div>
                        <div class="stat-label">待配置</div>
                    </div>
                </div>
                <div class="config-status none">
                    🚀 点击开始配置
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
            </div>

            <!-- 零售行业 -->
            <div class="industry-card configured" onclick="selectIndustry('retail')">
                <div class="industry-icon retail">🛍️</div>
                <div class="industry-title">零售电商行业</div>
                <div class="industry-desc">购物体验、商品评价、物流服务反馈分析</div>
                <div class="industry-stats">
                    <div class="stat-item">
                        <div class="stat-number">85万+</div>
                        <div class="stat-label">订单评价</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">94%</div>
                        <div class="stat-label">配置完成</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">4.5</div>
                        <div class="stat-label">满意度</div>
                    </div>
                </div>
                <div class="config-status complete">
                    ✅ 配置完整，可直接使用
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 94%"></div>
                </div>
            </div>

            <!-- 金融行业 -->
            <div class="industry-card" onclick="selectIndustry('finance')">
                <div class="industry-icon finance">💰</div>
                <div class="industry-title">金融保险行业</div>
                <div class="industry-desc">客户服务体验、产品满意度、风险偏好分析</div>
                <div class="industry-stats">
                    <div class="stat-item">
                        <div class="stat-number">25万+</div>
                        <div class="stat-label">客户反馈</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">0%</div>
                        <div class="stat-label">配置完成</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">-</div>
                        <div class="stat-label">待配置</div>
                    </div>
                </div>
                <div class="config-status none">
                    🚀 点击开始配置
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <button class="btn btn-primary" onclick="startConfiguration()">
                🎯 开始智能配置
            </button>
            <button class="btn btn-secondary" onclick="createCustomIndustry()">
                ⚙️ 自定义行业配置
            </button>
        </div>
    </div>

    <div class="ai-assistant tooltip" data-tooltip="AI智能助手" onclick="openAIAssistant()">
        🤖
    </div>

    <script>
        function selectIndustry(industry) {
            // 移除所有卡片的选中状态
            document.querySelectorAll('.industry-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // 添加选中状态
            event.currentTarget.classList.add('selected');
            
            // 根据配置状态决定下一步操作
            const card = event.currentTarget;
            const configStatus = card.querySelector('.config-status');
            
            if (configStatus.classList.contains('complete')) {
                // 已配置完成，直接进入系统
                setTimeout(() => {
                    alert(`正在进入${card.querySelector('.industry-title').textContent}VOC分析系统...`);
                    window.location.href = `../03-核心功能/数据分析仪表板原型.html?industry=${industry}`;
                }, 500);
            } else if (configStatus.classList.contains('partial')) {
                // 配置进行中，查看配置进度
                setTimeout(() => {
                    alert(`${card.querySelector('.industry-title').textContent}配置进行中，是否查看配置进度？`);
                    window.location.href = `配置向导页面原型.html?industry=${industry}`;
                }, 500);
            } else {
                // 未配置，开始配置流程
                setTimeout(() => {
                    if (confirm(`开始配置${card.querySelector('.industry-title').textContent}VOC分析系统？\n\n系统将为您智能推荐最佳配置方案。`)) {
                        window.location.href = `配置向导页面原型.html?industry=${industry}`;
                    }
                }, 500);
            }
        }

        function startConfiguration() {
            // 检查是否选择了行业
            const selectedCard = document.querySelector('.industry-card.selected');
            if (!selectedCard) {
                alert('请先选择一个行业');
                return;
            }
            
            const industryTitle = selectedCard.querySelector('.industry-title').textContent;
            const configStatus = selectedCard.querySelector('.config-status');
            
            if (configStatus.classList.contains('complete')) {
                alert(`${industryTitle}已配置完成，正在进入系统...`);
                window.location.href = '../03-核心功能/数据分析仪表板原型.html';
            } else {
                alert(`开始${industryTitle}智能配置向导...`);
                window.location.href = '配置向导页面原型.html';
            }
        }

        function createCustomIndustry() {
            if (confirm('创建自定义行业配置？\n\n您可以基于现有行业模板进行定制，或从零开始创建全新配置。')) {
                window.location.href = '配置向导页面原型.html?mode=custom';
            }
        }

        function openAIAssistant() {
            alert('AI智能助手：\n\n👋 您好！我是VOC系统智能助手。\n\n🎯 我可以帮您：\n• 选择最适合的行业配置\n• 解答配置过程中的问题\n• 提供最佳实践建议\n• 优化系统配置参数\n\n有什么可以帮助您的吗？');
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.industry-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // 实时更新配置进度（模拟）
        setInterval(() => {
            const partialCards = document.querySelectorAll('.config-status.partial');
            partialCards.forEach(status => {
                const progressBar = status.parentNode.querySelector('.progress-fill');
                const currentWidth = parseInt(progressBar.style.width);
                if (currentWidth < 100) {
                    const newWidth = Math.min(currentWidth + Math.random() * 2, 100);
                    progressBar.style.width = newWidth + '%';
                    
                    const statNumber = status.parentNode.querySelector('.stat-number');
                    statNumber.textContent = Math.round(newWidth) + '%';
                    
                    if (newWidth >= 100) {
                        status.className = 'config-status complete';
                        status.innerHTML = '✅ 配置完整，可直接使用';
                        status.parentNode.classList.add('configured');
                        status.parentNode.classList.remove('ai-recommended');
                    }
                }
            });
        }, 3000);
    </script>
</body>
</html> 