<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VOC系统 - 智能配置向导</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f2f5;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 头部导航 */
        .header {
            background: white;
            border-bottom: 1px solid #e8e8e8;
            padding: 16px 32px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 20px;
            font-weight: 600;
            color: #1890ff;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: 1px solid #d9d9d9;
            background: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .btn-primary {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background: #40a9ff;
        }

        /* 进度条 */
        .progress-section {
            background: white;
            border-bottom: 1px solid #e8e8e8;
            padding: 24px 32px;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .progress-title {
            font-size: 24px;
            font-weight: 600;
            color: #262626;
        }

        .progress-subtitle {
            color: #8c8c8c;
            font-size: 14px;
        }

        .steps {
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            position: relative;
        }

        .step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 20px;
            left: 60%;
            right: -40%;
            height: 2px;
            background: #e8e8e8;
            z-index: 1;
        }

        .step.completed::after {
            background: #52c41a;
        }

        .step.active::after {
            background: linear-gradient(90deg, #1890ff 50%, #e8e8e8 50%);
        }

        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e8e8e8;
            color: #8c8c8c;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-bottom: 8px;
            z-index: 2;
            position: relative;
        }

        .step.completed .step-circle {
            background: #52c41a;
            color: white;
        }

        .step.active .step-circle {
            background: #1890ff;
            color: white;
        }

        .step-label {
            font-size: 14px;
            color: #8c8c8c;
            text-align: center;
        }

        .step.completed .step-label,
        .step.active .step-label {
            color: #262626;
            font-weight: 500;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            display: flex;
            padding: 32px;
            gap: 32px;
        }

        .config-panel {
            flex: 2;
            background: white;
            border-radius: 8px;
            border: 1px solid #e8e8e8;
            padding: 32px;
        }

        .preview-panel {
            flex: 1;
            background: white;
            border-radius: 8px;
            border: 1px solid #e8e8e8;
            padding: 24px;
        }

        .panel-title {
            font-size: 20px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 配置表单 */
        .config-section {
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .ai-suggestion {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 16px;
        }

        .ai-suggestion-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-weight: 500;
            color: #1890ff;
        }

        .ai-suggestion-content {
            font-size: 14px;
            color: #262626;
            line-height: 1.5;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #262626;
            margin-bottom: 8px;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-control:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .checkbox-item:hover {
            border-color: #1890ff;
            background: #f0f9ff;
        }

        .checkbox-item.selected {
            border-color: #1890ff;
            background: #e6f7ff;
        }

        /* 预览面板 */
        .preview-section {
            margin-bottom: 24px;
        }

        .preview-title {
            font-size: 14px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 12px;
        }

        .preview-content {
            background: #fafafa;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 16px;
            font-size: 14px;
            line-height: 1.5;
        }

        .preview-chart {
            height: 200px;
            background: #fafafa;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #8c8c8c;
            font-size: 14px;
        }

        /* 底部操作区 */
        .footer-actions {
            background: white;
            border-top: 1px solid #e8e8e8;
            padding: 16px 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .action-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .action-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .btn-large {
            padding: 12px 24px;
            font-size: 16px;
        }

        /* AI助手悬浮窗 */
        .ai-assistant {
            position: fixed;
            bottom: 24px;
            right: 24px;
            width: 56px;
            height: 56px;
            background: #1890ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(24, 144, 255, 0.3);
            z-index: 1000;
        }

        .validation-message {
            font-size: 12px;
            margin-top: 4px;
        }

        .validation-success {
            color: #52c41a;
        }

        .validation-error {
            color: #ff4d4f;
        }

        .validation-warning {
            color: #fa8c16;
        }

        @media (max-width: 1200px) {
            .main-content {
                flex-direction: column;
            }
            
            .preview-panel {
                order: -1;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <div class="logo">
                🎯 VOC智能配置向导
            </div>
            <div class="header-actions">
                <button class="btn" onclick="saveAsDraft()">💾 保存草稿</button>
                <button class="btn" onclick="previewConfig()">👁️ 预览配置</button>
                <button class="btn" onclick="exitWizard()">❌ 退出向导</button>
            </div>
        </div>

        <!-- 进度条 -->
        <div class="progress-section">
            <div class="progress-header">
                <div>
                    <div class="progress-title">手机通讯行业 VOC配置向导</div>
                    <div class="progress-subtitle">第2步，共5步 - 数据源配置</div>
                </div>
                <div style="color: #8c8c8c; font-size: 14px;">
                    预计完成时间: 15分钟 | 已用时: 3分钟
                </div>
            </div>
            <div class="steps">
                <div class="step completed">
                    <div class="step-circle">✓</div>
                    <div class="step-label">基础信息</div>
                </div>
                <div class="step active">
                    <div class="step-circle">2</div>
                    <div class="step-label">数据源配置</div>
                </div>
                <div class="step">
                    <div class="step-circle">3</div>
                    <div class="step-label">字段映射</div>
                </div>
                <div class="step">
                    <div class="step-circle">4</div>
                    <div class="step-label">业务规则</div>
                </div>
                <div class="step">
                    <div class="step-circle">5</div>
                    <div class="step-label">预览发布</div>
                </div>
            </div>
        </div>

        <!-- 主内容 -->
        <div class="main-content">
            <!-- 配置面板 -->
            <div class="config-panel">
                <div class="panel-title">
                    📊 数据源配置
                </div>

                <!-- AI建议 -->
                <div class="ai-suggestion">
                    <div class="ai-suggestion-header">
                        🤖 AI智能建议
                    </div>
                    <div class="ai-suggestion-content">
                        根据手机通讯行业特点，建议接入以下数据源：客服通话录音、在线客服对话、用户评价、社交媒体提及、应用商店评分。
                        这些数据源将帮助您全面了解用户体验和产品反馈。
                    </div>
                </div>

                <!-- 数据源类型选择 -->
                <div class="config-section">
                    <div class="section-title">
                        🔌 选择数据源类型
                    </div>
                    <div class="checkbox-group">
                        <div class="checkbox-item selected" onclick="toggleDataSource(this)">
                            <input type="checkbox" checked>
                            <div>
                                <div style="font-weight: 500;">📞 客服通话录音</div>
                                <div style="font-size: 12px; color: #8c8c8c;">语音转文本，情感分析</div>
                            </div>
                        </div>
                        <div class="checkbox-item selected" onclick="toggleDataSource(this)">
                            <input type="checkbox" checked>
                            <div>
                                <div style="font-weight: 500;">💬 在线客服对话</div>
                                <div style="font-size: 12px; color: #8c8c8c;">实时文本对话记录</div>
                            </div>
                        </div>
                        <div class="checkbox-item selected" onclick="toggleDataSource(this)">
                            <input type="checkbox" checked>
                            <div>
                                <div style="font-weight: 500;">⭐ 用户评价反馈</div>
                                <div style="font-size: 12px; color: #8c8c8c;">产品评分和文字评价</div>
                            </div>
                        </div>
                        <div class="checkbox-item" onclick="toggleDataSource(this)">
                            <input type="checkbox">
                            <div>
                                <div style="font-weight: 500;">📱 社交媒体</div>
                                <div style="font-size: 12px; color: #8c8c8c;">微博、微信、抖音提及</div>
                            </div>
                        </div>
                        <div class="checkbox-item selected" onclick="toggleDataSource(this)">
                            <input type="checkbox" checked>
                            <div>
                                <div style="font-weight: 500;">📋 问卷调研</div>
                                <div style="font-size: 12px; color: #8c8c8c;">用户满意度调研</div>
                            </div>
                        </div>
                        <div class="checkbox-item" onclick="toggleDataSource(this)">
                            <input type="checkbox">
                            <div>
                                <div style="font-weight: 500;">📧 邮件反馈</div>
                                <div style="font-size: 12px; color: #8c8c8c;">客户邮件投诉建议</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据接入方式 -->
                <div class="config-section">
                    <div class="section-title">
                        🔗 数据接入方式
                    </div>
                    <div class="form-group">
                        <label class="form-label">主要接入方式</label>
                        <select class="form-control" onchange="updateDataMethod(this.value)">
                            <option value="api">API实时接入 (推荐)</option>
                            <option value="batch">批量文件导入</option>
                            <option value="database">数据库直连</option>
                            <option value="webhook">Webhook推送</option>
                        </select>
                        <div class="validation-message validation-success">
                            ✅ API接入方式已验证，支持实时数据同步
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">数据同步频率</label>
                        <select class="form-control">
                            <option value="realtime">实时同步 (推荐)</option>
                            <option value="5min">每5分钟</option>
                            <option value="15min">每15分钟</option>
                            <option value="hourly">每小时</option>
                            <option value="daily">每日</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">数据格式</label>
                        <div class="checkbox-group">
                            <div class="checkbox-item selected">
                                <input type="checkbox" checked>
                                <span>JSON</span>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox">
                                <span>XML</span>
                            </div>
                            <div class="checkbox-item selected">
                                <input type="checkbox" checked>
                                <span>CSV</span>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox">
                                <span>Excel</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据质量要求 -->
                <div class="config-section">
                    <div class="section-title">
                        🛡️ 数据质量控制
                    </div>
                    <div class="form-group">
                        <label class="form-label">数据完整性要求</label>
                        <select class="form-control">
                            <option value="strict">严格模式 - 必须包含所有必填字段</option>
                            <option value="normal" selected>标准模式 - 允许部分字段为空</option>
                            <option value="loose">宽松模式 - 尽可能接收数据</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">重复数据处理</label>
                        <select class="form-control">
                            <option value="skip" selected>跳过重复数据</option>
                            <option value="update">更新已有数据</option>
                            <option value="merge">合并重复数据</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 预览面板 -->
            <div class="preview-panel">
                <div class="panel-title">
                    👁️ 实时预览
                </div>

                <!-- 数据源概览 -->
                <div class="preview-section">
                    <div class="preview-title">数据源配置概览</div>
                    <div class="preview-content">
                        <div style="margin-bottom: 12px;"><strong>已选择数据源:</strong> 4个</div>
                        <div style="margin-bottom: 8px;">• 📞 客服通话录音</div>
                        <div style="margin-bottom: 8px;">• 💬 在线客服对话</div>
                        <div style="margin-bottom: 8px;">• ⭐ 用户评价反馈</div>
                        <div style="margin-bottom: 12px;">• 📋 问卷调研</div>
                        <div style="color: #52c41a; font-size: 12px;">
                            ✅ 覆盖度评分: 85/100 (优秀)
                        </div>
                    </div>
                </div>

                <!-- 预计数据量 -->
                <div class="preview-section">
                    <div class="preview-title">预计数据量</div>
                    <div class="preview-content">
                        <div style="margin-bottom: 8px;">📊 月均数据量: ~15万条</div>
                        <div style="margin-bottom: 8px;">⚡ 实时数据量: ~500条/小时</div>
                        <div style="margin-bottom: 8px;">💾 存储需求: ~2.5GB/月</div>
                        <div style="color: #1890ff; font-size: 12px;">
                            ℹ️ 基于同行业平均水平估算
                        </div>
                    </div>
                </div>

                <!-- 配置有效性 -->
                <div class="preview-section">
                    <div class="preview-title">配置验证结果</div>
                    <div class="preview-content">
                        <div style="margin-bottom: 8px; color: #52c41a;">✅ 数据源连接正常</div>
                        <div style="margin-bottom: 8px; color: #52c41a;">✅ 接入方式配置正确</div>
                        <div style="margin-bottom: 8px; color: #fa8c16;">⚠️ 建议增加社交媒体数据源</div>
                        <div style="color: #52c41a;">✅ 质量控制策略合理</div>
                    </div>
                </div>

                <!-- 示例数据预览 -->
                <div class="preview-section">
                    <div class="preview-title">示例数据预览</div>
                    <div class="preview-chart">
                        📈 数据流监控图表<br/>
                        <small>配置完成后显示实时数据</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部操作 -->
        <div class="footer-actions">
            <div class="action-left">
                <div style="font-size: 14px; color: #8c8c8c;">
                    💾 配置已自动保存 | 上次保存: 2分钟前
                </div>
            </div>
            <div class="action-right">
                <button class="btn" onclick="previousStep()">← 上一步</button>
                <button class="btn btn-primary btn-large" onclick="nextStep()">下一步: 字段映射 →</button>
            </div>
        </div>
    </div>

    <!-- AI助手 -->
    <div class="ai-assistant" onclick="openAIAssistant()">
        🤖
    </div>

    <script>
        function toggleDataSource(element) {
            const checkbox = element.querySelector('input[type="checkbox"]');
            checkbox.checked = !checkbox.checked;
            
            if (checkbox.checked) {
                element.classList.add('selected');
            } else {
                element.classList.remove('selected');
            }
            
            updatePreview();
        }

        function updateDataMethod(method) {
            const validationMsg = document.querySelector('.validation-message');
            switch(method) {
                case 'api':
                    validationMsg.className = 'validation-message validation-success';
                    validationMsg.innerHTML = '✅ API接入方式已验证，支持实时数据同步';
                    break;
                case 'batch':
                    validationMsg.className = 'validation-message validation-warning';
                    validationMsg.innerHTML = '⚠️ 批量导入模式，建议同时配置API接入以获得实时性';
                    break;
                case 'database':
                    validationMsg.className = 'validation-message validation-success';
                    validationMsg.innerHTML = '✅ 数据库直连已配置，支持定时同步';
                    break;
                case 'webhook':
                    validationMsg.className = 'validation-message validation-warning';
                    validationMsg.innerHTML = '⚠️ Webhook模式需要确保推送端的稳定性';
                    break;
            }
            updatePreview();
        }

        function updatePreview() {
            // 实时更新预览面板内容
            const selectedSources = document.querySelectorAll('.checkbox-item.selected').length;
            const overviewContent = document.querySelector('.preview-content');
            
            // 更新数据源数量
            const sourceCountElement = overviewContent.querySelector('div');
            sourceCountElement.innerHTML = `<strong>已选择数据源:</strong> ${selectedSources}个`;
            
            // 更新覆盖度评分
            const coverage = Math.min(selectedSources * 20, 100);
            const coverageElement = overviewContent.querySelector('.preview-content div:last-child');
            let coverageStatus = coverage >= 80 ? '优秀' : coverage >= 60 ? '良好' : '需改进';
            let coverageColor = coverage >= 80 ? '#52c41a' : coverage >= 60 ? '#fa8c16' : '#ff4d4f';
            
            coverageElement.style.color = coverageColor;
            coverageElement.innerHTML = `✅ 覆盖度评分: ${coverage}/100 (${coverageStatus})`;
        }

        function previousStep() {
            if (confirm('返回上一步？当前配置将自动保存。')) {
                window.location.href = '行业选择页面原型.html';
            }
        }

        function nextStep() {
            // 验证配置
            const selectedSources = document.querySelectorAll('.checkbox-item.selected').length;
            if (selectedSources < 2) {
                alert('请至少选择2个数据源以确保分析的全面性。');
                return;
            }
            
            // 显示进度
            const progressBar = document.createElement('div');
            progressBar.style.cssText = `
                position: fixed; top: 0; left: 0; right: 0; height: 4px; 
                background: #1890ff; z-index: 9999;
                transition: width 2s ease;
            `;
            document.body.appendChild(progressBar);
            
            // 模拟保存过程
            setTimeout(() => {
                alert('✅ 数据源配置已保存！\n\n配置即将完成，正在跳转到系统仪表板...');
                window.location.href = '../03-核心功能/数据分析仪表板原型.html';
            }, 1000);
        }

        function saveAsDraft() {
            const saveBtn = event.target;
            saveBtn.innerHTML = '💾 保存中...';
            saveBtn.disabled = true;
            
            setTimeout(() => {
                saveBtn.innerHTML = '✅ 已保存';
                setTimeout(() => {
                    saveBtn.innerHTML = '💾 保存草稿';
                    saveBtn.disabled = false;
                }, 1000);
            }, 800);
        }

        function previewConfig() {
            alert('🔍 配置预览\n\n当前配置将生成以下分析能力：\n\n📊 实时客户情感监控\n🎯 产品问题智能识别\n📈 用户满意度趋势分析\n⚡ 客服质量评估\n\n配置完成度: 40%');
        }

        function exitWizard() {
            if (confirm('确定退出配置向导？\n\n当前进度将自动保存，您可以稍后继续配置。')) {
                window.location.href = '行业选择页面原型.html';
            }
        }

        function openAIAssistant() {
            alert('🤖 AI配置助手\n\n我注意到您正在配置手机通讯行业的数据源。\n\n💡 建议:\n• 客服通话录音是最有价值的数据源\n• 建议开启实时同步以获得及时洞察\n• 社交媒体数据可以帮助发现潜在问题\n\n需要我协助配置特定的数据源吗？');
        }

        // 页面加载时的动画效果
        document.addEventListener('DOMContentLoaded', function() {
            // 模拟配置加载过程
            const configSections = document.querySelectorAll('.config-section');
            configSections.forEach((section, index) => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    section.style.transition = 'all 0.5s ease';
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, index * 200);
            });

            // 自动保存功能
            setInterval(() => {
                const saveStatus = document.querySelector('.action-left div');
                const now = new Date();
                const timeStr = now.toLocaleTimeString();
                saveStatus.innerHTML = `💾 配置已自动保存 | 上次保存: ${timeStr}`;
            }, 30000);
        });
    </script>
</body>
</html> 