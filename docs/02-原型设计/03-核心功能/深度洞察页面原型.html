<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VOC系统 - 深度洞察</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f2f5;
            min-height: 100vh;
        }

        /* 顶部导航 */
        .header {
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 0 24px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 64px;
            max-width: 1600px;
            margin: 0 auto;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 20px;
            font-weight: 600;
            color: #1890ff;
            cursor: pointer;
        }

        .nav-menu {
            display: flex;
            gap: 32px;
        }

        .nav-item {
            padding: 8px 16px;
            color: #262626;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.2s;
            cursor: pointer;
        }

        .nav-item:hover {
            background: #e6f7ff;
            color: #1890ff;
        }

        .nav-item.active {
            background: #1890ff;
            color: white;
        }

        .nav-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .industry-selector {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            background: #f0f9ff;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 6px;
            transition: background 0.2s;
        }

        .user-menu:hover {
            background: #f5f5f5;
        }

        /* 主容器 */
        .main-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 24px;
        }

        /* AI洞察横幅 */
        .ai-banner {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 32px;
            margin-bottom: 24px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .ai-banner::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20px;
            width: 100px;
            height: 200%;
            background: rgba(255, 255, 255, 0.1);
            transform: rotate(15deg);
        }

        .banner-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .banner-text h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .banner-text p {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .ai-stats {
            display: flex;
            gap: 32px;
        }

        .ai-stat {
            text-align: center;
        }

        .ai-stat-value {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .ai-stat-label {
            font-size: 12px;
            opacity: 0.8;
        }

        .generate-insights-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.2s;
        }

        .generate-insights-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        /* 洞察网格 */
        .insights-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }

        .insight-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .insight-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .insight-header {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            padding: 20px 24px;
            color: white;
        }

        .insight-header.trend {
            background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
        }

        .insight-header.problem {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #8b4513;
        }

        .insight-header.opportunity {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #2c5282;
        }

        .insight-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .insight-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }

        .insight-content {
            padding: 24px;
        }

        .insight-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            position: relative;
        }

        .insight-item:last-child {
            margin-bottom: 0;
        }

        .insight-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .insight-type {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .insight-type.critical {
            background: #fff2f0;
            color: #ff4d4f;
        }

        .insight-type.important {
            background: #fff7e6;
            color: #fa8c16;
        }

        .insight-type.normal {
            background: #f6ffed;
            color: #52c41a;
        }

        .confidence-score {
            font-size: 12px;
            color: #8c8c8c;
        }

        .insight-text {
            font-size: 14px;
            line-height: 1.6;
            color: #262626;
            margin-bottom: 12px;
        }

        .insight-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }

        .action-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        /* 词云分析 */
        .wordcloud-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .wordcloud-container {
            height: 300px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .word-item {
            position: absolute;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .word-item:hover {
            transform: scale(1.2);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .word-large {
            font-size: 32px;
            color: #1890ff;
        }

        .word-medium {
            font-size: 24px;
            color: #52c41a;
        }

        .word-small {
            font-size: 16px;
            color: #fa8c16;
        }

        /* 情感分析时间线 */
        .timeline-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .timeline {
            position: relative;
            margin: 20px 0;
        }

        .timeline::before {
            content: '';
            position: absolute;
            top: 0;
            left: 30px;
            height: 100%;
            width: 2px;
            background: #e8e8e8;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 24px;
            padding-left: 70px;
        }

        .timeline-icon {
            position: absolute;
            left: 20px;
            top: 4px;
            width: 20px;
            height: 20px;
            background: #1890ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            z-index: 2;
        }

        .timeline-icon.positive {
            background: #52c41a;
        }

        .timeline-icon.negative {
            background: #ff4d4f;
        }

        .timeline-icon.neutral {
            background: #fa8c16;
        }

        .timeline-content {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
        }

        .timeline-date {
            font-size: 12px;
            color: #8c8c8c;
            margin-bottom: 4px;
        }

        .timeline-title {
            font-size: 14px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
        }

        .timeline-desc {
            font-size: 14px;
            color: #595959;
            line-height: 1.5;
        }

        /* AI建议面板 */
        .recommendations-panel {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .recommendation-item {
            background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
            border: 1px solid #91d5ff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 16px;
            position: relative;
        }

        .recommendation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .recommendation-title {
            font-size: 16px;
            font-weight: 600;
            color: #1890ff;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .priority-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .priority-high {
            background: #fff2f0;
            color: #ff4d4f;
        }

        .priority-medium {
            background: #fff7e6;
            color: #fa8c16;
        }

        .priority-low {
            background: #f6ffed;
            color: #52c41a;
        }

        .recommendation-desc {
            font-size: 14px;
            color: #262626;
            line-height: 1.6;
            margin-bottom: 16px;
        }

        .recommendation-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: 1px solid #d9d9d9;
            background: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .btn-primary {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background: #40a9ff;
        }

        /* 响应式 */
        @media (max-width: 1200px) {
            .insights-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .main-container {
                padding: 16px;
            }
            
            .banner-content {
                flex-direction: column;
                text-align: center;
            }
            
            .ai-stats {
                margin-top: 20px;
            }
        }

        /* 加载动画 */
        .loading-animation {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="header">
        <div class="nav-container">
            <div class="logo" onclick="goToHome()">
                🎯 VOC数据分析平台
            </div>
            <div class="nav-menu">
                <a href="#" class="nav-item" onclick="goToDashboard()">📊 仪表板</a>
                <a href="#" class="nav-item" onclick="goToReports()">📈 报表分析</a>
                <a href="#" class="nav-item active">🔍 深度洞察</a>
                <a href="#" class="nav-item" onclick="goToSettings()">⚙️ 系统配置</a>
            </div>
            <div class="nav-actions">
                <div class="industry-selector" onclick="changeIndustry()">
                    📱 手机通讯行业 ▼
                </div>
                <div class="user-menu" onclick="openUserMenu()">
                    👤 张三 ▼
                </div>
            </div>
        </div>
    </div>

    <div class="main-container">
        <!-- AI洞察横幅 -->
        <div class="ai-banner">
            <div class="banner-content">
                <div class="banner-text">
                    <h1>🤖 AI深度洞察中心</h1>
                    <p>基于机器学习算法的智能分析，为您发现隐藏的客户洞察和商业机会</p>
                    <button class="generate-insights-btn" onclick="generateNewInsights()">
                        ⚡ 生成新洞察
                    </button>
                </div>
                <div class="ai-stats">
                    <div class="ai-stat">
                        <div class="ai-stat-value">127</div>
                        <div class="ai-stat-label">AI洞察</div>
                    </div>
                    <div class="ai-stat">
                        <div class="ai-stat-value">95%</div>
                        <div class="ai-stat-label">准确率</div>
                    </div>
                    <div class="ai-stat">
                        <div class="ai-stat-value">24h</div>
                        <div class="ai-stat-label">更新频率</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 洞察网格 -->
        <div class="insights-grid">
            <!-- 关键发现 -->
            <div class="insight-card">
                <div class="insight-header">
                    <div class="insight-title">
                        🎯 关键发现
                    </div>
                    <div class="insight-subtitle">基于数据挖掘的重要发现</div>
                </div>
                <div class="insight-content">
                    <div class="insight-item">
                        <div class="insight-meta">
                            <span class="insight-type critical">紧急</span>
                            <span class="confidence-score">置信度: 96%</span>
                        </div>
                        <div class="insight-text">
                            电池续航问题在旗舰机型X1中集中爆发，过去72小时内相关投诉增长312%，主要集中在使用6个月以上的设备。
                        </div>
                        <div class="insight-actions">
                            <button class="action-btn" onclick="viewInsightDetails('battery-issue')">📊 查看详情</button>
                            <button class="action-btn" onclick="createAlert('battery-issue')">🔔 创建告警</button>
                        </div>
                    </div>
                    
                    <div class="insight-item">
                        <div class="insight-meta">
                            <span class="insight-type important">重要</span>
                            <span class="confidence-score">置信度: 89%</span>
                        </div>
                        <div class="insight-text">
                            夜景拍照功能获得用户高度好评，相关正面提及增长45%，建议在营销中突出此功能优势。
                        </div>
                        <div class="insight-actions">
                            <button class="action-btn" onclick="viewInsightDetails('night-mode')">📊 查看详情</button>
                            <button class="action-btn" onclick="shareInsight('night-mode')">📤 分享洞察</button>
                        </div>
                    </div>

                    <div class="insight-item">
                        <div class="insight-meta">
                            <span class="insight-type normal">一般</span>
                            <span class="confidence-score">置信度: 78%</span>
                        </div>
                        <div class="insight-text">
                            客服响应时间在工作日晚上7-9点有明显延长，建议增加此时段人员配置。
                        </div>
                        <div class="insight-actions">
                            <button class="action-btn" onclick="viewInsightDetails('response-time')">📊 查看详情</button>
                            <button class="action-btn" onclick="implementSuggestion('response-time')">✅ 采纳建议</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 趋势预测 -->
            <div class="insight-card">
                <div class="insight-header trend">
                    <div class="insight-title">
                        📈 趋势预测
                    </div>
                    <div class="insight-subtitle">基于时间序列分析的预测</div>
                </div>
                <div class="insight-content">
                    <div class="insight-item">
                        <div class="insight-meta">
                            <span class="insight-type important">重要</span>
                            <span class="confidence-score">置信度: 91%</span>
                        </div>
                        <div class="insight-text">
                            预测未来7天客户满意度将下降至4.2分，主要由于电池问题影响，建议提前准备应对策略。
                        </div>
                        <div class="insight-actions">
                            <button class="action-btn" onclick="viewTrendAnalysis()">📈 趋势分析</button>
                            <button class="action-btn" onclick="preparePlan()">📋 制定预案</button>
                        </div>
                    </div>

                    <div class="insight-item">
                        <div class="insight-meta">
                            <span class="insight-type normal">一般</span>
                            <span class="confidence-score">置信度: 85%</span>
                        </div>
                        <div class="insight-text">
                            5G功能相关询问预计在下月增长60%，建议提前准备相关技术支持文档和FAQ。
                        </div>
                        <div class="insight-actions">
                            <button class="action-btn" onclick="prepareContent()">📝 准备内容</button>
                            <button class="action-btn" onclick="trainStaff()">👨‍🎓 培训员工</button>
                        </div>
                    </div>

                    <div class="insight-item">
                        <div class="insight-meta">
                            <span class="insight-type normal">一般</span>
                            <span class="confidence-score">置信度: 72%</span>
                        </div>
                        <div class="insight-text">
                            春节假期后预计会有客户咨询高峰，建议提前安排客服值班和工作流程。
                        </div>
                        <div class="insight-actions">
                            <button class="action-btn" onclick="scheduleStaff()">📅 排班计划</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 问题聚类 -->
            <div class="insight-card">
                <div class="insight-header problem">
                    <div class="insight-title">
                        🔧 问题聚类
                    </div>
                    <div class="insight-subtitle">智能问题分类和关联分析</div>
                </div>
                <div class="insight-content">
                    <div class="insight-item">
                        <div class="insight-meta">
                            <span class="insight-type critical">紧急</span>
                            <span class="confidence-score">关联度: 94%</span>
                        </div>
                        <div class="insight-text">
                            发现"电池续航"、"充电速度"、"发热"三类问题高度相关，可能存在共同的硬件或软件原因。
                        </div>
                        <div class="insight-actions">
                            <button class="action-btn" onclick="viewCorrelation()">🔗 查看关联</button>
                            <button class="action-btn" onclick="investigateRoot()">🔍 根因分析</button>
                        </div>
                    </div>

                    <div class="insight-item">
                        <div class="insight-meta">
                            <span class="insight-type important">重要</span>
                            <span class="confidence-score">聚类置信度: 87%</span>
                        </div>
                        <div class="insight-text">
                            识别出6个主要问题簇，其中界面卡顿和应用闪退问题占总投诉的23%，建议优先处理。
                        </div>
                        <div class="insight-actions">
                            <button class="action-btn" onclick="viewCluster()">🎯 查看簇</button>
                            <button class="action-btn" onclick="prioritizeIssues()">📋 排优先级</button>
                        </div>
                    </div>

                    <div class="insight-item">
                        <div class="insight-meta">
                            <span class="insight-type normal">一般</span>
                            <span class="confidence-score">相似度: 76%</span>
                        </div>
                        <div class="insight-text">
                            新用户和老用户反馈的问题类型存在明显差异，建议制定差异化的支持策略。
                        </div>
                        <div class="insight-actions">
                            <button class="action-btn" onclick="segmentAnalysis()">👥 用户分群</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 机会识别 -->
            <div class="insight-card">
                <div class="insight-header opportunity">
                    <div class="insight-title">
                        💡 机会识别
                    </div>
                    <div class="insight-subtitle">基于情感分析的机会发现</div>
                </div>
                <div class="insight-content">
                    <div class="insight-item">
                        <div class="insight-meta">
                            <span class="insight-type important">重要</span>
                            <span class="confidence-score">机会值: 92%</span>
                        </div>
                        <div class="insight-text">
                            用户对快充功能满意度极高(4.9分)，但宣传不足，建议加强此功能的市场推广。
                        </div>
                        <div class="insight-actions">
                            <button class="action-btn" onclick="marketingPlan()">📢 营销计划</button>
                            <button class="action-btn" onclick="contentStrategy()">📝 内容策略</button>
                        </div>
                    </div>

                    <div class="insight-item">
                        <div class="insight-meta">
                            <span class="insight-type normal">一般</span>
                            <span class="confidence-score">潜力分数: 84%</span>
                        </div>
                        <div class="insight-text">
                            检测到用户对环保包装的正面反馈显著增加，建议将此作为品牌形象宣传点。
                        </div>
                        <div class="insight-actions">
                            <button class="action-btn" onclick="brandStrategy()">🌱 品牌策略</button>
                        </div>
                    </div>

                    <div class="insight-item">
                        <div class="insight-meta">
                            <span class="insight-type normal">一般</span>
                            <span class="confidence-score">改进空间: 78%</span>
                        </div>
                        <div class="insight-text">
                            客服人员专业知识获得用户认可，建议建立知识库并推广最佳实践。
                        </div>
                        <div class="insight-actions">
                            <button class="action-btn" onclick="knowledgeBase()">📚 知识库</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 词云分析 -->
        <div class="wordcloud-section">
            <div class="section-title">
                ☁️ 关键词云分析
            </div>
            <div class="wordcloud-container" onclick="refreshWordCloud()">
                <div class="word-item word-large" style="top: 30%; left: 40%;" onclick="analyzeKeyword('电池续航')">电池续航</div>
                <div class="word-item word-medium" style="top: 50%; left: 20%;" onclick="analyzeKeyword('充电速度')">充电速度</div>
                <div class="word-item word-large" style="top: 20%; left: 70%;" onclick="analyzeKeyword('拍照效果')">拍照效果</div>
                <div class="word-item word-small" style="top: 70%; left: 60%;" onclick="analyzeKeyword('系统流畅')">系统流畅</div>
                <div class="word-item word-medium" style="top: 40%; left: 80%;" onclick="analyzeKeyword('外观设计')">外观设计</div>
                <div class="word-item word-small" style="top: 80%; left: 30%;" onclick="analyzeKeyword('客服态度')">客服态度</div>
                <div class="word-item word-medium" style="top: 60%; left: 10%;" onclick="analyzeKeyword('价格合理')">价格合理</div>
                <div class="word-item word-small" style="top: 10%; left: 20%;" onclick="analyzeKeyword('屏幕显示')">屏幕显示</div>
                <div class="word-item word-large" style="top: 45%; left: 55%;" onclick="analyzeKeyword('夜景模式')">夜景模式</div>
                <div class="word-item word-small" style="top: 25%; left: 10%;" onclick="analyzeKeyword('发热控制')">发热控制</div>
            </div>
        </div>

        <!-- 情感时间线 -->
        <div class="timeline-section">
            <div class="section-title">
                📅 情感变化时间线
            </div>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-icon positive">😊</div>
                    <div class="timeline-content">
                        <div class="timeline-date">2024-01-15 14:30</div>
                        <div class="timeline-title">夜景拍照功能好评激增</div>
                        <div class="timeline-desc">
                            检测到夜景拍照相关正面评价在过去24小时内增长45%，用户特别赞扬在弱光环境下的成像质量。
                        </div>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-icon negative">😞</div>
                    <div class="timeline-content">
                        <div class="timeline-date">2024-01-14 16:20</div>
                        <div class="timeline-title">电池续航问题爆发</div>
                        <div class="timeline-desc">
                            旗舰机型X1的电池续航投诉量激增，主要集中在使用时间超过6个月的设备，情感得分下降至2.1。
                        </div>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-icon neutral">😐</div>
                    <div class="timeline-content">
                        <div class="timeline-date">2024-01-13 10:15</div>
                        <div class="timeline-title">客服响应时间延长</div>
                        <div class="timeline-desc">
                            用户反馈客服响应时间较平时延长15秒，主要集中在晚间高峰时段，整体满意度略有下降。
                        </div>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-icon positive">😊</div>
                    <div class="timeline-content">
                        <div class="timeline-date">2024-01-12 09:30</div>
                        <div class="timeline-title">快充功能获得认可</div>
                        <div class="timeline-desc">
                            用户对快充功能的满意度达到4.9分，特别是在紧急情况下的快速充电能力获得高度评价。
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI建议面板 -->
        <div class="recommendations-panel">
            <div class="section-title">
                🎯 AI智能建议
            </div>
            
            <div class="recommendation-item">
                <div class="recommendation-header">
                    <div class="recommendation-title">
                        🚨 紧急处理建议
                    </div>
                    <span class="priority-badge priority-high">高优先级</span>
                </div>
                <div class="recommendation-desc">
                    基于当前电池续航问题的严重程度和影响范围，建议立即启动应急响应机制，组建专项技术团队进行根因分析，同时准备客户沟通方案和临时解决方案。
                </div>
                <div class="recommendation-actions">
                    <button class="btn btn-primary" onclick="implementEmergencyPlan()">🚀 立即执行</button>
                    <button class="btn" onclick="scheduleTask()">📅 安排任务</button>
                    <button class="btn" onclick="viewRecommendationDetails()">📋 查看详情</button>
                </div>
            </div>

            <div class="recommendation-item">
                <div class="recommendation-header">
                    <div class="recommendation-title">
                        📈 营销优化建议
                    </div>
                    <span class="priority-badge priority-medium">中优先级</span>
                </div>
                <div class="recommendation-desc">
                    夜景拍照和快充功能获得用户高度认可，建议在下一轮营销活动中重点突出这两个功能优势，预计可提升产品吸引力15-20%。
                </div>
                <div class="recommendation-actions">
                    <button class="btn btn-primary" onclick="createMarketingPlan()">📢 制定计划</button>
                    <button class="btn" onclick="analyzeROI()">💰 投资回报分析</button>
                </div>
            </div>

            <div class="recommendation-item">
                <div class="recommendation-header">
                    <div class="recommendation-title">
                        🎓 客服培训建议
                    </div>
                    <span class="priority-badge priority-low">低优先级</span>
                </div>
                <div class="recommendation-desc">
                    建议针对电池续航问题为客服团队提供专门培训，包括技术解释、临时解决方案和客户安抚技巧，以提升客户满意度。
                </div>
                <div class="recommendation-actions">
                    <button class="btn" onclick="designTraining()">📚 设计培训</button>
                    <button class="btn" onclick="scheduleTraining()">📅 安排培训</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 行业数据映射
        const industryData = {
            telecom: {
                name: '📱 手机通讯行业',
                totalInsights: '127',
                accuracyRate: '95%',
                aiStats: { insights: 127, accuracy: 95, predictions: 23, recommendations: 15 },
                insights: [
                    { type: '🎯 关键发现', content: '电池续航问题在旗舰机型X1中集中爆发，建议立即启动技术排查并发布临时解决方案。', confidence: '94%' },
                    { type: '📈 趋势预测', content: '基于近期反馈趋势，预计相机夜景功能将成为用户满意度主要驱动因素。', confidence: '87%' },
                    { type: '🔍 问题聚类', content: '客服咨询量在工作日下午达到峰值，建议调整人员排班以优化响应效率。', confidence: '91%' },
                    { type: '💡 机会识别', content: '夜景模式功能获得极高评价，建议作为核心卖点加大营销推广力度。', confidence: '89%' }
                ],
                wordCloud: ['电池续航', '拍照效果', '夜景模式', '充电速度', '系统流畅', '外观设计', '价格合理'],
                recommendations: [
                    { title: '应急响应', desc: '针对电池问题启动紧急处理流程', action: 'implementEmergencyPlan()' },
                    { title: '营销优化', desc: '基于夜景功能制定营销计划', action: 'createMarketingPlan()' },
                    { title: '客服培训', desc: '针对电池问题设计培训方案', action: 'designTraining()' }
                ]
            },
            beauty: {
                name: '💄 美妆护肤行业',
                totalInsights: '156',
                accuracyRate: '96%',
                aiStats: { insights: 156, accuracy: 96, predictions: 31, recommendations: 22 },
                insights: [
                    { type: '🎯 关键发现', content: '敏感肌用户群体快速增长，温和型产品需求上升32%，建议加大相关产品线投入。', confidence: '96%' },
                    { type: '📈 趋势预测', content: '基于社交媒体数据分析，预计"自然妆感"将成为下季度主流趋势。', confidence: '89%' },
                    { type: '🔍 问题聚类', content: '春季敏感期用户咨询集中在过敏反应，建议推出季节性护理指南。', confidence: '93%' },
                    { type: '💡 机会识别', content: '环保包装概念获得年轻用户高度认可，可作为差异化竞争优势。', confidence: '91%' }
                ],
                wordCloud: ['敏感肌', '自然妆感', '环保包装', '玫瑰金', '温和配方', '持久不脱', '滋润保湿'],
                recommendations: [
                    { title: '产品开发', desc: '扩大敏感肌专用产品线', action: 'developSensitiveProducts()' },
                    { title: '趋势营销', desc: '围绕自然妆感制定推广策略', action: 'createNaturalTrend()' },
                    { title: '环保升级', desc: '推进包装环保化改造', action: 'upgradePackaging()' }
                ]
            },
            retail: {
                name: '🛍️ 零售电商行业',
                totalInsights: '203',
                accuracyRate: '94%',
                aiStats: { insights: 203, accuracy: 94, predictions: 45, recommendations: 28 },
                insights: [
                    { type: '🎯 关键发现', content: '物流配送是影响用户满意度的最关键因素，建议优化配送网络布局。', confidence: '97%' },
                    { type: '📈 趋势预测', content: '基于购买行为分析，预计个性化推荐将显著提升转化率。', confidence: '85%' },
                    { type: '🔍 问题聚类', content: '退换货流程复杂度是用户投诉的主要原因，建议简化操作流程。', confidence: '92%' },
                    { type: '💡 机会识别', content: '可持续发展相关产品关注度提升45%，是新的增长点。', confidence: '88%' }
                ],
                wordCloud: ['配送速度', '退换货', '个性推荐', '可持续', '春季新品', '价格优势', '客服响应'],
                recommendations: [
                    { title: '物流优化', desc: '升级配送网络提升效率', action: 'optimizeLogistics()' },
                    { title: '流程简化', desc: '重新设计退换货流程', action: 'simplifyReturns()' },
                    { title: '绿色商品', desc: '推广可持续发展产品', action: 'promoteSustainable()' }
                ]
            },
            automotive: {
                name: '🚗 汽车制造行业',
                totalInsights: '89',
                accuracyRate: '95%',
                aiStats: { insights: 89, accuracy: 95, predictions: 18, recommendations: 12 },
                insights: [
                    { type: '🎯 关键发现', content: '新能源车用户对充电体验最为关注，充电便利性直接影响品牌忠诚度。', confidence: '95%' },
                    { type: '📈 趋势预测', content: '智能化配置将成为用户购车决策的重要因素，预计需求增长40%。', confidence: '88%' },
                    { type: '🔍 问题聚类', content: '第三方充电桩兼容性问题集中反馈，影响用户使用体验。', confidence: '90%' },
                    { type: '💡 机会识别', content: '智能驾驶辅助系统获得高度认可，可作为核心卖点推广。', confidence: '92%' }
                ],
                wordCloud: ['充电体验', '智能驾驶', '新能源', '兼容性', '售后服务', '智能配置', '品牌忠诚'],
                recommendations: [
                    { title: '充电网络', desc: '完善充电设施兼容性', action: 'improveCharging()' },
                    { title: '智能升级', desc: '推广智能驾驶功能', action: 'promoteSmartDriving()' },
                    { title: '服务延伸', desc: '优化售后服务体验', action: 'enhanceService()' }
                ]
            }
        };

        // 当前行业
        let currentIndustry = 'telecom';

        // 页面初始化时检查URL参数
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const industryParam = urlParams.get('industry');
            if (industryParam && industryData[industryParam]) {
                currentIndustry = industryParam;
                updateInsightsData();
            }
        });

        // 更新洞察数据
        function updateInsightsData() {
            const data = industryData[currentIndustry];
            if (!data) return;

            // 更新行业选择器
            document.querySelector('.industry-selector').innerHTML = data.name + ' ▼';

            // 更新AI统计
            const aiStatValues = document.querySelectorAll('.ai-stat-value');
            if (aiStatValues[0]) aiStatValues[0].textContent = data.aiStats.insights;
            if (aiStatValues[1]) aiStatValues[1].textContent = data.aiStats.accuracy + '%';
            if (aiStatValues[2]) aiStatValues[2].textContent = data.aiStats.predictions;
            if (aiStatValues[3]) aiStatValues[3].textContent = data.aiStats.recommendations;

            // 更新洞察卡片
            const insightCards = document.querySelectorAll('.insight-card');
            data.insights.forEach((insight, index) => {
                if (insightCards[index]) {
                    insightCards[index].querySelector('.insight-type').textContent = insight.type;
                    insightCards[index].querySelector('.insight-content').textContent = insight.content;
                    insightCards[index].querySelector('.insight-confidence').textContent = '置信度: ' + insight.confidence;
                }
            });

            // 更新词云
            const wordCloudContent = document.querySelector('.wordcloud-content');
            if (wordCloudContent) {
                wordCloudContent.innerHTML = data.wordCloud.map((word, index) => {
                    const sizes = ['font-size: 24px;', 'font-size: 18px;', 'font-size: 16px;', 'font-size: 14px;'];
                    const size = sizes[Math.min(index, sizes.length - 1)];
                    return `<span class="word-item" style="${size}" onclick="analyzeKeyword('${word}')">${word}</span>`;
                }).join('');
            }

            // 更新智能建议
            const recommendationsGrid = document.querySelector('.recommendations-grid');
            if (recommendationsGrid) {
                recommendationsGrid.innerHTML = data.recommendations.map(rec => `
                    <div class="recommendation-item">
                        <div class="recommendation-title">${rec.title}</div>
                        <div class="recommendation-desc">${rec.desc}</div>
                        <button class="recommendation-btn" onclick="${rec.action}">立即执行</button>
                    </div>
                `).join('');
            }

            // 添加切换动画效果
            document.body.style.opacity = '0.8';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 300);
        }

        // 导航函数
        function goToHome() {
            window.location.href = '../01-系统入口/登录页面原型.html';
        }

        function goToDashboard() {
            window.location.href = `数据分析仪表板原型.html?industry=${currentIndustry}`;
        }

        function goToReports() {
            window.location.href = `报表分析页面原型.html?industry=${currentIndustry}`;
        }

        function goToSettings() {
            window.location.href = '../04-系统管理/系统管理页面原型.html';
        }

        function changeIndustry() {
            const availableIndustries = Object.keys(industryData).map(key => industryData[key].name).join('\n• ');
            if (confirm(`是否切换到其他行业？\n\n当前已配置的行业:\n• ${availableIndustries}`)) {
                window.location.href = '../02-配置流程/行业选择页面原型.html';
            }
        }

        function openUserMenu() {
            alert('👤 用户菜单\n\n• 个人资料\n• 修改密码\n• 系统设置\n• 退出登录');
        }

        // AI洞察生成
        function generateNewInsights() {
            const btn = event.target;
            btn.innerHTML = '<div class="loading-animation"></div> 分析中...';
            btn.disabled = true;
            
            setTimeout(() => {
                btn.innerHTML = '⚡ 生成新洞察';
                btn.disabled = false;
                alert('🤖 AI洞察生成完成！\n\n新发现:\n• 检测到充电接口问题的上升趋势\n• 发现用户对新UI设计的积极反馈\n• 识别出客服专业度提升的机会\n\n洞察已更新到各个分析模块中');
            }, 3000);
        }

        // 洞察详情
        function viewInsightDetails(type) {
            const insights = {
                'battery-issue': '🔋 电池续航问题详情\n\n问题分布:\n• 旗舰机型X1: 67%\n• 中端机型M2: 23%\n• 入门机型E3: 10%\n\n时间分布:\n• 使用6个月+: 78%\n• 使用3-6个月: 18%\n• 使用3个月内: 4%',
                'night-mode': '📸 夜景模式好评详情\n\n好评关键词:\n• "夜景清晰": 342次提及\n• "弱光优秀": 289次提及\n• "噪点控制": 156次提及\n\n用户画像:\n• 摄影爱好者: 45%\n• 普通用户: 55%',
                'response-time': '⏱️ 响应时间详情\n\n延长时段:\n• 19:00-21:00: 平均延长28秒\n• 周五晚: 延长最严重\n• 影响用户: 约1,200人次/天'
            };
            
            alert(insights[type] || '详细数据加载中...');
        }

        function createAlert(type) {
            alert('🔔 告警创建成功！\n\n告警名称: ' + type + '监控\n触发条件: 相关投诉增长>200%\n通知方式: 邮件+短信+企业微信\n负责人: 产品经理、技术经理\n\n系统将实时监控相关指标变化');
        }

        function shareInsight(type) {
            alert('📤 分享洞察\n\n分享方式:\n• 企业微信群\n• 邮件报告\n• PPT演示文档\n• 数据仪表板链接\n\n已生成分享链接和摘要');
        }

        function implementSuggestion(type) {
            alert('✅ 建议已采纳\n\n将创建工作任务:\n• 分析具体原因\n• 制定改进方案\n• 安排实施计划\n• 跟踪改进效果\n\n任务已分配给相关团队');
        }

        // 趋势分析
        function viewTrendAnalysis() {
            alert('📈 趋势分析详情\n\n预测模型: LSTM时间序列\n历史数据: 90天\n预测精度: 91%\n\n关键影响因子:\n• 电池问题: -0.3分影响\n• 季节性因素: -0.1分\n• 竞品影响: -0.05分');
        }

        function preparePlan() {
            alert('📋 应对预案制定\n\n建议措施:\n1. 技术团队72小时内排查电池问题\n2. 客服准备标准应答话术\n3. 营销团队准备正面宣传内容\n4. 准备临时补偿方案\n\n预案已生成，等待审核');
        }

        // 词云分析
        function analyzeKeyword(keyword) {
            const analysis = {
                '电池续航': '提及频次: 2,847次\n情感倾向: 负面 (-0.7)\n趋势: 上升↗\n关联词: 充电、发热、性能',
                '拍照效果': '提及频次: 1,923次\n情感倾向: 正面 (+0.8)\n趋势: 上升↗\n关联词: 夜景、清晰、色彩',
                '夜景模式': '提及频次: 1,567次\n情感倾向: 极正面 (+0.9)\n趋势: 急升↗↗\n关联词: 惊艳、专业、满意'
            };
            
            alert(`🔍 "${keyword}" 分析结果\n\n${analysis[keyword] || '正在分析中...'}`);
        }

        function refreshWordCloud() {
            alert('☁️ 词云刷新中...\n\n重新分析最近24小时数据\n应用最新权重算法\n过滤无关词汇\n\n词云将在3秒后更新');
        }

        // AI建议执行
        function implementEmergencyPlan() {
            alert('🚨 应急响应已启动！\n\n执行动作:\n✅ 通知技术团队leader\n✅ 创建紧急任务看板\n✅ 预定明天上午专题会议\n✅ 启动客户沟通预案\n\n预计24小时内给出初步方案');
        }

        function createMarketingPlan() {
            alert('📢 营销计划制定中...\n\n计划要素:\n• 主打夜景拍照功能\n• 突出快充优势\n• 目标人群: 摄影爱好者\n• 投放渠道: 社交媒体\n• 预算建议: 50万\n• 预期ROI: 3:1\n\n计划草案已生成');
        }

        function designTraining() {
            alert('🎓 培训方案设计\n\n培训内容:\n• 电池技术基础知识\n• 常见问题FAQ\n• 客户沟通技巧\n• 临时解决方案\n\n培训时长: 2小时\n培训方式: 在线+线下\n考核方式: 模拟对话');
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 洞察卡片依次显示
            const cards = document.querySelectorAll('.insight-card, .wordcloud-section, .timeline-section, .recommendations-panel');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });

            // 词云动画效果
            setTimeout(() => {
                const words = document.querySelectorAll('.word-item');
                words.forEach((word, index) => {
                    setTimeout(() => {
                        word.style.opacity = '1';
                        word.style.transform = 'scale(1)';
                    }, index * 100);
                });
            }, 1000);
        });

        // 实时更新AI统计
        setInterval(() => {
            const aiStats = document.querySelectorAll('.ai-stat-value');
            if (Math.random() > 0.9) { // 10%概率更新
                const randomStat = aiStats[Math.floor(Math.random() * aiStats.length)];
                randomStat.style.animation = 'pulse 0.5s ease-in-out';
                setTimeout(() => {
                    randomStat.style.animation = '';
                }, 500);
            }
        }, 3000);
    </script>
</body>
</html> 