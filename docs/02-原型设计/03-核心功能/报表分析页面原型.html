<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VOC系统 - 报表分析</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f2f5;
            min-height: 100vh;
        }

        /* 顶部导航 */
        .header {
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 0 24px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 64px;
            max-width: 1600px;
            margin: 0 auto;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 20px;
            font-weight: 600;
            color: #1890ff;
            cursor: pointer;
        }

        .nav-menu {
            display: flex;
            gap: 32px;
        }

        .nav-item {
            padding: 8px 16px;
            color: #262626;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.2s;
            cursor: pointer;
        }

        .nav-item:hover {
            background: #e6f7ff;
            color: #1890ff;
        }

        .nav-item.active {
            background: #1890ff;
            color: white;
        }

        .nav-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .industry-selector {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            background: #f0f9ff;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 6px;
            transition: background 0.2s;
        }

        .user-menu:hover {
            background: #f5f5f5;
        }

        /* 主容器 */
        .main-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 24px;
        }

        /* 页面头部 */
        .page-header {
            background: white;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
        }

        .page-subtitle {
            color: #8c8c8c;
            font-size: 14px;
        }

        /* 筛选器区域 */
        .filters-section {
            background: white;
            border-radius: 8px;
            padding: 20px 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .filters-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filters-row {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .filter-label {
            font-size: 14px;
            color: #595959;
            font-weight: 500;
        }

        .filter-control {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            min-width: 150px;
            transition: border-color 0.2s;
        }

        .filter-control:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .filter-actions {
            display: flex;
            gap: 12px;
            margin-left: auto;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: 1px solid #d9d9d9;
            background: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .btn-primary {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background: #40a9ff;
        }

        /* 报表网格 */
        .reports-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }

        .report-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .card-header {
            padding: 20px 24px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #262626;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }

        .action-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .card-content {
            padding: 20px 24px 24px;
        }

        /* 图表样式 */
        .chart-container {
            height: 300px;
            position: relative;
            background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .trend-chart {
            width: 100%;
            height: 100%;
            position: relative;
            padding: 20px;
        }

        .chart-line {
            position: absolute;
            bottom: 60px;
            left: 40px;
            right: 40px;
            height: 2px;
            background: linear-gradient(90deg, #52c41a 0%, #1890ff 50%, #fa8c16 100%);
            border-radius: 1px;
        }

        .chart-points {
            position: absolute;
            bottom: 50px;
            left: 40px;
            right: 40px;
            display: flex;
            justify-content: space-between;
        }

        .chart-point {
            width: 8px;
            height: 8px;
            background: #1890ff;
            border-radius: 50%;
            position: relative;
            cursor: pointer;
            transition: all 0.2s;
        }

        .chart-point:hover {
            transform: scale(1.5);
            background: #40a9ff;
        }

        .chart-value {
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            color: #1890ff;
            font-weight: 500;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .chart-point:hover .chart-value {
            opacity: 1;
        }

        .chart-labels {
            position: absolute;
            bottom: 20px;
            left: 40px;
            right: 40px;
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #8c8c8c;
        }

        /* 数据表格 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }

        .data-table th,
        .data-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table th {
            background: #fafafa;
            font-weight: 600;
            color: #262626;
            font-size: 14px;
        }

        .data-table tbody tr:hover {
            background: #f5f5f5;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .stat-icon {
            font-size: 24px;
            margin-bottom: 12px;
        }

        .stat-value {
            font-size: 28px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: #8c8c8c;
            margin-bottom: 8px;
        }

        .stat-change {
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .stat-change.positive {
            color: #52c41a;
        }

        .stat-change.negative {
            color: #ff4d4f;
        }

        .stat-change.neutral {
            color: #8c8c8c;
        }

        /* 饼图样式 */
        .pie-chart {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: conic-gradient(
                #52c41a 0deg 144deg,
                #1890ff 144deg 216deg,
                #fa8c16 216deg 288deg,
                #ff4d4f 288deg 324deg,
                #d9d9d9 324deg 360deg
            );
            position: relative;
            margin: 0 auto;
        }

        .pie-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100px;
            height: 100px;
            background: white;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .pie-total {
            font-size: 18px;
            font-weight: 600;
            color: #262626;
        }

        .pie-label {
            font-size: 12px;
            color: #8c8c8c;
        }

        .legend {
            margin-top: 20px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        /* 导出模态框 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            padding: 24px;
            width: 90%;
            max-width: 500px;
        }

        .modal-header {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-close {
            cursor: pointer;
            font-size: 20px;
            color: #8c8c8c;
        }

        .export-options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }

        .export-option {
            padding: 12px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            cursor: pointer;
            text-align: center;
            transition: all 0.2s;
        }

        .export-option:hover {
            border-color: #1890ff;
            background: #f0f9ff;
        }

        .export-option.selected {
            border-color: #1890ff;
            background: #e6f7ff;
        }

        /* 响应式 */
        @media (max-width: 1200px) {
            .reports-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .main-container {
                padding: 16px;
            }
            
            .filters-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-actions {
                margin-left: 0;
                margin-top: 16px;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="header">
        <div class="nav-container">
            <div class="logo" onclick="goToHome()">
                🎯 VOC数据分析平台
            </div>
            <div class="nav-menu">
                <a href="#" class="nav-item" onclick="goToDashboard()">📊 仪表板</a>
                <a href="#" class="nav-item active">📈 报表分析</a>
                <a href="#" class="nav-item" onclick="goToInsights()">🔍 深度洞察</a>
                <a href="#" class="nav-item" onclick="goToSettings()">⚙️ 系统配置</a>
            </div>
            <div class="nav-actions">
                <div class="industry-selector" onclick="changeIndustry()">
                    📱 手机通讯行业 ▼
                </div>
                <div class="user-menu" onclick="openUserMenu()">
                    👤 张三 ▼
                </div>
            </div>
        </div>
    </div>

    <div class="main-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="page-title">📈 报表分析中心</div>
            <div class="page-subtitle">深度分析客户反馈数据，生成专业分析报表</div>
        </div>

        <!-- 筛选器 -->
        <div class="filters-section">
            <div class="filters-title">
                🔍 数据筛选器
            </div>
            <div class="filters-row">
                <div class="filter-group">
                    <div class="filter-label">时间范围</div>
                    <select class="filter-control" onchange="updateCharts()">
                        <option value="7d">最近7天</option>
                        <option value="30d" selected>最近30天</option>
                        <option value="90d">最近90天</option>
                        <option value="6m">最近6个月</option>
                        <option value="1y">最近1年</option>
                    </select>
                </div>
                <div class="filter-group">
                    <div class="filter-label">数据源</div>
                    <select class="filter-control" onchange="updateCharts()">
                        <option value="all">全部数据源</option>
                        <option value="call">客服通话</option>
                        <option value="chat">在线客服</option>
                        <option value="review">用户评价</option>
                        <option value="survey">问卷调研</option>
                    </select>
                </div>
                <div class="filter-group">
                    <div class="filter-label">产品线</div>
                    <select class="filter-control" onchange="updateCharts()">
                        <option value="all">全部产品</option>
                        <option value="flagship">旗舰手机</option>
                        <option value="mid">中端手机</option>
                        <option value="entry">入门手机</option>
                    </select>
                </div>
                <div class="filter-group">
                    <div class="filter-label">地区</div>
                    <select class="filter-control" onchange="updateCharts()">
                        <option value="all">全国</option>
                        <option value="north">华北地区</option>
                        <option value="east">华东地区</option>
                        <option value="south">华南地区</option>
                    </select>
                </div>
                <div class="filter-actions">
                    <button class="btn" onclick="resetFilters()">🔄 重置</button>
                    <button class="btn btn-primary" onclick="exportReport()">📤 导出报表</button>
                </div>
            </div>
        </div>

        <!-- 关键指标统计 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">📊</div>
                <div class="stat-value">28,547</div>
                <div class="stat-label">总反馈数量</div>
                <div class="stat-change positive">
                    ↗ +15.2% 环比上月
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">😊</div>
                <div class="stat-value">4.7</div>
                <div class="stat-label">平均满意度</div>
                <div class="stat-change positive">
                    ↗ +0.3 环比上月
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">⚡</div>
                <div class="stat-value">91.3%</div>
                <div class="stat-label">问题解决率</div>
                <div class="stat-change negative">
                    ↘ -2.1% 环比上月
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">⏱️</div>
                <div class="stat-value">3.2h</div>
                <div class="stat-label">平均响应时间</div>
                <div class="stat-change neutral">
                    → 0% 环比上月
                </div>
            </div>
        </div>

        <!-- 报表网格 -->
        <div class="reports-grid">
            <!-- 趋势分析图 -->
            <div class="report-card">
                <div class="card-header">
                    <div class="card-title">
                        📈 客户满意度趋势
                    </div>
                    <div class="card-actions">
                        <button class="action-btn" onclick="toggleChartType()">📊 切换图表</button>
                        <button class="action-btn" onclick="drillDown()">🔍 钻取分析</button>
                        <button class="action-btn" onclick="exportChart()">📤 导出</button>
                    </div>
                </div>
                <div class="card-content">
                    <div class="chart-container">
                        <div class="trend-chart">
                            <div class="chart-line"></div>
                            <div class="chart-points">
                                <div class="chart-point" onclick="showDataPoint('周一', '4.2')">
                                    <div class="chart-value">4.2</div>
                                </div>
                                <div class="chart-point" onclick="showDataPoint('周二', '4.3')">
                                    <div class="chart-value">4.3</div>
                                </div>
                                <div class="chart-point" onclick="showDataPoint('周三', '4.1')">
                                    <div class="chart-value">4.1</div>
                                </div>
                                <div class="chart-point" onclick="showDataPoint('周四', '4.5')">
                                    <div class="chart-value">4.5</div>
                                </div>
                                <div class="chart-point" onclick="showDataPoint('周五', '4.6')">
                                    <div class="chart-value">4.6</div>
                                </div>
                                <div class="chart-point" onclick="showDataPoint('周六', '4.8')">
                                    <div class="chart-value">4.8</div>
                                </div>
                                <div class="chart-point" onclick="showDataPoint('周日', '4.7')">
                                    <div class="chart-value">4.7</div>
                                </div>
                            </div>
                            <div class="chart-labels">
                                <span>周一</span>
                                <span>周二</span>
                                <span>周三</span>
                                <span>周四</span>
                                <span>周五</span>
                                <span>周六</span>
                                <span>周日</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 情感分布饼图 -->
            <div class="report-card">
                <div class="card-header">
                    <div class="card-title">
                        😊 情感分布分析
                    </div>
                    <div class="card-actions">
                        <button class="action-btn" onclick="viewDetails()">📋 查看详情</button>
                    </div>
                </div>
                <div class="card-content">
                    <div class="pie-chart">
                        <div class="pie-center">
                            <div class="pie-total">28,547</div>
                            <div class="pie-label">总数</div>
                        </div>
                    </div>
                    <div class="legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background: #52c41a;"></div>
                            <span>非常满意 (40%)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #1890ff;"></div>
                            <span>满意 (20%)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #fa8c16;"></div>
                            <span>一般 (20%)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #ff4d4f;"></div>
                            <span>不满意 (10%)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #d9d9d9;"></div>
                            <span>未知 (10%)</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细数据表格 -->
        <div class="report-card">
            <div class="card-header">
                <div class="card-title">
                    📋 详细数据列表
                </div>
                <div class="card-actions">
                    <button class="action-btn" onclick="refreshData()">🔄 刷新</button>
                    <button class="action-btn" onclick="configColumns()">⚙️ 配置列</button>
                    <button class="action-btn" onclick="exportTableData()">📤 导出数据</button>
                </div>
            </div>
            <div class="card-content">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>数据源</th>
                            <th>反馈数量</th>
                            <th>平均满意度</th>
                            <th>问题数量</th>
                            <th>解决率</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr onclick="viewRowDetails('2024-01-15')">
                            <td>2024-01-15</td>
                            <td>客服通话</td>
                            <td>2,845</td>
                            <td>4.6</td>
                            <td>267</td>
                            <td>89.2%</td>
                            <td>
                                <button class="action-btn" onclick="viewDetails('2024-01-15')">📊 查看</button>
                            </td>
                        </tr>
                        <tr onclick="viewRowDetails('2024-01-14')">
                            <td>2024-01-14</td>
                            <td>在线客服</td>
                            <td>1,923</td>
                            <td>4.8</td>
                            <td>145</td>
                            <td>92.4%</td>
                            <td>
                                <button class="action-btn" onclick="viewDetails('2024-01-14')">📊 查看</button>
                            </td>
                        </tr>
                        <tr onclick="viewRowDetails('2024-01-13')">
                            <td>2024-01-13</td>
                            <td>用户评价</td>
                            <td>3,567</td>
                            <td>4.5</td>
                            <td>389</td>
                            <td>87.1%</td>
                            <td>
                                <button class="action-btn" onclick="viewDetails('2024-01-13')">📊 查看</button>
                            </td>
                        </tr>
                        <tr onclick="viewRowDetails('2024-01-12')">
                            <td>2024-01-12</td>
                            <td>问卷调研</td>
                            <td>892</td>
                            <td>4.7</td>
                            <td>67</td>
                            <td>94.2%</td>
                            <td>
                                <button class="action-btn" onclick="viewDetails('2024-01-12')">📊 查看</button>
                            </td>
                        </tr>
                        <tr onclick="viewRowDetails('2024-01-11')">
                            <td>2024-01-11</td>
                            <td>客服通话</td>
                            <td>2,156</td>
                            <td>4.4</td>
                            <td>298</td>
                            <td>85.6%</td>
                            <td>
                                <button class="action-btn" onclick="viewDetails('2024-01-11')">📊 查看</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 导出模态框 -->
    <div class="modal" id="exportModal">
        <div class="modal-content">
            <div class="modal-header">
                <span>📤 导出报表</span>
                <span class="modal-close" onclick="closeExportModal()">×</span>
            </div>
            <div class="export-options">
                <div class="export-option selected" onclick="selectExportFormat(this, 'excel')">
                    <div style="font-size: 24px; margin-bottom: 8px;">📊</div>
                    <div>Excel报表</div>
                </div>
                <div class="export-option" onclick="selectExportFormat(this, 'pdf')">
                    <div style="font-size: 24px; margin-bottom: 8px;">📄</div>
                    <div>PDF报告</div>
                </div>
                <div class="export-option" onclick="selectExportFormat(this, 'csv')">
                    <div style="font-size: 24px; margin-bottom: 8px;">📋</div>
                    <div>CSV数据</div>
                </div>
                <div class="export-option" onclick="selectExportFormat(this, 'png')">
                    <div style="font-size: 24px; margin-bottom: 8px;">🖼️</div>
                    <div>图片格式</div>
                </div>
            </div>
            <div style="display: flex; gap: 12px; justify-content: flex-end;">
                <button class="btn" onclick="closeExportModal()">取消</button>
                <button class="btn btn-primary" onclick="confirmExport()">开始导出</button>
            </div>
        </div>
    </div>

    <script>
        let selectedExportFormat = 'excel';

        // 行业数据映射
        const industryData = {
            telecom: {
                name: '📱 手机通讯行业',
                totalFeedback: '15,847',
                avgSatisfaction: '4.6',
                trendData: [4.2, 4.3, 4.1, 4.5, 4.6, 4.4, 4.6],
                positiveSentiment: 65,
                neutralSentiment: 25,
                negativeSentiment: 10,
                productFilters: ['旗舰手机', '中端手机', '入门手机'],
                recentData: [
                    { date: '2024-01-13', source: '在线客服', feedback: 2847, satisfaction: 4.6, issues: 245, resolution: '91.2%' },
                    { date: '2024-01-12', source: '用户评价', feedback: 3124, satisfaction: 4.5, issues: 312, resolution: '87.8%' },
                    { date: '2024-01-11', source: '客服通话', feedback: 2156, satisfaction: 4.4, issues: 298, resolution: '85.6%' }
                ]
            },
            beauty: {
                name: '💄 美妆护肤行业',
                totalFeedback: '24,356',
                avgSatisfaction: '4.7',
                trendData: [4.4, 4.5, 4.6, 4.7, 4.8, 4.7, 4.7],
                positiveSentiment: 72,
                neutralSentiment: 20,
                negativeSentiment: 8,
                productFilters: ['护肤产品', '彩妆产品', '香氛产品'],
                recentData: [
                    { date: '2024-01-13', source: '用户评价', feedback: 4285, satisfaction: 4.7, issues: 156, resolution: '95.2%' },
                    { date: '2024-01-12', source: '社交媒体', feedback: 3896, satisfaction: 4.8, issues: 98, resolution: '96.8%' },
                    { date: '2024-01-11', source: '在线客服', feedback: 2745, satisfaction: 4.6, issues: 187, resolution: '93.4%' }
                ]
            },
            retail: {
                name: '🛍️ 零售电商行业',
                totalFeedback: '68,924',
                avgSatisfaction: '4.5',
                trendData: [4.3, 4.4, 4.2, 4.5, 4.6, 4.4, 4.5],
                positiveSentiment: 62,
                neutralSentiment: 28,
                negativeSentiment: 10,
                productFilters: ['服装鞋帽', '家居用品', '数码电器'],
                recentData: [
                    { date: '2024-01-13', source: '订单评价', feedback: 8247, satisfaction: 4.5, issues: 734, resolution: '89.2%' },
                    { date: '2024-01-12', source: '客服咨询', feedback: 6832, satisfaction: 4.4, issues: 891, resolution: '86.7%' },
                    { date: '2024-01-11', source: '退换货', feedback: 5643, satisfaction: 4.3, issues: 1024, resolution: '84.5%' }
                ]
            },
            automotive: {
                name: '🚗 汽车制造行业',
                totalFeedback: '12,458',
                avgSatisfaction: '4.6',
                trendData: [4.4, 4.5, 4.7, 4.6, 4.7, 4.5, 4.6],
                positiveSentiment: 68,
                neutralSentiment: 22,
                negativeSentiment: 10,
                productFilters: ['轿车', 'SUV', '新能源车'],
                recentData: [
                    { date: '2024-01-13', source: '4S店反馈', feedback: 1847, satisfaction: 4.6, issues: 125, resolution: '92.8%' },
                    { date: '2024-01-12', source: '用户调研', feedback: 2124, satisfaction: 4.7, issues: 98, resolution: '94.2%' },
                    { date: '2024-01-11', source: '售后服务', feedback: 1756, satisfaction: 4.5, issues: 156, resolution: '89.6%' }
                ]
            }
        };

        // 当前行业
        let currentIndustry = 'telecom';

        // 页面初始化时检查URL参数
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const industryParam = urlParams.get('industry');
            if (industryParam && industryData[industryParam]) {
                currentIndustry = industryParam;
                updateReportData();
            }
        });

        // 更新报表数据
        function updateReportData() {
            const data = industryData[currentIndustry];
            if (!data) return;

            // 更新行业选择器
            document.querySelector('.industry-selector').innerHTML = data.name + ' ▼';

            // 更新关键指标
            const statCards = document.querySelectorAll('.stat-card');
            if (statCards[0]) statCards[0].querySelector('.stat-value').textContent = data.totalFeedback;
            if (statCards[1]) statCards[1].querySelector('.stat-value').innerHTML = data.avgSatisfaction + '<span style="font-size: 16px;">/5.0</span>';
            if (statCards[2]) statCards[2].querySelector('.stat-value').textContent = data.positiveSentiment + '%';

            // 更新情感分析饼图
            const sentimentLegend = document.querySelectorAll('.legend-item');
            if (sentimentLegend[0]) sentimentLegend[0].querySelector('.legend-value').textContent = data.positiveSentiment + '%';
            if (sentimentLegend[1]) sentimentLegend[1].querySelector('.legend-value').textContent = data.neutralSentiment + '%';
            if (sentimentLegend[2]) sentimentLegend[2].querySelector('.legend-value').textContent = data.negativeSentiment + '%';

            // 更新产品筛选器
            const productFilterSelects = document.querySelectorAll('.filter-select');
            if (productFilterSelects[2]) {
                productFilterSelects[2].innerHTML = '<option>全部产品</option>' + 
                    data.productFilters.map(filter => `<option>${filter}</option>`).join('');
            }

            // 更新数据表格
            const tableBody = document.querySelector('.report-table tbody');
            if (tableBody) {
                tableBody.innerHTML = data.recentData.map(row => `
                    <tr onclick="viewRowDetails('${row.date}')">
                        <td>${row.date}</td>
                        <td>${row.source}</td>
                        <td>${row.feedback.toLocaleString()}</td>
                        <td>${row.satisfaction}</td>
                        <td>${row.issues}</td>
                        <td>${row.resolution}</td>
                        <td>
                            <button class="action-btn" onclick="viewDetails('${row.date}')">📊 查看</button>
                        </td>
                    </tr>
                `).join('');
            }

            // 添加切换动画效果
            document.body.style.opacity = '0.8';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 300);
        }

        // 导航函数
        function goToHome() {
            window.location.href = '../01-系统入口/登录页面原型.html';
        }

        function goToDashboard() {
            window.location.href = `数据分析仪表板原型.html?industry=${currentIndustry}`;
        }

        function goToInsights() {
            window.location.href = `深度洞察页面原型.html?industry=${currentIndustry}`;
        }

        function goToSettings() {
            window.location.href = '../04-系统管理/系统管理页面原型.html';
        }

        function changeIndustry() {
            const availableIndustries = Object.keys(industryData).map(key => industryData[key].name).join('\n• ');
            if (confirm(`是否切换到其他行业？\n\n当前已配置的行业:\n• ${availableIndustries}`)) {
                window.location.href = '../02-配置流程/行业选择页面原型.html';
            }
        }

        function openUserMenu() {
            alert('👤 用户菜单\n\n• 个人资料\n• 修改密码\n• 系统设置\n• 退出登录');
        }

        // 图表交互
        function showDataPoint(day, value) {
            alert(`📊 数据详情\n\n日期：${day}\n满意度：${value}/5.0\n反馈数量：${Math.floor(Math.random() * 1000 + 500)}\n主要问题：电池续航、充电速度`);
        }

        function updateCharts() {
            // 模拟图表更新
            const charts = document.querySelectorAll('.chart-container, .pie-chart');
            charts.forEach(chart => {
                chart.style.opacity = '0.7';
                setTimeout(() => {
                    chart.style.opacity = '1';
                }, 500);
            });
            
            // 模拟数据更新
            setTimeout(() => {
                alert('✅ 数据已更新\n\n根据新的筛选条件重新生成了图表和统计数据');
            }, 800);
        }

        function resetFilters() {
            document.querySelectorAll('.filter-control').forEach(control => {
                control.selectedIndex = 0;
            });
            updateCharts();
        }

        function toggleChartType() {
            alert('📊 图表类型切换\n\n可选类型：\n• 折线图 (当前)\n• 柱状图\n• 面积图\n• 散点图\n\n点击确定切换到柱状图');
        }

        function drillDown() {
            alert('🔍 钻取分析\n\n选择钻取维度：\n• 按产品线钻取\n• 按地区钻取\n• 按时间钻取\n• 按问题分类钻取\n\n这将显示更详细的分析数据');
        }

        function exportChart() {
            alert('📊 导出图表\n\n支持格式：\n• PNG图片\n• SVG矢量图\n• PDF文档\n• Excel数据\n\n图表导出中...');
        }

        function viewDetails() {
            alert('📋 情感分布详情\n\n非常满意：11,419条 (40%)\n满意：5,709条 (20%)\n一般：5,709条 (20%)\n不满意：2,855条 (10%)\n未知：2,855条 (10%)\n\n点击可查看具体反馈内容');
        }

        function viewRowDetails(date) {
            alert(`📊 ${date} 详细数据\n\n• 总反馈数：2,845条\n• 满意度分布：\n  - 非常满意：45%\n  - 满意：25%\n  - 一般：20%\n  - 不满意：10%\n\n• 主要问题：\n  - 电池续航：156条\n  - 充电问题：89条\n  - 系统卡顿：67条`);
        }

        function refreshData() {
            const btn = event.target;
            btn.innerHTML = '🔄 刷新中...';
            btn.disabled = true;
            
            setTimeout(() => {
                btn.innerHTML = '🔄 刷新';
                btn.disabled = false;
                alert('✅ 数据已刷新\n\n获取到最新的反馈数据，表格已更新');
            }, 1500);
        }

        function configColumns() {
            alert('⚙️ 列配置\n\n可配置项：\n☑️ 日期\n☑️ 数据源\n☑️ 反馈数量\n☑️ 平均满意度\n□ 响应时间\n□ 处理人员\n□ 客户等级\n\n点击可隐藏/显示列');
        }

        function exportTableData() {
            alert('📤 导出表格数据\n\n导出格式：\n• Excel (.xlsx)\n• CSV (.csv)\n• JSON (.json)\n\n导出范围：\n• 当前页数据\n• 全部数据\n• 筛选后数据');
        }

        // 导出模态框
        function exportReport() {
            document.getElementById('exportModal').style.display = 'block';
        }

        function closeExportModal() {
            document.getElementById('exportModal').style.display = 'none';
        }

        function selectExportFormat(element, format) {
            document.querySelectorAll('.export-option').forEach(opt => {
                opt.classList.remove('selected');
            });
            element.classList.add('selected');
            selectedExportFormat = format;
        }

        function confirmExport() {
            closeExportModal();
            
            const formatNames = {
                'excel': 'Excel报表',
                'pdf': 'PDF报告', 
                'csv': 'CSV数据',
                'png': '图片格式'
            };
            
            alert(`📤 开始导出 ${formatNames[selectedExportFormat]}\n\n导出内容：\n• 关键指标统计\n• 趋势分析图表\n• 情感分布数据\n• 详细数据列表\n\n预计用时：30秒\n导出完成后将自动下载`);
            
            // 模拟导出进度
            setTimeout(() => {
                alert('✅ 导出完成！\n\n文件已保存到下载文件夹\n文件名：VOC分析报表_' + new Date().toLocaleDateString() + '.' + selectedExportFormat);
            }, 3000);
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.report-card, .stat-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // 模拟实时数据更新
        setInterval(() => {
            const statValues = document.querySelectorAll('.stat-value');
            statValues.forEach(stat => {
                if (Math.random() > 0.8) { // 20%概率更新
                    stat.style.transform = 'scale(1.1)';
                    stat.style.color = '#1890ff';
                    setTimeout(() => {
                        stat.style.transform = 'scale(1)';
                        stat.style.color = '#262626';
                    }, 300);
                }
            });
        }, 5000);
    </script>
</body>
</html> 