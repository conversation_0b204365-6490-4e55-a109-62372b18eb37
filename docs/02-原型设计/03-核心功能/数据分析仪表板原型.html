<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VOC数据分析仪表板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f2f5;
            min-height: 100vh;
        }

        /* 顶部导航 */
        .header {
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 0 24px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 64px;
            max-width: 1600px;
            margin: 0 auto;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 20px;
            font-weight: 600;
            color: #1890ff;
        }

        .nav-menu {
            display: flex;
            gap: 32px;
        }

        .nav-item {
            padding: 8px 16px;
            color: #262626;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.2s;
        }

        .nav-item:hover,
        .nav-item.active {
            background: #e6f7ff;
            color: #1890ff;
        }

        .nav-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .industry-selector {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            background: #f0f9ff;
            border-radius: 6px;
            font-size: 14px;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }

        /* 主容器 */
        .main-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 24px;
        }

        /* 快速统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .stat-title {
            font-size: 14px;
            color: #8c8c8c;
            font-weight: 500;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
        }

        .stat-value {
            font-size: 32px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
        }

        .stat-change {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 14px;
        }

        .stat-change.positive {
            color: #52c41a;
        }

        .stat-change.negative {
            color: #ff4d4f;
        }

        .stat-sparkline {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 60px;
            height: 30px;
            opacity: 0.3;
        }

        /* 仪表板网格 */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
            margin-bottom: 32px;
        }

        /* 卡片基础样式 */
        .dashboard-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .card-header {
            padding: 20px 24px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            padding: 4px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            color: #8c8c8c;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .action-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .card-content {
            padding: 20px 24px 24px;
        }

        /* 情感分析图表 */
        .sentiment-chart {
            height: 300px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sentiment-pie {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: conic-gradient(
                #52c41a 0deg 144deg,
                #fadb14 144deg 216deg,
                #ff4d4f 216deg 288deg,
                #d9d9d9 288deg 360deg
            );
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sentiment-center {
            width: 120px;
            height: 120px;
            background: white;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .sentiment-score {
            font-size: 24px;
            font-weight: 600;
            color: #52c41a;
        }

        .sentiment-label {
            font-size: 12px;
            color: #8c8c8c;
        }

        .sentiment-legend {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        /* 实时监控 */
        .realtime-monitor {
            height: 400px;
        }

        .monitor-tabs {
            display: flex;
            border-bottom: 1px solid #e8e8e8;
            margin-bottom: 16px;
        }

        .monitor-tab {
            padding: 8px 16px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: #8c8c8c;
            transition: all 0.2s;
        }

        .monitor-tab.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
        }

        .monitor-content {
            height: 320px;
            overflow-y: auto;
        }

        .monitor-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .monitor-status {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-top: 6px;
            flex-shrink: 0;
        }

        .monitor-status.high {
            background: #ff4d4f;
        }

        .monitor-status.medium {
            background: #fa8c16;
        }

        .monitor-status.low {
            background: #52c41a;
        }

        .monitor-text {
            flex: 1;
            font-size: 14px;
            line-height: 1.5;
        }

        .monitor-time {
            font-size: 12px;
            color: #8c8c8c;
        }

        /* 趋势分析 */
        .trend-analysis {
            grid-column: 1 / -1;
        }

        .trend-chart {
            height: 300px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .trend-line {
            position: absolute;
            bottom: 40px;
            left: 40px;
            right: 40px;
            height: 2px;
            background: #1890ff;
        }

        .trend-points {
            position: absolute;
            bottom: 30px;
            left: 40px;
            right: 40px;
            display: flex;
            justify-content: space-between;
        }

        .trend-point {
            width: 8px;
            height: 8px;
            background: #1890ff;
            border-radius: 50%;
            position: relative;
        }

        .trend-value {
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            color: #1890ff;
            font-weight: 500;
        }

        .trend-labels {
            position: absolute;
            bottom: 10px;
            left: 40px;
            right: 40px;
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #8c8c8c;
        }

        /* AI洞察面板 */
        .ai-insights {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .ai-insights .card-title {
            color: white;
        }

        .insight-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
        }

        .insight-type {
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 8px;
        }

        .insight-content {
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 8px;
        }

        .insight-confidence {
            font-size: 12px;
            opacity: 0.7;
        }

        /* 快速操作 */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 32px;
        }

        .action-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .action-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .action-icon {
            font-size: 32px;
            margin-bottom: 12px;
        }

        .action-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .action-desc {
            font-size: 14px;
            color: #8c8c8c;
        }

        /* 响应式 */
        @media (max-width: 1200px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .main-container {
                padding: 16px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 筛选器 */
        .filters-bar {
            background: white;
            border-radius: 8px;
            padding: 16px 24px;
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-label {
            font-size: 14px;
            color: #8c8c8c;
            white-space: nowrap;
        }

        .filter-select {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            min-width: 120px;
        }

        .filter-select:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="header">
        <div class="nav-container">
            <div class="logo">
                🎯 VOC数据分析平台
            </div>
            <div class="nav-menu">
                <a href="#" class="nav-item active" onclick="goToDashboard()">📊 仪表板</a>
                <a href="#" class="nav-item" onclick="goToReports()">📈 报表分析</a>
                <a href="#" class="nav-item" onclick="goToInsights()">🔍 深度洞察</a>
                <a href="#" class="nav-item" onclick="goToSettings()">⚙️ 系统配置</a>
            </div>
            <div class="nav-actions">
                <div class="industry-selector" onclick="changeIndustry()">
                    📱 手机通讯行业 ▼
                </div>
                <div class="user-menu" onclick="openUserMenu()">
                    👤 张三 ▼
                </div>
            </div>
        </div>
    </div>

    <div class="main-container">
        <!-- 筛选器 -->
        <div class="filters-bar">
            <div class="filter-group">
                <span class="filter-label">时间范围:</span>
                <select class="filter-select">
                    <option>最近7天</option>
                    <option>最近30天</option>
                    <option>最近90天</option>
                    <option>自定义时间</option>
                </select>
            </div>
            <div class="filter-group">
                <span class="filter-label">数据源:</span>
                <select class="filter-select">
                    <option>全部数据源</option>
                    <option>客服通话</option>
                    <option>在线客服</option>
                    <option>用户评价</option>
                </select>
            </div>
            <div class="filter-group">
                <span class="filter-label">产品线:</span>
                <select class="filter-select">
                    <option>全部产品</option>
                    <option>旗舰手机</option>
                    <option>中端手机</option>
                    <option>入门手机</option>
                </select>
            </div>
            <div class="filter-group">
                <span class="filter-label">地区:</span>
                <select class="filter-select">
                    <option>全国</option>
                    <option>华北地区</option>
                    <option>华东地区</option>
                    <option>华南地区</option>
                </select>
            </div>
        </div>

        <!-- 快速统计 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">总反馈数量</div>
                    <div class="stat-icon" style="background: #1890ff;">📊</div>
                </div>
                <div class="stat-value">15,847</div>
                <div class="stat-change positive">
                    ↗ +12.5% 较上周
                </div>
                <div class="stat-sparkline">📈</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">客户满意度</div>
                    <div class="stat-icon" style="background: #52c41a;">😊</div>
                </div>
                <div class="stat-value">4.6<span style="font-size: 16px;">/5.0</span></div>
                <div class="stat-change positive">
                    ↗ +0.3 较上周
                </div>
                <div class="stat-sparkline">📈</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">问题解决率</div>
                    <div class="stat-icon" style="background: #fa8c16;">🎯</div>
                </div>
                <div class="stat-value">89.2<span style="font-size: 16px;">%</span></div>
                <div class="stat-change negative">
                    ↘ -2.1% 较上周
                </div>
                <div class="stat-sparkline">📉</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">AI处理准确率</div>
                    <div class="stat-icon" style="background: #722ed1;">🤖</div>
                </div>
                <div class="stat-value">96.8<span style="font-size: 16px;">%</span></div>
                <div class="stat-change positive">
                    ↗ +1.2% 较上周
                </div>
                <div class="stat-sparkline">📈</div>
            </div>
        </div>

        <!-- 主要图表区域 -->
        <div class="dashboard-grid">
            <!-- 情感分析 -->
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-title">
                        😊 情感分析分布
                    </div>
                    <div class="card-actions">
                        <button class="action-btn">📊 详细分析</button>
                        <button class="action-btn">📤 导出</button>
                    </div>
                </div>
                <div class="card-content">
                    <div class="sentiment-chart">
                        <div class="sentiment-pie">
                            <div class="sentiment-center">
                                <div class="sentiment-score">4.6</div>
                                <div class="sentiment-label">综合评分</div>
                            </div>
                        </div>
                        <div class="sentiment-legend">
                            <div class="legend-item">
                                <div class="legend-color" style="background: #52c41a;"></div>
                                <span>正面 (40%)</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: #fadb14;"></div>
                                <span>中性 (20%)</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: #ff4d4f;"></div>
                                <span>负面 (20%)</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: #d9d9d9;"></div>
                                <span>待处理 (20%)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 实时监控 -->
            <div class="dashboard-card realtime-monitor">
                <div class="card-header">
                    <div class="card-title">
                        ⚡ 实时问题监控
                    </div>
                    <div class="card-actions">
                        <button class="action-btn">🔔 设置告警</button>
                    </div>
                </div>
                <div class="card-content">
                    <div class="monitor-tabs">
                        <div class="monitor-tab active">🚨 紧急</div>
                        <div class="monitor-tab">⚠️ 重要</div>
                        <div class="monitor-tab">ℹ️ 一般</div>
                    </div>
                    <div class="monitor-content">
                        <div class="monitor-item">
                            <div class="monitor-status high"></div>
                            <div class="monitor-text">
                                <div><strong>电池续航问题激增</strong></div>
                                <div>旗舰机型X1用户反馈电池续航明显下降，已识别32条相关反馈</div>
                            </div>
                            <div class="monitor-time">2分钟前</div>
                        </div>
                        <div class="monitor-item">
                            <div class="monitor-status high"></div>
                            <div class="monitor-text">
                                <div><strong>充电速度异常</strong></div>
                                <div>多名用户反馈快充功能失效，疑似系统更新相关问题</div>
                            </div>
                            <div class="monitor-time">5分钟前</div>
                        </div>
                        <div class="monitor-item">
                            <div class="monitor-status medium"></div>
                            <div class="monitor-text">
                                <div><strong>客服响应时间延长</strong></div>
                                <div>平均响应时间较昨日增加15秒，建议关注客服工作负荷</div>
                            </div>
                            <div class="monitor-time">8分钟前</div>
                        </div>
                        <div class="monitor-item">
                            <div class="monitor-status low"></div>
                            <div class="monitor-text">
                                <div><strong>新功能反馈积极</strong></div>
                                <div>用户对新发布的相机夜景模式反馈良好，满意度达4.8分</div>
                            </div>
                            <div class="monitor-time">12分钟前</div>
                        </div>
                        <div class="monitor-item">
                            <div class="monitor-status medium"></div>
                            <div class="monitor-text">
                                <div><strong>售后服务咨询增加</strong></div>
                                <div>保修相关咨询较上周增长18%，建议增加FAQ说明</div>
                            </div>
                            <div class="monitor-time">15分钟前</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 趋势分析 -->
        <div class="dashboard-card trend-analysis">
            <div class="card-header">
                <div class="card-title">
                    📈 满意度趋势分析
                </div>
                <div class="card-actions">
                    <button class="action-btn">📊 更多指标</button>
                    <button class="action-btn">📋 生成报表</button>
                </div>
            </div>
            <div class="card-content">
                <div class="trend-chart">
                    <div class="trend-line"></div>
                    <div class="trend-points">
                        <div class="trend-point">
                            <div class="trend-value">4.2</div>
                        </div>
                        <div class="trend-point">
                            <div class="trend-value">4.3</div>
                        </div>
                        <div class="trend-point">
                            <div class="trend-value">4.1</div>
                        </div>
                        <div class="trend-point">
                            <div class="trend-value">4.5</div>
                        </div>
                        <div class="trend-point">
                            <div class="trend-value">4.6</div>
                        </div>
                        <div class="trend-point">
                            <div class="trend-value">4.4</div>
                        </div>
                        <div class="trend-point">
                            <div class="trend-value">4.6</div>
                        </div>
                    </div>
                    <div class="trend-labels">
                        <span>周一</span>
                        <span>周二</span>
                        <span>周三</span>
                        <span>周四</span>
                        <span>周五</span>
                        <span>周六</span>
                        <span>周日</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI洞察和快速操作 -->
        <div class="dashboard-grid">
            <!-- AI洞察 -->
            <div class="dashboard-card ai-insights">
                <div class="card-header">
                    <div class="card-title">
                        🤖 AI智能洞察
                    </div>
                    <div class="card-actions">
                        <button class="action-btn" style="border-color: rgba(255,255,255,0.3); color: rgba(255,255,255,0.8);">🔄 刷新</button>
                    </div>
                </div>
                <div class="card-content">
                    <div class="insight-item">
                        <div class="insight-type">🎯 关键发现</div>
                        <div class="insight-content">
                            电池续航问题在旗舰机型X1中集中爆发，建议立即启动技术排查并发布临时解决方案。
                        </div>
                        <div class="insight-confidence">置信度: 95%</div>
                    </div>
                    <div class="insight-item">
                        <div class="insight-type">📊 趋势预测</div>
                        <div class="insight-content">
                            基于历史数据分析，预计本周客户满意度将维持在4.5-4.7之间，整体表现良好。
                        </div>
                        <div class="insight-confidence">置信度: 87%</div>
                    </div>
                    <div class="insight-item">
                        <div class="insight-type">💡 优化建议</div>
                        <div class="insight-content">
                            建议在产品页面增加电池使用技巧说明，可有效减少15%的续航相关咨询。
                        </div>
                        <div class="insight-confidence">置信度: 82%</div>
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div style="display: flex; flex-direction: column; gap: 16px;">
                <div class="action-card" onclick="generateReport()">
                    <div class="action-icon">📋</div>
                    <div class="action-title">生成周报</div>
                    <div class="action-desc">自动生成本周VOC分析报告</div>
                </div>
                <div class="action-card" onclick="createAlert()">
                    <div class="action-icon">🔔</div>
                    <div class="action-title">设置告警</div>
                    <div class="action-desc">配置智能监控告警规则</div>
                </div>
                <div class="action-card" onclick="exportData()">
                    <div class="action-icon">📤</div>
                    <div class="action-title">导出数据</div>
                    <div class="action-desc">导出分析数据和图表</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 实时数据更新
        function updateRealtimeData() {
            // 模拟实时数据更新
            const statValues = document.querySelectorAll('.stat-value');
            statValues.forEach(stat => {
                const currentValue = parseFloat(stat.textContent.replace(/[^\d.]/g, ''));
                const change = (Math.random() - 0.5) * 0.1;
                const newValue = Math.max(0, currentValue + change);
                
                if (stat.textContent.includes('/')) {
                    stat.innerHTML = newValue.toFixed(1) + '<span style="font-size: 16px;">/5.0</span>';
                } else if (stat.textContent.includes('%')) {
                    stat.innerHTML = newValue.toFixed(1) + '<span style="font-size: 16px;">%</span>';
                } else {
                    stat.textContent = Math.round(newValue).toLocaleString();
                }
            });
        }

        // 监控标签切换
        document.querySelectorAll('.monitor-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.monitor-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                // 模拟加载不同类型的监控数据
                const content = document.querySelector('.monitor-content');
                content.style.opacity = '0.5';
                setTimeout(() => {
                    content.style.opacity = '1';
                }, 300);
            });
        });

        // 快速操作函数
        function generateReport() {
            alert('📋 正在生成VOC周报...\n\n包含内容:\n• 客户满意度分析\n• 问题热点识别\n• 改进建议\n• 趋势预测\n\n预计3分钟后完成');
        }

        function createAlert() {
            alert('🔔 告警规则配置\n\n可设置以下告警:\n• 满意度突然下降\n• 问题反馈激增\n• 关键词异常提及\n• 负面情绪超阈值\n\n点击确定进入配置页面');
        }

        function exportData() {
            alert('📤 数据导出选项\n\n可导出格式:\n• Excel报表\n• PDF报告\n• 原始数据CSV\n• 图表PNG/SVG\n\n请选择需要的数据范围和格式');
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 统计卡片动画
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // 启动实时数据更新
            setInterval(updateRealtimeData, 30000);
            
            // 模拟新的监控项目添加
            setInterval(() => {
                const monitorContent = document.querySelector('.monitor-content');
                const items = monitorContent.querySelectorAll('.monitor-item');
                if (items.length > 0) {
                    // 更新第一个项目的时间
                    const firstTime = items[0].querySelector('.monitor-time');
                    const minutes = Math.floor(Math.random() * 5) + 1;
                    firstTime.textContent = `${minutes}分钟前`;
                }
            }, 60000);
        });

        // 筛选器变化处理
        document.querySelectorAll('.filter-select').forEach(select => {
            select.addEventListener('change', function() {
                // 模拟数据重新加载
                const cards = document.querySelectorAll('.dashboard-card');
                cards.forEach(card => {
                    card.style.opacity = '0.7';
                    setTimeout(() => {
                        card.style.opacity = '1';
                    }, 500);
                });
            });
        });

        // 行业数据映射
        const industryData = {
            telecom: {
                name: '📱 手机通讯行业',
                totalFeedback: '15,847',
                satisfaction: '4.6',
                resolutionRate: '89.2',
                aiAccuracy: '96.8',
                sentimentScore: '4.6',
                positiveSentiment: 65,
                neutralSentiment: 25,
                negativeSentiment: 10,
                productFilters: ['旗舰手机', '中端手机', '入门手机'],
                recentIssues: [
                    { level: 'high', title: '电池续航异常', desc: '多名用户反馈快充功能失效，疑似系统更新相关问题', time: '5分钟前' },
                    { level: 'medium', title: '客服响应时间延长', desc: '平均响应时间较昨日增加15秒，建议关注客服工作负荷', time: '8分钟前' },
                    { level: 'low', title: '新功能反馈积极', desc: '用户对新发布的相机夜景模式反馈良好，满意度达4.8分', time: '12分钟前' }
                ],
                aiInsights: [
                    { type: '🎯 关键发现', content: '电池续航问题在旗舰机型X1中集中爆发，建议立即启动技术排查并发布临时解决方案。', confidence: '置信度: 94%' },
                    { type: '📈 趋势预测', content: '基于近期反馈趋势，预计相机夜景功能将成为用户满意度主要驱动因素。', confidence: '置信度: 87%' },
                    { type: '🔍 深度分析', content: '客服咨询量在工作日下午达到峰值，建议调整人员排班以优化响应效率。', confidence: '置信度: 91%' }
                ]
            },
            beauty: {
                name: '💄 美妆护肤行业',
                totalFeedback: '24,356',
                satisfaction: '4.7',
                resolutionRate: '92.8',
                aiAccuracy: '95.2',
                sentimentScore: '4.7',
                positiveSentiment: 72,
                neutralSentiment: 20,
                negativeSentiment: 8,
                productFilters: ['护肤产品', '彩妆产品', '香氛产品'],
                recentIssues: [
                    { level: 'medium', title: '季节性过敏反应', desc: '春季敏感肌用户反馈增多，建议推广温和型产品线', time: '3分钟前' },
                    { level: 'low', title: '新品口红色号好评', desc: '新推出的玫瑰金系列口红获得用户一致好评，复购率达85%', time: '10分钟前' },
                    { level: 'medium', title: '包装设计建议', desc: '用户建议改进粉底液包装设计，提升使用便利性', time: '18分钟前' }
                ],
                aiInsights: [
                    { type: '🎯 关键发现', content: '敏感肌用户群体快速增长，温和型产品需求上升32%，建议加大相关产品线投入。', confidence: '置信度: 96%' },
                    { type: '📈 趋势预测', content: '基于社交媒体数据分析，预计"自然妆感"将成为下季度主流趋势。', confidence: '置信度: 89%' },
                    { type: '🔍 深度分析', content: '年轻用户更偏好环保包装，建议推出可持续包装产品线。', confidence: '置信度: 93%' }
                ]
            },
            retail: {
                name: '🛍️ 零售电商行业',
                totalFeedback: '68,924',
                satisfaction: '4.5',
                resolutionRate: '88.6',
                aiAccuracy: '94.1',
                sentimentScore: '4.5',
                positiveSentiment: 62,
                neutralSentiment: 28,
                negativeSentiment: 10,
                productFilters: ['服装鞋帽', '家居用品', '数码电器'],
                recentIssues: [
                    { level: 'high', title: '物流配送延迟', desc: '华南地区配送延迟严重，多个订单超出预期配送时间', time: '2分钟前' },
                    { level: 'medium', title: '退换货流程优化', desc: '用户反馈退货流程复杂，建议简化操作步骤', time: '15分钟前' },
                    { level: 'low', title: '新品销售火爆', desc: '春季新品服装系列销售超预期，用户评价积极', time: '25分钟前' }
                ],
                aiInsights: [
                    { type: '🎯 关键发现', content: '物流配送是影响用户满意度的最关键因素，建议优化配送网络布局。', confidence: '置信度: 97%' },
                    { type: '📈 趋势预测', content: '基于购买行为分析，预计个性化推荐将显著提升转化率。', confidence: '置信度: 85%' },
                    { type: '🔍 深度分析', content: '用户对可持续发展相关产品的关注度提升45%，是新的增长点。', confidence: '置信度: 92%' }
                ]
            },
            automotive: {
                name: '🚗 汽车制造行业',
                totalFeedback: '12,458',
                satisfaction: '4.6',
                resolutionRate: '91.3',
                aiAccuracy: '95.8',
                sentimentScore: '4.6',
                positiveSentiment: 68,
                neutralSentiment: 22,
                negativeSentiment: 10,
                productFilters: ['轿车', 'SUV', '新能源车'],
                recentIssues: [
                    { level: 'medium', title: '充电桩兼容性', desc: '部分用户反馈第三方充电桩兼容性问题，影响使用体验', time: '6分钟前' },
                    { level: 'low', title: '智能驾驶功能好评', desc: '新一代智能驾驶辅助系统获得用户高度认可', time: '20分钟前' },
                    { level: 'medium', title: '售后服务时间', desc: '用户希望延长售后服务时间，提供更灵活的预约方式', time: '30分钟前' }
                ],
                aiInsights: [
                    { type: '🎯 关键发现', content: '新能源车用户对充电体验最为关注，充电便利性直接影响品牌忠诚度。', confidence: '置信度: 95%' },
                    { type: '📈 趋势预测', content: '智能化配置将成为用户购车决策的重要因素，预计需求增长40%。', confidence: '置信度: 88%' },
                    { type: '🔍 深度分析', content: '年轻用户群体更重视车载娱乐系统和互联功能体验。', confidence: '置信度: 90%' }
                ]
            }
        };

        // 当前行业
        let currentIndustry = 'telecom';

        // 页面初始化时检查URL参数
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const industryParam = urlParams.get('industry');
            if (industryParam && industryData[industryParam]) {
                currentIndustry = industryParam;
                updateDashboardData();
            }
        });

        // 更新仪表板数据
        function updateDashboardData() {
            const data = industryData[currentIndustry];
            if (!data) return;

            // 更新行业选择器
            document.querySelector('.industry-selector').innerHTML = data.name + ' ▼';

            // 更新关键指标
            const statCards = document.querySelectorAll('.stat-card');
            if (statCards[0]) statCards[0].querySelector('.stat-value').textContent = data.totalFeedback;
            if (statCards[1]) statCards[1].querySelector('.stat-value').innerHTML = data.satisfaction + '<span style="font-size: 16px;">/5.0</span>';
            if (statCards[2]) statCards[2].querySelector('.stat-value').innerHTML = data.resolutionRate + '<span style="font-size: 16px;">%</span>';
            if (statCards[3]) statCards[3].querySelector('.stat-value').innerHTML = data.aiAccuracy + '<span style="font-size: 16px;">%</span>';

            // 更新情感分析
            document.querySelector('.sentiment-score').textContent = data.sentimentScore;
            const legendItems = document.querySelectorAll('.legend-item');
            if (legendItems[0]) legendItems[0].querySelector('.legend-value').textContent = data.positiveSentiment + '%';
            if (legendItems[1]) legendItems[1].querySelector('.legend-value').textContent = data.neutralSentiment + '%';
            if (legendItems[2]) legendItems[2].querySelector('.legend-value').textContent = data.negativeSentiment + '%';

            // 更新产品筛选器
            const productFilterSelects = document.querySelectorAll('.filter-select');
            if (productFilterSelects[2]) {
                productFilterSelects[2].innerHTML = '<option>全部产品</option>' + 
                    data.productFilters.map(filter => `<option>${filter}</option>`).join('');
            }

            // 更新实时问题监控
            const monitorItems = document.querySelectorAll('.monitor-item');
            data.recentIssues.forEach((issue, index) => {
                if (monitorItems[index]) {
                    const statusElement = monitorItems[index].querySelector('.monitor-status');
                    statusElement.className = `monitor-status ${issue.level}`;
                    
                    monitorItems[index].querySelector('.monitor-text').innerHTML = 
                        `<div><strong>${issue.title}</strong></div><div>${issue.desc}</div>`;
                    monitorItems[index].querySelector('.monitor-time').textContent = issue.time;
                }
            });

            // 更新AI洞察
            const insightItems = document.querySelectorAll('.insight-item');
            data.aiInsights.forEach((insight, index) => {
                if (insightItems[index]) {
                    insightItems[index].querySelector('.insight-type').textContent = insight.type;
                    insightItems[index].querySelector('.insight-content').textContent = insight.content;
                    insightItems[index].querySelector('.insight-confidence').textContent = insight.confidence;
                }
            });

            // 添加切换动画效果
            document.body.style.opacity = '0.8';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 300);
        }

        // 导航函数
        function goToHome() {
            window.location.href = '../01-系统入口/登录页面原型.html';
        }

        function goToDashboard() {
            // 当前页面，不需要跳转
        }

        function goToReports() {
            window.location.href = `报表分析页面原型.html?industry=${currentIndustry}`;
        }

        function goToInsights() {
            window.location.href = `深度洞察页面原型.html?industry=${currentIndustry}`;
        }

        function goToSettings() {
            window.location.href = '../04-系统管理/系统管理页面原型.html';
        }

        function changeIndustry() {
            const availableIndustries = Object.keys(industryData).map(key => industryData[key].name).join('\n• ');
            if (confirm(`是否切换到其他行业？\n\n当前已配置的行业:\n• ${availableIndustries}`)) {
                window.location.href = '../02-配置流程/行业选择页面原型.html';
            }
        }

        function openUserMenu() {
            alert('👤 用户菜单\n\n• 个人资料\n• 修改密码\n• 系统设置\n• 退出登录');
        }

        // 响应式导航处理
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 768) {
                // 移动端适配逻辑
                console.log('切换到移动端视图');
            }
        });
    </script>
</body>
</html> 