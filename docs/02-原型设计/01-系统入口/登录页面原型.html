<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VOC客户声音分析系统 - 登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        /* 背景动画效果 */
        .bg-animation {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.1;
            z-index: 0;
        }

        .floating-shape {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .floating-shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .floating-shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            padding: 0;
            max-width: 900px;
            width: 100%;
            margin: 20px;
            display: flex;
            overflow: hidden;
            position: relative;
            z-index: 1;
        }

        .login-left {
            flex: 1;
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
            padding: 60px 40px;
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
        }

        .login-left::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20px;
            width: 100px;
            height: 200%;
            background: rgba(255, 255, 255, 0.1);
            transform: rotate(15deg);
        }

        .brand-logo {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .brand-title {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 16px;
            line-height: 1.2;
        }

        .brand-subtitle {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 40px;
            line-height: 1.5;
        }

        .feature-list {
            list-style: none;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            font-size: 16px;
            opacity: 0.9;
        }

        .feature-icon {
            margin-right: 12px;
            font-size: 18px;
        }

        .login-right {
            flex: 1;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .login-title {
            font-size: 28px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
        }

        .login-desc {
            color: #8c8c8c;
            font-size: 16px;
        }

        .login-form {
            width: 100%;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #262626;
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 14px 16px;
            border: 2px solid #e8e8e8;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #fafafa;
        }

        .form-input:focus {
            outline: none;
            border-color: #1890ff;
            background: white;
            box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
        }

        .form-input.error {
            border-color: #ff4d4f;
            background: #fff2f0;
        }

        .form-input.success {
            border-color: #52c41a;
            background: #f6ffed;
        }

        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #595959;
            font-size: 14px;
        }

        .forgot-password {
            color: #1890ff;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.2s;
        }

        .forgot-password:hover {
            color: #40a9ff;
        }

        .login-button {
            width: 100%;
            padding: 16px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .login-button:hover {
            background: #40a9ff;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(24, 144, 255, 0.3);
        }

        .login-button:active {
            transform: translateY(0);
        }

        .login-button.loading {
            background: #8c8c8c;
            cursor: not-allowed;
        }

        .login-divider {
            text-align: center;
            margin: 32px 0;
            position: relative;
            color: #8c8c8c;
            font-size: 14px;
        }

        .login-divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e8e8e8;
            z-index: 1;
        }

        .login-divider span {
            background: white;
            padding: 0 16px;
            position: relative;
            z-index: 2;
        }

        .demo-accounts {
            background: #f0f9ff;
            border: 1px solid #91d5ff;
            border-radius: 8px;
            padding: 16px;
            margin-top: 24px;
        }

        .demo-title {
            font-size: 14px;
            font-weight: 600;
            color: #1890ff;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .demo-account {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            background: white;
            border-radius: 4px;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .demo-account:last-child {
            margin-bottom: 0;
        }

        .demo-role {
            color: #8c8c8c;
        }

        .demo-login-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .demo-login-btn:hover {
            background: #40a9ff;
        }

        .error-message {
            color: #ff4d4f;
            font-size: 14px;
            margin-top: 8px;
            display: none;
        }

        .success-message {
            color: #52c41a;
            font-size: 14px;
            margin-top: 8px;
            display: none;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .login-container {
                flex-direction: column;
                margin: 10px;
                max-width: none;
            }
            
            .login-left {
                padding: 40px 30px;
            }
            
            .login-right {
                padding: 40px 30px;
            }
            
            .brand-title {
                font-size: 24px;
            }
            
            .login-title {
                font-size: 24px;
            }
        }

        /* 加载动画 */
        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: none;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 背景动画 -->
    <div class="bg-animation">
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
    </div>

    <div class="login-container">
        <!-- 左侧品牌介绍 -->
        <div class="login-left">
            <div class="brand-logo">🎯</div>
            <div class="brand-title">VOC客户声音分析系统</div>
            <div class="brand-subtitle">AI驱动的多行业客户反馈智能分析平台</div>
            
            <ul class="feature-list">
                <li class="feature-item">
                    <span class="feature-icon">🤖</span>
                    智能情感分析与问题识别
                </li>
                <li class="feature-item">
                    <span class="feature-icon">📊</span>
                    实时数据监控与可视化
                </li>
                <li class="feature-item">
                    <span class="feature-icon">🎯</span>
                    多行业配置与定制化
                </li>
                <li class="feature-item">
                    <span class="feature-icon">⚡</span>
                    自动化报表与洞察生成
                </li>
            </ul>
        </div>

        <!-- 右侧登录表单 -->
        <div class="login-right">
            <div class="login-header">
                <div class="login-title">欢迎登录</div>
                <div class="login-desc">登录您的账户以访问VOC分析系统</div>
            </div>

            <form class="login-form" onsubmit="handleLogin(event)">
                <div class="form-group">
                    <label class="form-label">用户名或邮箱</label>
                    <input type="text" class="form-input" id="username" placeholder="请输入用户名或邮箱" required>
                    <div class="error-message" id="username-error">请输入正确的用户名或邮箱</div>
                </div>

                <div class="form-group">
                    <label class="form-label">密码</label>
                    <input type="password" class="form-input" id="password" placeholder="请输入密码" required>
                    <div class="error-message" id="password-error">密码不能为空</div>
                </div>

                <div class="form-options">
                    <label class="remember-me">
                        <input type="checkbox" id="remember">
                        记住我
                    </label>
                    <a href="#" class="forgot-password" onclick="forgotPassword()">忘记密码？</a>
                </div>

                <button type="submit" class="login-button" id="login-btn">
                    <div class="loading-spinner" id="loading-spinner"></div>
                    登录
                </button>
            </form>

            <div class="login-divider">
                <span>或使用演示账户登录</span>
            </div>

            <!-- 演示账户 -->
            <div class="demo-accounts">
                <div class="demo-title">
                    🚀 快速体验账户
                </div>
                <div class="demo-account">
                    <div>
                        <strong>admin</strong> / 123456
                        <div class="demo-role">系统管理员</div>
                    </div>
                    <button class="demo-login-btn" onclick="demoLogin('admin', '系统管理员')">登录</button>
                </div>
                <div class="demo-account">
                    <div>
                        <strong>analyst</strong> / 123456
                        <div class="demo-role">数据分析师</div>
                    </div>
                    <button class="demo-login-btn" onclick="demoLogin('analyst', '数据分析师')">登录</button>
                </div>
                <div class="demo-account">
                    <div>
                        <strong>user</strong> / 123456
                        <div class="demo-role">普通用户</div>
                    </div>
                    <button class="demo-login-btn" onclick="demoLogin('user', '普通用户')">登录</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 表单验证
        function validateForm() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            let isValid = true;

            // 清除之前的错误状态
            document.querySelectorAll('.form-input').forEach(input => {
                input.classList.remove('error', 'success');
            });
            document.querySelectorAll('.error-message').forEach(msg => {
                msg.style.display = 'none';
            });

            // 验证用户名
            if (!username || username.length < 3) {
                document.getElementById('username').classList.add('error');
                document.getElementById('username-error').style.display = 'block';
                isValid = false;
            } else {
                document.getElementById('username').classList.add('success');
            }

            // 验证密码
            if (!password || password.length < 6) {
                document.getElementById('password').classList.add('error');
                document.getElementById('password-error').style.display = 'block';
                isValid = false;
            } else {
                document.getElementById('password').classList.add('success');
            }

            return isValid;
        }

        // 处理登录
        function handleLogin(event) {
            event.preventDefault();
            
            if (!validateForm()) {
                return;
            }

            const loginBtn = document.getElementById('login-btn');
            const spinner = document.getElementById('loading-spinner');
            
            // 显示加载状态
            loginBtn.classList.add('loading');
            loginBtn.disabled = true;
            spinner.style.display = 'inline-block';
            loginBtn.innerHTML = '<div class="loading-spinner"></div>登录中...';

            // 模拟登录过程
            setTimeout(() => {
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                
                // 模拟验证逻辑
                if ((username === 'admin' || username === 'analyst' || username === 'user') && password === '123456') {
                    // 登录成功
                    loginBtn.innerHTML = '✅ 登录成功';
                    loginBtn.style.background = '#52c41a';
                    
                    setTimeout(() => {
                        // 根据用户类型跳转到不同页面
                        if (username === 'admin') {
                            window.location.href = '../04-系统管理/系统管理页面原型.html';
                        } else {
                            window.location.href = '../02-配置流程/行业选择页面原型.html';
                        }
                    }, 1000);
                } else {
                    // 登录失败
                    loginBtn.innerHTML = '❌ 登录失败';
                    loginBtn.style.background = '#ff4d4f';
                    
                    setTimeout(() => {
                        loginBtn.classList.remove('loading');
                        loginBtn.disabled = false;
                        loginBtn.innerHTML = '登录';
                        loginBtn.style.background = '#1890ff';
                        
                        // 显示错误信息
                        alert('❌ 登录失败\n\n用户名或密码错误，请检查后重试。\n\n💡 提示：您可以使用右侧的演示账户快速登录体验');
                    }, 1500);
                }
            }, 2000);
        }

        // 演示账户登录
        function demoLogin(username, role) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = '123456';
            
            // 触发登录
            setTimeout(() => {
                alert(`🎉 正在使用 ${role} 账户登录...\n\n用户名: ${username}\n角色: ${role}\n\n系统将为您展示对应权限的功能界面。`);
                handleLogin({ preventDefault: () => {} });
            }, 500);
        }

        // 忘记密码
        function forgotPassword() {
            alert('🔐 密码重置\n\n在实际系统中，您可以：\n\n📧 通过邮箱重置密码\n📱 通过手机验证码重置\n👨‍💼 联系系统管理员重置\n\n📞 技术支持: ************\n📧 <EMAIL>');
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 登录容器淡入动画
            const container = document.querySelector('.login-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                container.style.transition = 'all 0.8s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 200);

            // 自动聚焦到用户名输入框
            setTimeout(() => {
                document.getElementById('username').focus();
            }, 1000);
        });

        // 实时输入验证
        document.getElementById('username').addEventListener('input', function() {
            if (this.value.length >= 3) {
                this.classList.remove('error');
                this.classList.add('success');
                document.getElementById('username-error').style.display = 'none';
            }
        });

        document.getElementById('password').addEventListener('input', function() {
            if (this.value.length >= 6) {
                this.classList.remove('error');
                this.classList.add('success');
                document.getElementById('password-error').style.display = 'none';
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', function(event) {
            // Ctrl+Enter 快速登录
            if (event.ctrlKey && event.key === 'Enter') {
                handleLogin({ preventDefault: () => {} });
            }
        });
    </script>
</body>
</html> 