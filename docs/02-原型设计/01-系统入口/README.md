# 01-系统入口

## 📝 模块说明

系统入口模块包含用户首次接触系统的关键页面，负责用户认证、欢迎引导等功能。

## 📁 文件列表

### 🚪 登录页面原型.html
- **功能**：系统登录认证入口
- **特色**：
  - 现代化设计，支持多设备响应式
  - 表单验证和错误提示
  - 演示账户快速登录
  - 加载动画和交互效果
- **演示账户**：
  - `admin / 123456` - 系统管理员（完整权限）
  - `analyst / 123456` - 数据分析师（分析功能）
  - `user / 123456` - 普通用户（基础功能）

## 🔗 页面流转

```mermaid
graph TD
    A[登录页面] --> B{登录验证}
    B -->|成功| C[跳转到行业选择]
    B -->|失败| D[显示错误提示]
    D --> A
    C --> E[../02-配置流程/行业选择页面原型.html]
```

## ⭐ 核心功能

1. **用户认证**
   - 用户名/密码验证
   - 记住登录状态
   - 忘记密码处理

2. **演示体验**
   - 快速演示账户
   - 角色权限说明
   - 功能预览

3. **用户引导**
   - 首次使用引导
   - 系统介绍说明
   - 帮助文档链接

## 🎨 设计特点

- **品牌展示**：突出VOC系统品牌特色
- **信任建立**：专业可靠的视觉设计
- **易用性**：简单直观的操作流程
- **多设备适配**：完美的响应式体验

## 🚀 使用建议

1. **产品演示**：作为演示的起始页面
2. **用户测试**：观察用户的首次体验
3. **培训材料**：用于用户培训的标准入口 